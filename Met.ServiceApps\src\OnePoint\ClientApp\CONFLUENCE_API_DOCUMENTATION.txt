h1. OnePoint API Documentation

{info:title=API Overview}
This document provides comprehensive documentation for the OnePoint API endpoints used in the Milwaukee Tool service application.

*Base URL:* https://onepoint-test.milwaukeetool.com/api
*API Version:* v1.0
*Last Updated:* January 2024
{info}

{toc:printable=true|style=square|maxLevel=3|indent=20px|minLevel=1|class=bigpink|exclude=[1//2]|type=list|outline=clear|include=.*}

h2. Authentication

All API endpoints require authentication using Bearer tokens:

{code:language=http}
Authorization: Bearer {your-jwt-token}
{code}

h3. Required Headers

||Header||Value||Description||
|Authorization|Bearer {token}|JWT authentication token|
|Content-Type|application/json|For POST/PUT requests|
|Accept|application/json|Expected response format|

h2. Rate Limiting

{note}
*Rate Limit:* 1000 requests per hour per API key
*Burst Limit:* 100 requests per minute

Rate limit information is returned in response headers:
* X-RateLimit-Limit: Total requests allowed per hour
* X-RateLimit-Remaining: Requests remaining in current window
* X-RateLimit-Reset: Time when rate limit resets
{note}

h2. Pricing Endpoints

h3. Get Service Pricing

Retrieves pricing information for service items based on specified criteria.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/Pricing/servicepricing
{panel}

h4. Parameters

||Parameter||Type||Required||Description||
|PriceType|integer|✓|Type of pricing (1 = Service, 2 = Retail, etc.)|
|CustomerType|integer|✓|Customer classification (1 = Retail, 2 = Commercial, etc.)|
|Currency|integer|✓|Currency code (1 = USD, 2 = CAD, etc.)|
|SvcCategory|string|✗|Service category filter|
|ItemCodes|array|✓|Array of item codes to get pricing for|

h4. Example Request

{code:language=http}
GET /api/Pricing/servicepricing?PriceType=1&CustomerType=2&Currency=1&SvcCategory=&ItemCodes%5B0%5D=271120
{code}

h4. Response Format

{code:language=json}
{
  "items": [
    {
      "itemCode": "271120",
      "price": 45.99,
      "currency": "USD",
      "priceType": 1,
      "customerType": 2,
      "effectiveDate": "2024-01-01T00:00:00Z",
      "expirationDate": "2024-12-31T23:59:59Z"
    }
  ],
  "success": true,
  "message": null
}
{code}

h2. Warranty Endpoints

h3. Check Warranty Status

Validates warranty status for a specific product SKU.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/warranty/checkWarranty
{panel}

h4. Parameters

||Parameter||Type||Required||Description||
|skuAlias|string|✓|Product SKU alias to check warranty for|

h4. Example Request

{code:language=http}
GET /api/warranty/checkWarranty?skuAlias=2711-20
{code}

h4. Response Format

{code:language=json}
{
  "skuAlias": "2711-20",
  "isUnderWarranty": true,
  "warrantyType": "Limited",
  "warrantyPeriod": "5 years",
  "purchaseDate": "2023-06-15T00:00:00Z",
  "expirationDate": "2028-06-15T00:00:00Z",
  "warrantyDescription": "5 year limited warranty",
  "success": true
}
{code}

h2. Product Endpoints

h3. Get Product Problems

Retrieves available problem categories for a specific product.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/products/{sku}/problems
{panel}

h4. Path Parameters

||Parameter||Type||Required||Description||
|sku|string|✓|Product SKU|

h4. Example Request

{code:language=http}
GET /api/products/271120/problems
{code}

h4. Response Format

{code:language=json}
{
  "problems": [
    {
      "id": 1,
      "code": "POWER",
      "description": "Power Issues",
      "category": "Electrical"
    },
    {
      "id": 2,
      "code": "MECHANICAL",
      "description": "Mechanical Problems",
      "category": "Hardware"
    }
  ],
  "success": true
}
{code}

h3. Get Product Inclusions

Retrieves included items/accessories for a specific product.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/products/{sku}/include
{panel}

h4. Path Parameters

||Parameter||Type||Required||Description||
|sku|string|✓|Product SKU|

h4. Example Request

{code:language=http}
GET /api/products/271120/include
{code}

h4. Response Format

{code:language=json}
{
  "inclusions": [
    {
      "id": 1,
      "sku": "271120-ACC1",
      "description": "Battery Charger",
      "quantity": 1,
      "isOptional": false
    },
    {
      "id": 2,
      "sku": "271120-ACC2", 
      "description": "Carrying Case",
      "quantity": 1,
      "isOptional": true
    }
  ],
  "success": true
}
{code}

h3. Get Replacement Reasons

Retrieves available replacement reason codes.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/ReplacementReason
{panel}

h4. Example Request

{code:language=http}
GET /api/ReplacementReason
{code}

h4. Response Format

{code:language=json}
{
  "reasons": [
    {
      "id": 1,
      "code": "DEFECTIVE",
      "description": "Defective Product",
      "category": "Quality"
    },
    {
      "id": 2,
      "code": "DAMAGED",
      "description": "Shipping Damage",
      "category": "Logistics"
    }
  ],
  "success": true
}
{code}

h2. Service Order Endpoints

h3. Calculate Service Order Estimate Pricing

Calculates pricing for a service order estimate.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* POST
*Endpoint:* /api/serviceOrderEstimate/{estimateId}/pricing
{panel}

h4. Path Parameters

||Parameter||Type||Required||Description||
|estimateId|string (GUID)|✓|Service order estimate ID|

h4. Request Body

{code:language=json}
{
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "serviceType": "repair"
    }
  ],
  "customerType": 2,
  "currency": 1,
  "taxExempt": false
}
{code}

h4. Example Request

{code:language=http}
POST /api/serviceOrderEstimate/b4a54aed-9e9b-429a-a087-ca267ed3e959/pricing
{code}

h3. Create Real-time Service Order Group

Creates a new real-time service order group.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* POST
*Endpoint:* /api/serviceOrders/groups/realtime
{panel}

h4. Request Body

{code:language=json}
{
  "customerId": "12345",
  "branchId": "BR001",
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "serviceType": "repair",
      "problemCodes": ["POWER"],
      "symptoms": ["No power"]
    }
  ],
  "priority": "normal"
}
{code}

h3. Calculate Service Order Charges

Calculates charges for service order items.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* POST
*Endpoint:* /api/serviceOrders/charges
{panel}

h4. Request Body

{code:language=json}
{
  "orderId": "SO123456",
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "laborHours": 2.5,
      "partsCost": 25.99
    }
  ],
  "customerType": 2,
  "discountPercent": 0
}
{code}

h3. Get Service Order Taxes

Retrieves tax calculations for service orders.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/serviceOrders/taxes
{panel}

h4. Parameters

||Parameter||Type||Required||Description||
|orderId|string|✓|Service order ID|
|zipCode|string|✗|Customer zip code for tax calculation|
|stateCode|string|✗|Customer state code|

h4. Example Request

{code:language=http}
GET /api/serviceOrders/taxes?orderId=SO123456&zipCode=53202&stateCode=WI
{code}

h2. Repair Details Endpoints

h3. Get Symptom Codes by Area

Retrieves symptom codes for a specific symptom area.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/repairDetails/symptomAreas/{areaId}/symptomCodes
{panel}

h4. Path Parameters

||Parameter||Type||Required||Description||
|areaId|integer|✓|Symptom area ID|

h4. Example Request

{code:language=http}
GET /api/repairDetails/symptomAreas/4/symptomCodes
{code}

h4. Response Format

{code:language=json}
{
  "symptomCodes": [
    {
      "id": 1,
      "code": "SYM001",
      "description": "Motor not starting",
      "areaId": 4,
      "severity": "high"
    },
    {
      "id": 2,
      "code": "SYM002",
      "description": "Intermittent operation",
      "areaId": 4,
      "severity": "medium"
    }
  ],
  "success": true
}
{code}

h2. Payment Endpoints

h3. Get Payment Terminals

Retrieves available payment terminals for processing transactions.

{panel:title=Endpoint Details|borderStyle=solid|borderColor=#ccc|titleBGColor=#f7f7f7|bgColor=#ffffff}
*Method:* GET
*Endpoint:* /api/Payments/terminals
{panel}

h4. Parameters

||Parameter||Type||Required||Description||
|branchId|string|✗|Filter by branch ID|
|status|string|✗|Filter by terminal status (active, inactive)|

h4. Example Request

{code:language=http}
GET /api/Payments/terminals?branchId=BR001&status=active
{code}

h4. Response Format

{code:language=json}
{
  "terminals": [
    {
      "id": "TERM001",
      "name": "Terminal 1 - Front Desk",
      "branchId": "BR001",
      "status": "active",
      "type": "credit_card",
      "capabilities": ["chip", "contactless", "magnetic_stripe"],
      "lastHeartbeat": "2024-01-15T10:30:00Z"
    }
  ],
  "success": true
}
{code}

h2. Common Response Formats

h3. Success Response

All successful API responses follow this general structure:

{code:language=json}
{
  "data": {}, // Response data (varies by endpoint)
  "success": true,
  "message": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
{code}

h3. Error Response

All error responses follow this structure:

{code:language=json}
{
  "success": false,
  "message": "Error description",
  "errorCode": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
{code}

h2. Error Handling

h3. HTTP Status Codes

||Status Code||Description||Common Scenarios||
|200|OK|Successful GET requests|
|201|Created|Successful POST requests that create resources|
|400|Bad Request|Invalid parameters, malformed JSON|
|401|Unauthorized|Missing or invalid authentication|
|403|Forbidden|Insufficient permissions|
|404|Not Found|Resource not found (invalid SKU, order ID, etc.)|
|422|Unprocessable Entity|Validation errors|
|500|Internal Server Error|Server-side errors|

h3. Common Error Codes

||Error Code||Description||Resolution||
|INVALID_SKU|Product SKU not found|Verify SKU exists in catalog|
|PRICING_UNAVAILABLE|Pricing not available for parameters|Check customer type and currency|
|WARRANTY_EXPIRED|Product warranty has expired|Inform customer of warranty status|
|INVALID_CUSTOMER_TYPE|Customer type not recognized|Use valid customer type codes|
|INSUFFICIENT_INVENTORY|Not enough inventory for request|Check available quantities|
|PAYMENT_TERMINAL_OFFLINE|Payment terminal unavailable|Use alternative terminal or retry|

h2. Data Types and Enums

h3. Price Types

||Value||Description||
|1|Service Pricing|
|2|Retail Pricing|
|3|Wholesale Pricing|

h3. Customer Types

||Value||Description||
|1|Retail Customer|
|2|Commercial Customer|
|3|Dealer|
|4|Internal|

h3. Currency Codes

||Value||Description||
|1|USD|
|2|CAD|
|3|EUR|

h3. Service Types

||Value||Description||
|repair|Standard repair service|
|replace|Product replacement|
|maintenance|Preventive maintenance|
|diagnostic|Diagnostic service only|

h2. SDK and Integration Examples

h3. TypeScript/JavaScript SDK Example

{code:language=typescript}
// Service for OnePoint API integration
class OnePointApiService {
  private baseUrl = 'https://onepoint-test.milwaukeetool.com/api';
  private authToken: string;

  constructor(authToken: string) {
    this.authToken = authToken;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Get service pricing
  async getServicePricing(params: {
    priceType: number;
    customerType: number;
    currency: number;
    itemCodes: string[];
    svcCategory?: string;
  }) {
    const queryParams = new URLSearchParams({
      PriceType: params.priceType.toString(),
      CustomerType: params.customerType.toString(),
      Currency: params.currency.toString(),
      SvcCategory: params.svcCategory || '',
    });

    params.itemCodes.forEach((code, index) => {
      queryParams.append(`ItemCodes[${index}]`, code);
    });

    return this.request(`/Pricing/servicepricing?${queryParams}`);
  }

  // Check warranty
  async checkWarranty(skuAlias: string) {
    return this.request(`/warranty/checkWarranty?skuAlias=${skuAlias}`);
  }

  // Get product problems
  async getProductProblems(sku: string) {
    return this.request(`/products/${sku}/problems`);
  }
}
{code}

h3. Usage Example

{code:language=typescript}
const apiService = new OnePointApiService('your-jwt-token');

// Get pricing for multiple items
const pricing = await apiService.getServicePricing({
  priceType: 1,
  customerType: 2,
  currency: 1,
  itemCodes: ['271120', '271121'],
});

// Check warranty status
const warranty = await apiService.checkWarranty('2711-20');

// Get product problems
const problems = await apiService.getProductProblems('271120');
{code}

h2. Testing and Development

{tip:title=Environment Information}
*Test Environment*
* Base URL: https://onepoint-test.milwaukeetool.com/api
* Test Data: Use test SKUs and customer IDs provided by Milwaukee Tool
* Rate Limits: Reduced rate limits in test environment

*Production Environment*
* Base URL: https://onepoint.milwaukeetool.com/api
* Authentication: Production JWT tokens required
* Monitoring: Full logging and monitoring enabled
{tip}

h2. Support and Contact

{warning:title=Support Information}
For API support and questions:
* *Technical Support:* Contact Milwaukee Tool IT Support
* *Documentation Updates:* Submit requests through internal channels
* *Bug Reports:* Use internal issue tracking system
{warning}

{info:title=Document Information}
*Last Updated:* January 2024
*API Version:* v1.0
*Document Version:* 1.0
{info}
