import { Button } from '@/design/molecules';
import { AccountSubTitle, SiteItem } from '@/features/account';
import { Location } from '@/features/branch';
import { Address, Customer, CustomerCard } from '@/features/customer';
import { Product, WarrantyDescriptionBadge } from '@/features/product';
import {
  SalesOrderCardHeader,
  SalesOrderChildRow,
  SalesOrderSearch
} from '@/features/salesOrder';
import {
  OrderListDisplayOrigin,
  ProductDataMap
} from '@/features/serviceOrder';
import { useAppDispatch } from '@/store/hooks';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Tooltip,
  Typography
} from '@mui/material';
import MuiChip from '@mui/material/Chip';
import { useTranslation } from 'next-export-i18n';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useMemo, useState } from 'react';

const ReturnNoticeBadge = ({ salesOrderId }: { salesOrderId: string }) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        mt: '4px'
      }}
    >
      <MuiChip
        label={t('features.salesOrder.returnOrder.returnNotice')}
        color="warning"
        data-testid={`returnNoticeBadge-${salesOrderId}`}
      />
    </Box>
  );
};

interface Props {
  salesOrder: SalesOrderSearch;
  productDataMap: ProductDataMap;
  branch?: Location;
  customer?: Customer | undefined;
  customerAddress?: Address;
  redirectToHomePage?: boolean;
  onToolCheck: (
    e: React.ChangeEvent<HTMLInputElement>,
    orderId: string
  ) => void;
  productsChecked?: {
    [x: string]: boolean;
  };
  displayOrigin?: OrderListDisplayOrigin;
  pageRedirect?: number;
  site?: SiteItem;
  onProcessSalesOrderClick?: (params: { orderId: string }) => void;
}

export const SalesOrderCard = (props: Props) => {
  const { t } = useTranslation();
  useAppDispatch();
  const defaultImage = '/logo-milwaukeetool-gray.svg';
  const [expanded, setExpanded] = useState(false);

  const router = useRouter();
  const handleViewDetailsClick = () => {
    router.push(`/salesOrder/${props.salesOrder.id}`);
  };
  const salesOrder = props.salesOrder;
  const product = props.productDataMap[salesOrder.items[0].sku];
  const getToolMessage = (value: number) =>
    `${t('features.order.toolsCounterText')} (${value})`;
  const hasMultipleItems = props.salesOrder.items.length > 1;
  const customerAccountAddress = useMemo(() => {
    const address = props.salesOrder.shipToMetadata;
    if (address) {
      return {
        addressLine1: address.addressLine1,
        city: address.city,
        state: address.state,
        countryCode: address.countryCode,
        postalCode: address.postalCode
      } as Address;
    }
  }, [props.salesOrder]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        backgroundColor: 'neutral.50',
        borderBottomColor: 'neutral.300'
      }}
    >
      <SalesOrderCardHeader
        salesOrderNumber={props.salesOrder.salesOrderNumber}
        createdAt={props.salesOrder.createdAt}
        translationLabel={
          props.salesOrder.orderType === 2
            ? 'features.salesOrder.returnOrder.orderIdentifier'
            : 'features.salesOrder.orderDetailsIdentifier'
        }
      />
      {props.customer && props.salesOrder.shipToMetadata && (
        <>
          <Box
            data-testid={'customer-generic-account-details'}
            sx={{
              display: 'flex',
              alignItems: 'center',
              px: 3,
              py: 2
            }}
          >
            <AccountSubTitle
              accountName={props.salesOrder.shipToMetadata.shipToSiteName ?? ''}
              siteNumber={props.salesOrder.accountNumber!}
              accountAddress={customerAccountAddress}
            />
          </Box>
          <CustomerCard
            customer={props.customer}
            showOnlyCustomerInfo
            containerSxProps={{
              borderBottom: '1px solid',
              borderBottomColor: 'neutral.200',
              backgroundColor: 'common.white'
            }}
          />
        </>
      )}
      {hasMultipleItems ? (
        <Accordion
          disableGutters={true}
          expanded={expanded}
          onChange={() => setExpanded(!expanded)}
          data-testid="accordion-testId"
          elevation={0}
          sx={{
            bgcolor: 'neutral.50',
            ['&:before']: {
              bgcolor: 'transparent'
            },
            '& .MuiAccordionDetails-root': {
              p: 0
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            sx={{
              backgroundColor: 'common.white',
              borderBottom: '1px solid',
              borderColor: 'neutral.200'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '20%'
              }}
            >
              <Typography
                variant="p2"
                color="secondary.900"
                sx={{ fontWeight: 'bold', p: 3 }}
                data-testid="toolsCounterText-testId"
              >
                {getToolMessage(
                  props.salesOrder.items.reduce(
                    (total, item) => total + (item.quantity || 1),
                    0
                  )
                )}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: '8px'
                }}
              >
                {salesOrder.items.map((item) => {
                  const product = props.productDataMap[item.sku];
                  return (
                    <Image
                      key={item.sku}
                      src={product?.image ?? defaultImage}
                      alt={t('common.logo')}
                      width={70}
                      height={70}
                      style={{ borderRadius: '4px' }}
                    />
                  );
                })}
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                justifyContent: 'center',
                flex: 1,
                width: '25%'
              }}
            >
              <MuiChip
                sx={{
                  p: '8px',
                  justifyContent: 'center',
                  width: 'fit-content',
                  borderRadius: '8px'
                }}
                label={
                  <Typography
                    variant="p2"
                    fontWeight={600}
                    color="neutral.700"
                    data-testid={`orderStatusChip-testId`}
                  >
                    {t('features.order.expandMultiToolOrder')}
                  </Typography>
                }
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-end',
                justifyContent: 'center',
                width: '26%'
              }}
            >
              {salesOrder.returns?.length > 0 && (
                <ReturnNoticeBadge salesOrderId={salesOrder.id} />
              )}
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexGrow: 1,
                justifyContent: 'flex-end'
              }}
            >
              <Button
                variant={'secondary'}
                sx={{
                  alignSelf: 'center',
                  padding: '6px 8px'
                }}
                onClick={handleViewDetailsClick}
                data-testid={`buttonViewOrder-${props.salesOrder.id}-testId`}
              >
                {t('features.order.viewDetails')}
              </Button>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {salesOrder.items.map((item) => (
              <SalesOrderChildRow
                key={item.sku}
                salesOrder={{ ...salesOrder, items: [item] }}
                productDataMap={props.productDataMap}
              />
            ))}
          </AccordionDetails>
        </Accordion>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            padding: '16px',
            borderBottom: '1px solid',
            borderColor: 'neutral.200',
            gap: '16px',
            backgroundColor: 'common.white'
          }}
        >
          <Box
            sx={{
              alignItems: 'center',
              display: 'flex',
              flexDirection: 'column',
              width: '20%'
            }}
          >
            <Typography
              variant="p2"
              color="secondary.900"
              sx={{ fontWeight: 'bold' }}
              data-testid="toolsCounterText-testId"
            >
              {getToolMessage(props.salesOrder.items[0].quantity)}
            </Typography>
            <Image
              src={product?.image ?? defaultImage}
              alt={t('common.logo')}
              width={70}
              height={70}
              data-testid={`toolInfo-image-${salesOrder.id}-testId`}
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              width: '25%'
            }}
          >
            <Typography
              variant="p1"
              color="neutral.500"
              data-testid={`toolInfo-sku-${salesOrder.id}-testId`}
            >
              {salesOrder.items[0].sku}
            </Typography>
            <Tooltip title={product?.description}>
              <Typography
                variant="p1"
                color="neutral.800"
                fontWeight={600}
                noWrap={true}
                data-testid={`toolInfo-description-${salesOrder.id}-testId`}
                sx={{ textOverflow: 'ellipsis', maxWidth: '250px' }}
              >
                {product?.description}
              </Typography>
            </Tooltip>
          </Box>
          <Box
            sx={{
              alignSelf: 'flex-start',
              display: 'flex',
              flexDirection: 'column',
              gap: '8px'
            }}
          >
            <Typography
              variant="p2"
              color="secondary.900"
              sx={{ fontWeight: 'bold' }}
              data-testid={`toolInfo-warranty-${salesOrder.id}-testId`}
            >
              {t('features.product.warranty')}
            </Typography>
            {product?.warrantyDescription && (
              <WarrantyDescriptionBadge
                item={product as Product}
                typographyProps={{
                  variant: 'p1',
                  fontWeight: 600
                }}
                sx={{ width: 'fit-content' }}
              />
            )}
            {salesOrder.returns?.length > 0 && (
              <ReturnNoticeBadge salesOrderId={salesOrder.id} />
            )}
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexGrow: 1,
              justifyContent: 'flex-end'
            }}
          >
            <Button
              variant={'secondary'}
              sx={{
                alignSelf: 'center',
                padding: '6px 8px',
                mr: '4.25%'
              }}
              onClick={handleViewDetailsClick}
              data-testid={`buttonViewOrder-${salesOrder.id}-testId`}
            >
              {t('features.order.viewDetails')}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};
