'use client';

import { BREAKPOINTS, CloseIcon } from '@/design/atoms';
import { PageHeaderContainer } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import {
  SavedOrderListContainer,
  SavedOrdersHeader
} from '@/features/serviceOrder';
import { Box, IconButton, Theme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';

export default function SavedOrders() {
  const router = useRouter();
  const isDeskTopScreen = useMediaQuery((theme: Theme) =>
    theme.breakpoints.up(BREAKPOINTS.values.xl)
  );
  return (
    <>
      <PageHeaderContainer
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            padding: isDeskTopScreen ? '0px 24px' : '0px 56px'
          }}
        >
          <SavedOrdersHeader />
        </Box>
        <IconButton
          onClick={() => router.push('/')}
          data-testid="closeBtn-testId"
        >
          <CloseIcon />
        </IconButton>
      </PageHeaderContainer>
      <PageAreaBox
        pageAreaBoxName="customer"
        sx={{
          height: '100%',
          bgcolor: 'background.paper',
          padding: '0px 56px 0px 56px !important',
          mt: '88px'
        }}
      >
        <SavedOrderListContainer />
      </PageAreaBox>
    </>
  );
}
