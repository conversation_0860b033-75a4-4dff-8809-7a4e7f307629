'use client';

import { ArrowLeftIcon, CloseIcon } from '@/design/atoms';
import { PageHeaderContainer, Snackbar } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { useSearchCustomersQuery } from '@/features/customer';
import {
  SalesOrderDetailsContainer,
  useGetSalesOrderQuery,
  useGetSalesOrderReturnsQuery
} from '@/features/salesOrder';
import { Box, CircularProgress, IconButton, Typography } from '@mui/material';
import { skipToken } from '@reduxjs/toolkit/query';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface SalesOrderDetailsPageProps {
  params: {
    id: string;
  };
  searchParams: { newReturnOrder?: string };
}

export default function SalesOrderDetailsPage({
  params,
  searchParams
}: SalesOrderDetailsPageProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const handleGoBack = () => {
    router.back();
  };
  const { id } = params;

  const [showReturnSuccessToast, setShowReturnSuccess] = useState(
    searchParams?.newReturnOrder != null
  );

  const {
    data: response,
    isLoading: isSalesOrderLoading,
    isError: isSalesOrderError
  } = useGetSalesOrderQuery(id ?? '', { skip: !id });
  const salesOrder = response?.salesOrder;

  const { data: customerData, isFetching } = useSearchCustomersQuery(
    salesOrder?.customerId
      ? {
          ids: [salesOrder.customerId]
        }
      : skipToken
  );

  const {
    data: refundResponse,
    isLoading: isSalesOrderReturnsLoading,
    isError: isSalesOrderReturnsError
  } = useGetSalesOrderReturnsQuery(id ?? '', { skip: !id });

  if (isSalesOrderLoading || isSalesOrderReturnsLoading || isFetching) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (isSalesOrderError || isSalesOrderReturnsError || !response) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}
      >
        <Typography variant="h6">{t('common.pageNotFound')}</Typography>
      </Box>
    );
  }

  return (
    <>
      <Snackbar
        open={showReturnSuccessToast}
        message={t('features.order.return.returnCreatedSuccess', {
          returnOrderNumber: searchParams.newReturnOrder
        })}
        severity="success"
        handleClose={() => setShowReturnSuccess(false)}
      />
      <PageHeaderContainer
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box
          data-testid="goBackToCustomerDetails-testId"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <>
            <IconButton
              data-testid="orderRepairHistory-testId"
              onClick={handleGoBack}
            >
              <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
            </IconButton>
            <Typography variant="h5" color="neutral.800" textAlign="center">
              {t('features.salesOrder.salesOrderDetails')}
            </Typography>
          </>
        </Box>
        <IconButton data-testid="closeBtn-testId" onClick={handleGoBack}>
          <CloseIcon />
        </IconButton>
      </PageHeaderContainer>
      <PageAreaBox
        pageAreaBoxName="repairDetails"
        sx={{
          height: '100%',
          bgcolor: 'background.paper',
          mt: '88px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <SalesOrderDetailsContainer
          id={response.salesOrder.id}
          productDataMap={response.productDataMap}
          salesOrder={response.salesOrder}
          customer={customerData?.users?.[0]}
          returns={refundResponse}
        />
      </PageAreaBox>
    </>
  );
}
