import type { Meta, StoryObj } from '@storybook/react';
import { MainButton } from './MainButton';
import { SalesOrderIcon } from '@/design/atoms';

const meta: Meta<typeof MainButton> = {
  title: 'Features/Home/MainButton',
  component: MainButton,
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof MainButton>;

export const Playground: Story = {
  args: {
    description: 'Click Me!'
  }
};

export const Icon: Story = {
  render: () => {
    return (
      <MainButton
        description="Click Me!"
        icon={<SalesOrderIcon />}
        onButtonClick={() => {
          return;
        }}
        testId="testId"
      />
    );
  }
};
