'use client';

import { Arrow<PERSON>eftIcon, CloseIcon } from '@/design/atoms';
import { PageHeaderContainer, TextField } from '@/design/molecules';
import { PageAreaBox, Prompt } from '@/design/organisms';
import { Customer, CustomerCard } from '@/features/customer';
import { CurrencyType, PricingItem } from '@/features/pricing';
import { ReplacementItem, SelectedProduct } from '@/features/product';
import {
  GroupResponse,
  selectedServiceOrderState,
  ServiceOrderHeader,
  ServiceOrderPaymentDrawer,
  ServiceOrderProduct,
  useServiceOrderPaymentContext,
  WarrantyActionCode,
  WarrantyStatusFromRepairLines
} from '@/features/serviceOrder';
import { useAppSelector } from '@/store/hooks';
import { Box, Card, CardContent, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';

interface Props {
  groupId: string;
  orderId: string;
  serviceOrderNumber: string;
  customer: Customer;
  product: SelectedProduct;
  isRepairByReplacement: boolean;
  isOperationalReplacement: boolean;
  isOutOfWarranty: boolean;
  onBackClick: () => void;
  onNextClick: (skipUpdatingPickup?: boolean) => Promise<void>;
  systemOrigin: number;
  submittedDate?: string;
  pricingData?: PricingItem | undefined;
  group: GroupResponse | undefined;
  currencyType: CurrencyType;
  onInviteToPayCompleted?: () => Promise<void> | void;
  showCreditCardAuthorized?: boolean;
  inviteToPayId?: string;
  creditCardId?: string;
}

export const PickupConfirmation = (props: Props) => {
  const { t } = useTranslation();
  const { showCreditCardAuthorized } = props;
  const router = useRouter();
  const [pickupParty, setPickupParty] = useState('');
  const [replacementSerialNumber, setReplacementSerialNumber] =
    React.useState<string>();
  const [openPrompt, setOpenPrompt] = React.useState(false);
  const orderIds = useMemo(() => [props.orderId], [props.orderId]);

  const pricingTax = useAppSelector(selectedServiceOrderState)?.pricingTax;
  const { setOrderPaymentState } = useServiceOrderPaymentContext();

  const repairLinesStatus = useMemo(() => {
    return props.group?.serviceOrders.find(
      (order) => order.id === props.orderId
    )?.warrantyStatusFromRepairLines;
  }, [props.group, props.orderId]);

  const oracleTrapReference = useMemo(() => {
    return props.group?.serviceOrders.find(
      (order) => order.id === props.orderId
    )?.oracleTrapReference;
  }, [props.group, props.orderId]);

  const isReturnDecision = useMemo(() => {
    return (
      props.group?.serviceOrders.find((order) => order.id === props.orderId)
        ?.repairDecision?.repairDecisionId === WarrantyActionCode.Return
    );
  }, [props.group?.serviceOrders, props.orderId]);

  // If order already has a trap ref, skip payment
  const skipPayment =
    (repairLinesStatus === WarrantyStatusFromRepairLines.Approved &&
      !pricingTax?.total) ||
    !!oracleTrapReference ||
    (isReturnDecision && (pricingTax?.total ?? 0) === 0);

  const isItpConfirmationAuthorized = !!oracleTrapReference;

  useEffect(() => {
    const updatePickupPartyTimeout = setTimeout(() => {
      setOrderPaymentState((prev) => ({ ...prev, pickupParty: pickupParty }));
    }, 400);

    return () => clearTimeout(updatePickupPartyTimeout);
  }, [pickupParty, setOrderPaymentState]);

  return (
    <>
      <Prompt
        open={openPrompt}
        header={t('features.order.abandonToolPickupQuestion')}
        message={t('common.areYouSureToExitThisPage')}
        confirmationButtonLabel={t('common.yesExit')}
        onPromptConfirm={() => router.push('/')}
        cancelButtonLabel={t('common.cancel')}
        onPromptClose={() => setOpenPrompt(false)}
      />
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          position: 'relative',
          flexDirection: 'column',
          overflow: 'hidden'
        }}
      >
        <PageHeaderContainer
          position="sticky"
          sx={{
            display: 'flex',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              onClick={props.onBackClick}
              data-testid="pickupConfirmationBackBtn-testId"
            >
              <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
            </IconButton>
            <Typography variant="h5">
              {t('features.order.confirmPickup')}
            </Typography>
          </Box>
          <IconButton
            data-testid="closeBtn-testId"
            onClick={() => setOpenPrompt(true)}
          >
            <CloseIcon />
          </IconButton>
        </PageHeaderContainer>
        <PageAreaBox
          pageAreaBoxName="pickupConfirmation"
          sx={{
            pb: 4,
            overflow: 'auto',
            flex: 1
          }}
        >
          <CustomerCard
            customer={props.customer}
            data-testid="customerData-testId"
            containerSxProps={{
              mb: 1,
              py: 0,
              pb: 2
            }}
          />
          <Card variant="outlined" sx={{ boxShadow: 1 }}>
            <CardContent
              sx={{ padding: '0 !important', backgroundColor: 'common.white' }}
            >
              <Box
                sx={{
                  borderBottom: '1px solid',
                  borderBottomColor: 'neutral.200',
                  px: 2,
                  py: 1
                }}
              >
                <Typography variant="p1" color="neutral.800">
                  {t('common.itemCount', {
                    current: 1,
                    all: 1
                  })}
                </Typography>
              </Box>
              <ServiceOrderHeader
                id={props.orderId}
                serviceOrderNumber={props.serviceOrderNumber}
                orderDateSubmitted={props.submittedDate}
                data-testid="serviceOrderHeader-testId"
                systemOrigin={1}
              />
              <ServiceOrderProduct
                product={props.product}
                orderId={props.orderId}
                data-testid="serviceOrderProduct-testId"
                isRepairByReplacement={props.isRepairByReplacement}
                isOperationalReplacement={props.isOperationalReplacement}
                isOutOfWarranty={props.isOutOfWarranty}
                lmrPricing={props.pricingData}
                warrantyStatusFromRepairLines={repairLinesStatus}
              />
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px',
                  px: 4,
                  py: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography
                    variant="h6"
                    color="neutral.800"
                    sx={{ fontWeight: '600', mr: 1 }}
                    data-testid="pickupPartyLabel-testId"
                  >
                    {t('features.order.pickupPartyInitialsOptional')}
                  </Typography>
                </Box>
                <TextField
                  placeHolder={t(
                    'features.order.pickupPartyInitialsPlaceholder'
                  )}
                  value={pickupParty}
                  maxCharacters={3}
                  name="pickupPartyInput"
                  data-testid="this-is-using-texfield-test-id-but-this-is-mandatory"
                  maxCharactersHelperText={true}
                  onChange={(e) => setPickupParty(e.target.value)}
                  sx={{
                    backgroundColor: 'common.white',
                    height: { xs: '64px' },
                    borderRadius: '4px',
                    '& .MuiInputBase-input': {
                      fontSize: { xs: '14px', sm: '18px' }
                    },
                    '& .MuiInputBase-root': {
                      px: { xs: '4px', sm: '6px', md: '10px' },
                      height: { xs: '66px' }
                    },
                    '& .MuiIconButton-root': {
                      px: { xs: '0px', md: '4px' }
                    }
                  }}
                />
              </Box>
              {props.isRepairByReplacement && (
                <ReplacementItem
                  product={props.product}
                  replacementSerialNumber={replacementSerialNumber}
                  onReplacementSerialNumberChange={setReplacementSerialNumber}
                  data-testid="replacementItem-testId"
                  hasSerialNumberInput
                />
              )}
            </CardContent>
          </Card>
        </PageAreaBox>
        <ServiceOrderPaymentDrawer
          customer={props.customer}
          orderIds={orderIds}
          onCompleteOrderClick={props.onNextClick}
          group={props.group}
          currencyType={props.currencyType}
          skipPayment={skipPayment}
          totalDue={pricingTax?.total ?? 0}
          totalTaxDue={pricingTax?.totalTax ?? 0}
          subTotalDue={pricingTax?.subTotal ?? 0}
          onInviteToPayCompleted={props.onInviteToPayCompleted}
          showCreditCardAuthorizedBadge={showCreditCardAuthorized}
          oracleTrapReference={oracleTrapReference}
          sx={{
            position: 'sticky'
          }}
          isItpConfirmationAuthorized={isItpConfirmationAuthorized}
        />
      </Box>
    </>
  );
};
