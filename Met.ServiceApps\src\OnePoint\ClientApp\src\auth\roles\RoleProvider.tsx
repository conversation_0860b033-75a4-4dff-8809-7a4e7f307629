'use client';

import { getStorage, updateStorage } from '@/store/local';
import { AccountInfo } from '@azure/msal-browser';
import { useAccount } from '@azure/msal-react';
import { useRouter } from 'next/navigation';
import {
  FC,
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState
} from 'react';
import { Role } from './role';

export interface RoleState {
  isAdmin: boolean;
  roles: Role[];
  currentRole: Role;
  setCurrentRole: (role: Role) => void;
}

export const RoleContext = createContext<RoleState>({
  isAdmin: false,
  roles: [],
  currentRole: Role.BranchAssociate,
  setCurrentRole: () => {
    return;
  }
});

interface Props {
  children: ReactNode;
}

export const RoleProvider: FC<Props> = ({ children }) => {
  const accountInfo = useAccount();
  const router = useRouter();
  const [currentRole, setCurrentRole] = useState<Role>(() =>
    getInitialRole(accountInfo)
  );

  const setCurrentRoleWrapper = useCallback(
    (role: Role) => {
      setCurrentRole(role);
      if (accountInfo && accountInfo?.username) {
        updateRole({ username: accountInfo.username, newRole: role });
      }
      router.push('/');
    },
    [router, accountInfo]
  );

  const contextValue: RoleState = useMemo(
    () => ({
      isAdmin: isAdmin(accountInfo),
      roles: getRoles(accountInfo),
      currentRole,
      setCurrentRole: setCurrentRoleWrapper
    }),
    [accountInfo, currentRole, setCurrentRoleWrapper]
  );

  return (
    <RoleContext.Provider value={contextValue}>{children}</RoleContext.Provider>
  );
};

export const useRoleContext = () => useContext(RoleContext);

const isAdmin = (accountInfo: AccountInfo | null): boolean => {
  return accountInfo?.idTokenClaims?.['roles']?.includes(Role.Admin) ?? false;
};

const getRoles = (accountInfo: AccountInfo | null): Role[] => {
  if (isAdmin(accountInfo)) {
    return Object.values(Role).filter((role) => role !== Role.Admin);
  }
  return (
    accountInfo?.idTokenClaims?.['roles']?.map(
      (role) => Role[role as keyof typeof Role]
    ) ?? []
  );
};

const getInitialRole = (accountInfo: AccountInfo | null): Role => {
  const roles = getRoles(accountInfo);
  const sessionData = getStorage();

  if (
    sessionData &&
    sessionData.selectedRoles &&
    accountInfo?.username &&
    sessionData.selectedRoles[accountInfo.username]
  ) {
    // Make sure the user still have the cached selected role
    if (roles.includes(sessionData.selectedRoles[accountInfo.username])) {
      return sessionData.selectedRoles[accountInfo.username];
    }
  }

  const initialRole = roles.includes(Role.BranchManager)
    ? Role.BranchManager
    : roles[0] ?? Role.BranchAssociate;

  if (accountInfo && accountInfo.username) {
    updateRole({ username: accountInfo.username, newRole: initialRole });
  }
  return initialRole;
};

const updateRole = (updateParams: { username: string; newRole: Role }) => {
  const { username, newRole } = updateParams;
  const sessionData = getStorage();
  const currentSelectedRoles = sessionData?.selectedRoles || {};

  updateStorage({
    selectedRoles: {
      ...currentSelectedRoles,
      [username]: newRole
    }
  });
};
