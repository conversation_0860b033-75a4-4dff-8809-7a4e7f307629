'use client';

import { useEnvContext } from '@/env';
import {
  AppInsightsContext,
  ReactPlugin,
  withAITracking
} from '@microsoft/applicationinsights-react-js';
import { ApplicationInsights } from '@microsoft/applicationinsights-web';
import { ReactNode } from 'react';

export const reactPlugin = new ReactPlugin();
let appInsights: ApplicationInsights | undefined = undefined;
function initializeAppInsights(appInsightsConnectionString?: string) {
  if (!appInsights && appInsightsConnectionString) {
    appInsights = new ApplicationInsights({
      config: {
        connectionString: appInsightsConnectionString,
        enableAutoRouteTracking: true,
        enableCorsCorrelation: true,
        enableRequestHeaderTracking: true,
        enableResponseHeaderTracking: true,
        enableAjaxPerfTracking: true,
        isBrowserLinkTrackingEnabled: true,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        extensions: [reactPlugin as any], // Known TypeScript intellisense error: https://github.com/microsoft/applicationinsights-react-js/issues/32
        extensionConfig: {
          [reactPlugin.identifier]: {
            debug: false
          }
        },
        correlationHeaderExcludedDomains: [
          'localhost:9100' // zebra browser print
        ]
      }
    });

    if (typeof window !== 'undefined') {
      appInsights.loadAppInsights();
    }
  }

  return reactPlugin;
}

interface Props {
  children?: ReactNode;
}

const AppInsightsProvider = ({ children }: Props) => {
  const { appInsightsConnectionString } = useEnvContext();
  return (
    <AppInsightsContext.Provider
      value={initializeAppInsights(appInsightsConnectionString)}
    >
      {children}
    </AppInsightsContext.Provider>
  );
};

export const AppInsightsTrackingProvider = withAITracking(
  reactPlugin,
  AppInsightsProvider
);
