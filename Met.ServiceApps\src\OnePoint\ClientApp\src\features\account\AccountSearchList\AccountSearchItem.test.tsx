import '@testing-library/jest-dom';
import { fireEvent, render, screen, within } from '@testing-library/react';
import { useRouter } from 'next/navigation';

import {
  AccountSearchItem,
  ParentAccount,
  Site,
  SiteItem
} from '@/features/account';

jest.mock('next/navigation', () => ({
  __esModule: true,
  useRouter: jest.fn()
}));

describe('AccountSearchItem component tests', () => {
  const mockParentAccount: ParentAccount = {
    id: '1',
    accountNumber: '12345',
    accountDescription: 'Example Account'
  };

  const mockSites: Site[] = Array.from({ length: 3 }, (_, index) => ({
    id: `site${index + 1}`,
    siteName: `SiteName${index + 1}`,
    siteNumber: `Site${index + 1}`,
    serviceProgram: 'Service',
    paymentTerms: 'Terms',
    isNationalAccount: true,
    addressLine1: 'Address1',
    addressLine2: 'Address2',
    addressLine3: 'Address3',
    city: 'City',
    state: 'State',
    postalCode: 'PostalCode',
    countryCode: 'CountryCode'
  }));

  const mockSiteItems: SiteItem[] = mockSites.map((site) => ({
    ...site,
    parentAccount: mockParentAccount
  }));

  const useRouterMock = useRouter as jest.Mock;
  const routerMock = { push: jest.fn() };
  useRouterMock.mockReturnValue(routerMock);

  it('Renders successfully account with email', () => {
    const mockAccount = mockSiteItems[1];
    const mockOnAccountSelected = jest.fn();

    render(
      <AccountSearchItem
        site={mockAccount}
        isViewMode={true}
        onAccountSelected={mockOnAccountSelected}
      />
    );

    const icon = screen.getByTestId('usersAccountListIcon-testId');
    const fullName = screen.getByTestId(`accountName${mockAccount.id}-testId`);

    const container = screen.getByTestId('customerCardContent-testId');
    const viewBtn = within(container).getByTestId('selectAccountButton-testId');

    expect(icon).toBeInTheDocument();
    expect(fullName).toHaveTextContent('SiteName2');
    expect(viewBtn).toHaveTextContent('View');

    fireEvent.click(viewBtn);

    expect(mockOnAccountSelected).toHaveBeenCalledTimes(1);
  });

  it('Renders select button when isViewMode is false', () => {
    const mockAccount = mockSiteItems[1];
    const mockOnAccountSelected = jest.fn();

    render(
      <AccountSearchItem
        site={mockAccount}
        isViewMode={false}
        onAccountSelected={mockOnAccountSelected}
      />
    );

    const container = screen.getByTestId('customerCardContent-testId');
    const selectBtn = within(container).getByTestId(
      'selectAccountButton-testId'
    );

    fireEvent.click(selectBtn);

    expect(selectBtn).toHaveTextContent('Select');

    expect(mockOnAccountSelected).toHaveBeenCalledTimes(1);
  });

  it('Renders select button when isViewMode is true', () => {
    const mockAccount = mockSiteItems[1];
    const mockOnAccountSelected = jest.fn();
    const searchText = 'test';
    const searchPostalCodeText = 'test';
    const mockRouter = {
      push: jest.fn()
    };

    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    render(
      <AccountSearchItem
        site={mockAccount}
        isViewMode={true}
        onAccountSelected={mockOnAccountSelected}
        searchText={searchText}
        searchPostalCodeText={searchPostalCodeText}
      />
    );

    const container = screen.getByTestId('customerCardContent-testId');
    const selectBtn = within(container).getByTestId(
      'selectAccountButton-testId'
    );

    fireEvent.click(selectBtn);
    expect(mockOnAccountSelected).toHaveBeenCalledTimes(1);
    expect(mockOnAccountSelected).toHaveBeenCalledWith(
      mockAccount.siteNumber,
      mockAccount
    );
  });
});
