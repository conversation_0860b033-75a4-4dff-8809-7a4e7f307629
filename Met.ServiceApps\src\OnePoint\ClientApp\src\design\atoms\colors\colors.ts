import {
  CommonColors,
  SimplePaletteColorOptions,
  TypeBackground
} from '@mui/material';

declare module '@mui/material/styles' {
  interface CommonColors {
    transparent: string;
  }
}

interface StyleGuideColor extends SimplePaletteColorOptions {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export const NEUTRAL: StyleGuideColor = {
  50: '#F8F9FA',
  100: '#F1F3F6',
  200: '#d6dbe3',
  300: '#bac3d0',
  400: '#9eacbd',
  500: '#7588A1',
  main: '#7588A1',
  600: '#5E718A',
  700: '#4b5a6e',
  800: '#384453',
  900: '#262d37'
};

export const PRIMARY: StyleGuideColor = {
  50: '#E6F0FC',
  100: '#CDE1FA',
  200: '#9CC3F5',
  300: '#6AA6F0',
  400: '#5197EE',
  500: '#076AE6',
  main: '#076AE6',
  600: '#0655b8',
  700: '#0041B3',
  800: '#032a5c',
  900: '#01152e'
};

export const SUCCESS: StyleGuideColor = {
  50: '#ECFDF5',
  100: '#D1FAE5',
  200: '#A7F3D0',
  300: '#6EE7B7',
  400: '#34D399',
  500: '#10B981',
  main: '#10B981',
  600: '#059669',
  700: '#047857',
  800: '#065F46',
  900: '#064E3B'
};

export const WARNING: StyleGuideColor = {
  50: '#FFFBEB',
  100: '#FEF3C7',
  200: '#FDE68A',
  300: '#FCD34D',
  400: '#FBBF24',
  500: '#F59E0B',
  main: '#F59E0B',
  600: '#D97706',
  700: '#B45309',
  800: '#92400E',
  900: '#78350F'
};

export const ERROR: StyleGuideColor = {
  50: '#FEF2F2',
  100: '#FEE2E2',
  200: '#FECACA',
  300: '#FCA5A5',
  400: '#F87171',
  500: '#EF4444',
  main: '#EF4444',
  600: '#DC2626',
  700: '#B91C1C',
  800: '#991B1B',
  900: '#7F1D1D'
};

export interface BrandVariables {
  drpClthGray: string;
  apprntceGray: string;
  pewterGray: string;
  wshrGray: string;
  wingnutGray: string;
  mkeRed: string;
  crimperRed: string;
  bladeGray: string;
  shopVacGray: string;
  slateBlack: string;
}

export const BRAND_VARIABLES: BrandVariables = {
  drpClthGray: '#F6F7F8',
  apprntceGray: '#F0F0F0',
  pewterGray: '#e9e9e9',
  wshrGray: '#DDDDDD',
  wingnutGray: '#ABACAD',
  mkeRed: '#DB011C',
  crimperRed: '#AD0015',
  bladeGray: '#747476',
  shopVacGray: '#515355',
  slateBlack: '#343434'
};

export const COMMON: CommonColors = {
  black: '#000000',
  white: '#FFFFFF',
  transparent: 'transparent'
};

export const BACKGROUND: TypeBackground = {
  default: '#F1F3F6',
  paper: '#F1F3F6'
};
