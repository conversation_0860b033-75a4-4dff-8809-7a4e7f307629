import { renderWithProviders } from '@/../__test-utils__/renderWithProviders';
import { accountPageSlice } from '@/features/account';
import { selectedBranchSlice } from '@/features/branch';
import {
  PagedListDetails,
  ProfileSearchBy,
  SearchCustomer
} from '@/features/search';
import { AppStore } from '@/store';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import { fireEvent, getByRole, screen, waitFor } from '@testing-library/react';
import UserEvent from '@testing-library/user-event';

const store = configureStore({
  reducer: {
    [accountPageSlice.name]: accountPageSlice.reducer,
    [selectedBranchSlice.name]: selectedBranchSlice.reducer
  },
  preloadedState: {
    [accountPageSlice.name]: {
      showProfileSearch: false,
      showContactDetailsModal: false,
      redirectToPagePath: ''
    },
    [selectedBranchSlice.name]: {}
  }
}) as AppStore;

describe('SearchCustomer tests', () => {
  it('should note default mask when searching by phone number only without country', () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();
    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.CustomerInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        isAdvancedFiltersOpen={false}
        setAdvancedFiltersOpen={jest.fn()}
      />
    );

    const Input = screen
      .getByTestId(`searchInput-testId`)
      .querySelector('input') as HTMLInputElement;

    fireEvent.change(Input, {
      target: { value: '**********' }
    });

    expect(Input).toHaveValue('**********');
  });
  it('should not have mask when searching by starting with number 1', () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();

    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.CustomerInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        isAdvancedFiltersOpen={false}
        setAdvancedFiltersOpen={jest.fn()}
      />
    );

    const Input = screen
      .getByTestId(`searchInput-testId`)
      .querySelector('input') as HTMLInputElement;

    fireEvent.change(Input, {
      target: { value: '1**********' }
    });

    expect(Input).toHaveValue('1**********');
  });

  it('should not call searchi endpoints when is in searching profile mode from account page', () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();

    const location = {
      ...window.location,
      search: '?st=1234&spc=54321'
    };
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location
    });

    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.CustomerInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        isAdvancedFiltersOpen={false}
        setAdvancedFiltersOpen={jest.fn()}
      />,
      { store: store }
    );

    expect(handleOnCustomerSearchBoxChangeMock).not.toBeCalled();
  });

  it('should show advanced search fields when prop is true', () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();

    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.AccountInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        setAdvancedFiltersOpen={jest.fn()}
        isAdvancedFiltersOpen
      />,
      { store: store }
    );

    expect(screen.getByTestId('parentAccountName-testId')).toBeInTheDocument();
    expect(screen.getByTestId('city-testId')).toBeInTheDocument();
    expect(screen.getByTestId('postalCode-testId')).toBeInTheDocument();
    expect(
      screen.getByTestId('applyFiltersAdvancedSearchButton-testid')
    ).toBeInTheDocument();
  });

  it('should show reset button when field is changed', async () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();

    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.AccountInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        setAdvancedFiltersOpen={jest.fn()}
        isAdvancedFiltersOpen
      />,
      { store: store }
    );

    const input = screen
      .getByTestId('parentAccountName-testId')
      .querySelector('input') as HTMLInputElement;

    await UserEvent.type(input, 'Fergusson');

    expect(
      screen.getByTestId('resetAdvancedSearchButton-testid')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('resetAdvancedSearchButton-testid')
    ).toBeEnabled();
  });

  it('should enable apply filters button only when required fields are not empty and at least on the optional fields is not empty', async () => {
    const handleInputChangeMock = jest.fn();
    const handleCustomerSelectionMock = jest.fn();
    const handleLoadMoreClickMock = jest.fn();
    const handleOnAccountSelectedMock = jest.fn();
    const setCustomerSearchByMock = jest.fn();
    const handleOnCustomerSearchBoxChangeMock = jest.fn();

    renderWithProviders(
      <SearchCustomer
        isViewMode={true}
        isFetching={false}
        customers={[]}
        onSearchBoxChange={handleInputChangeMock}
        onCustomerSelected={handleCustomerSelectionMock}
        isListOpened={false}
        valueNotFound={false}
        customerSearchBy={ProfileSearchBy.AccountInfo}
        paginationData={{} as PagedListDetails}
        onLoadMoreClick={handleLoadMoreClickMock}
        isListOpenedAccountList={false}
        onAccountSearchBoxChange={handleInputChangeMock}
        isAccountSearchFetching={false}
        onLoadMoreAccountsClick={handleLoadMoreClickMock}
        paginationAccountData={{} as PagedListDetails}
        sites={[]}
        onAccountSelected={handleOnAccountSelectedMock}
        setCustomerSearchBy={setCustomerSearchByMock}
        onCustomerSearchBoxChange={handleOnCustomerSearchBoxChangeMock}
        setAdvancedFiltersOpen={jest.fn()}
        isAdvancedFiltersOpen
      />,
      { store: store }
    );

    const input = screen
      .getByTestId('accountSearchInput-testId')
      .querySelector('input') as HTMLInputElement;
    fireEvent.change(input, {
      target: { value: 'Ferguson' }
    });

    const select = getByRole(
      screen.getByTestId('select-stateOrProvince-testId'),
      'combobox'
    );

    await UserEvent.click(select);
    await waitFor(() => UserEvent.click(screen.getByText('Alabama')));

    const cityInput = screen
      .getByTestId('city-testId')
      .querySelector('input') as HTMLInputElement;
    fireEvent.change(cityInput, {
      target: { value: 'Madison' }
    });

    expect(
      screen.getByTestId('applyFiltersAdvancedSearchButton-testid')
    ).toBeEnabled();
  });
});
