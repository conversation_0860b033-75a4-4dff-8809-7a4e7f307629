import { renderWithProviders } from '@/../__test-utils__/renderWithProviders';
import '@testing-library/jest-dom';
import CustomerSearchPage from './page';

describe('Customer page component tests', () => {
  it('It should render toggle buttons', () => {
    const { getByTestId } = renderWithProviders(<CustomerSearchPage />);

    expect(getByTestId('accountSearch-btn-testid')).toBeInTheDocument();
    expect(getByTestId('customerSearch-btn-testid')).toBeInTheDocument();
  });

  it('It should have the correct button text', () => {
    const { getByText } = renderWithProviders(<CustomerSearchPage />);

    expect(getByText('Site lookup')).toBeInTheDocument();
    expect(getByText('Profile lookup')).toBeInTheDocument();
  });
});
