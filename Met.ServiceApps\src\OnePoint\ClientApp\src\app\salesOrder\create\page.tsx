'use client';

import { Loading } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { selectBranch } from '@/features/branch';
import { useCalculateSalesPricingMutation } from '@/features/pricing';
import {
  mapSalesPricingResponse,
  SalesOrderCartHeader,
  SalesOrderCartSection,
  salesOrderCreationView,
  SalesOrderCustomerSearch,
  SalesOrderCustomerSection,
  SalesOrderPageHeader,
  SalesOrderPaymentCompleted,
  SalesOrderProductSearch,
  SalesOrderSummaryHeader,
  selectedSalesOrderCreationState,
  setCurrentView
} from '@/features/salesOrder';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { Box } from '@mui/material';
import { useEffect } from 'react';

export default function SalesOrderCreationPage() {
  const dispatch = useAppDispatch();
  const { salesOrderCreationProfile, selectedCustomer } = useAppSelector(
    (state) => state.customer
  );
  const { currentView, selectedProducts } = useAppSelector(
    selectedSalesOrderCreationState
  );
  const selectedBranch = useAppSelector(selectBranch);
  const [calculateSalesPricing, { isLoading }] =
    useCalculateSalesPricingMutation();

  useEffect(() => {
    if (selectedCustomer || salesOrderCreationProfile?.isGuest) {
      dispatch(setCurrentView(salesOrderCreationView.CartSummary));
    }
  }, [selectedCustomer, dispatch, salesOrderCreationProfile]);

  const handleCalculatePricing = async () => {
    const accountNumber = salesOrderCreationProfile?.accountNumber ?? '';

    const request = {
      requestType: 2,
      priceDate: new Date().toISOString(),
      branchId: selectedBranch?.id,
      accountNumber,
      lines: selectedProducts.map((product, index) => ({
        itemNumber: product.product.sku,
        quantity: product.quantity,
        lineNumber: index.toString(),
        unitOfMeasure: 'EA',
        netZeroFlag: false,
        servicePrice: false,
        pceCode: product.pceCode
      }))
    };

    try {
      const response = await calculateSalesPricing(request).unwrap();
      dispatch(mapSalesPricingResponse(response));
      dispatch(setCurrentView(salesOrderCreationView.OrderSummary));
    } catch (err) {
      console.error('Failed to calculate pricing:', err);
    }
  };

  function renderPageContent() {
    if (currentView === salesOrderCreationView.CustomerSelection) {
      return <SalesOrderCustomerSearch />;
    }

    if (currentView === salesOrderCreationView.CartSummary) {
      return (
        <>
          <SalesOrderCustomerSection />
          <SalesOrderCartHeader onContinueToCheckout={handleCalculatePricing} />
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <SalesOrderCartSection />
          </Box>
        </>
      );
    }

    if (currentView === salesOrderCreationView.ProductSearch) {
      return <SalesOrderProductSearch />;
    }

    if (currentView === salesOrderCreationView.OrderSummary) {
      return (
        <>
          <SalesOrderCustomerSection />
          <SalesOrderSummaryHeader />
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: '1rem'
            }}
          >
            <SalesOrderCartSection />
          </Box>
        </>
      );
    }

    if (currentView === salesOrderCreationView.PaymentCompleted) {
      return (
        <>
          <SalesOrderCustomerSection />
          <SalesOrderPaymentCompleted />
        </>
      );
    }

    return null;
  }

  return (
    <>
      <SalesOrderPageHeader />
      <PageAreaBox
        sx={{ mt: 6, maxWidth: '1460px' }}
        pageAreaBoxName="OrderRepair"
      >
        <Loading
          isLoading={isLoading}
          fallbackContainerProps={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            zIndex: 1
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              pt: '36px'
            }}
          >
            {renderPageContent()}
          </Box>
        </Loading>
      </PageAreaBox>
    </>
  );
}
