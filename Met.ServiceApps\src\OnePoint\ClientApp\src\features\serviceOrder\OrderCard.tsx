import { Role, useRoleContext } from '@/auth';
import { CircleExclamationIcon } from '@/design/atoms';
import { Button, ProgressBar } from '@/design/molecules';
import {
  AccountSubTitle,
  FilterField,
  SearchCdlCustomersRequest,
  SearchField,
  SiteItem,
  useLazySearchCustomersCdlQuery
} from '@/features/account';
import { COUNTRY_CODES, Location, selectBranch } from '@/features/branch';
import { Address, Customer, CustomerCard } from '@/features/customer';
import { setPersistSearch } from '@/features/search';
import {
  CondensedAddress,
  CreditCardAuthorizedBadge,
  OrderChildRow,
  OrderListDisplayOrigin,
  OrderType,
  PoNumberOrderCard,
  ServiceOrderHeader,
  ServiceOrderStatusCode,
  ToolRetrievedAlert,
  serviceOrderStatusText
} from '@/features/serviceOrder';
import { useNavigation } from '@/hooks';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  calculateStatusCodeProgressPercentage,
  isDecisionMadeFromOracle,
  isOPR
} from '@/util';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  Grid,
  Tooltip,
  Typography,
  linearProgressClasses
} from '@mui/material';
import MuiChip from '@mui/material/Chip';
import { useTranslation } from 'next-export-i18n';
import Image from 'next/image';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ToolRecycledAlert } from './Details/ToolRecycledAlert/ToolRecycledAlert';
import {
  DeliveryType,
  DeliveryTypeNames,
  OrderIds,
  ProductDataMap,
  ServiceOrderGroupSearch,
  ServiceOrderOracleStatus,
  SignalRHandlers
} from './models';
import { RepairDecisionType } from './models/repairDecisionType';
import { ServiceOrderSearch } from './models/serviceOrderSearch';

interface Props {
  group: ServiceOrderGroupSearch;
  signalRHandlers: SignalRHandlers;
  productDataMap: ProductDataMap;
  branch?: Location;
  redirectToHomePage?: boolean;
  productsChecked?: {
    [x: string]: boolean;
  };
  onToolCheck?: (
    e: React.ChangeEvent<HTMLInputElement>,
    orderId: string
  ) => void;
  showMultiToolCheckbox?: boolean;
  customer?: Customer | undefined;
  customerAddress?: Address;
  pageRedirect?: number;
  onProcessOrderClick?: (orderIds: OrderIds, systemOrigin: OrderType) => void;
  displayOrigin?: OrderListDisplayOrigin;
  site?: SiteItem;
  onCustomProcessOrderClick?: (orderId: string) => void;
  orderSelectedHasTrapReference?: boolean;
}

export const OrderCard = (props: Props) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const defaultImage = '/logo-milwaukeetool-gray.svg';
  const [expanded, setExpanded] = useState(false);
  const getToolMessage = (value: number) =>
    `${t('features.order.toolsCounterText')} (${value})`;

  const hasMultipleServiceOrders = props.group.serviceOrders.length > 1;
  const singleOrderIsReadyForPickup =
    props.group.serviceOrders[0].status ===
    ServiceOrderOracleStatus.SrShipWillCall;
  const { currentRole } = useRoleContext();
  const isBranchRole =
    currentRole === Role.BranchManager || currentRole === Role.BranchAssociate;
  const isHubRole = currentRole === Role.Hub;
  const shippingAddress =
    props.group.serviceOrders[0].dropShipAddress ?? props.customerAddress;

  const selectedBranch = useAppSelector(selectBranch);

  const { goToRepairDetailOrderPage } = useNavigation();

  const [statusId, setStatusId] = useState(
    props.group.systemOrigin === OrderType.BranchOrder
      ? props.group.serviceOrders[0].onepointStatusId!
      : props.group.serviceOrders[0].eserviceStatusId!
  );

  const calculateState = useCallback(
    (order: ServiceOrderSearch): ServiceOrderStatusCode => {
      if (
        props.group.systemOrigin === OrderType.BranchOrder &&
        order?.pickedUpDate
      ) {
        return ServiceOrderStatusCode.OrderComplete;
      }
      return statusId;
    },
    [props.group.systemOrigin, statusId]
  );

  const [serviceRequestNumber, setServiceRequestNumber] = useState(
    props.group.serviceOrders[0].serviceRequestNumber
  );

  const calculatedState = useMemo(() => {
    return statusId;
  }, [statusId]);

  const getStatusText = (state: ServiceOrderStatusCode) => {
    if (state) {
      const stateName = serviceOrderStatusText[state];
      const systemOriginStatesKey =
        props.group.systemOrigin === OrderType.BranchOrder
          ? 'onePointStates'
          : 'eServiceStates';
      return `serviceOrderStates.${systemOriginStatesKey}.${stateName}`;
    }
  };

  const isMultiToolCheckboxDisabled = useCallback(
    (serviceOrder: ServiceOrderSearch) => {
      return (
        !!serviceOrder.oracleTrapReference !==
          props.orderSelectedHasTrapReference &&
        props.productsChecked &&
        Object.values(props.productsChecked).some((value) => value)
      );
    },
    [props.orderSelectedHasTrapReference, props.productsChecked]
  );

  const SingleToolOrderInfo = (): React.ReactNode => {
    const serviceOrder = props.group.serviceOrders[0];
    const product = props.productDataMap[serviceOrder.sku];

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        {props.showMultiToolCheckbox && (
          <Checkbox
            checked={props.productsChecked?.[serviceOrder.id]}
            onChange={(e) =>
              props.onToolCheck ? props.onToolCheck(e, serviceOrder.id) : {}
            }
            disabled={isMultiToolCheckboxDisabled(serviceOrder)}
            sx={{ pl: 0, pr: 0.5 }}
            data-testid={`product${serviceOrder.id}CheckBox-testId`}
          />
        )}
        <Image
          src={product.image ?? defaultImage}
          alt={t('common.logo')}
          width={70}
          height={70}
          data-testid={`toolInfo-image-${serviceOrder.id}-testId`}
        />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 0.5
          }}
        >
          <Typography
            variant="p1"
            color="neutral.500"
            data-testid={`toolInfo-sku-${serviceOrder.id}-testId`}
          >
            {serviceOrder.sku}
          </Typography>
          <Tooltip title={product.description}>
            <Typography
              variant="p1"
              color="neutral.800"
              fontWeight={600}
              noWrap={true}
              data-testid={`toolInfo-description-${serviceOrder.id}-testId`}
              sx={{ textOverflow: 'ellipsis', maxWidth: '250px' }}
            >
              {product.description}
            </Typography>
          </Tooltip>
          {serviceOrder.serialNumber && (
            <Typography
              variant="p1"
              color="primary"
              data-testid={`toolInfo-serialNumber-${serviceOrder.id}-testId`}
            >
              {serviceOrder.serialNumber}
            </Typography>
          )}
          {serviceOrder.serialNumberUnreadable && (
            <Typography
              variant="p1"
              color="primary"
              fontStyle="italic"
              data-testid={`toolInfo-serialNumberUnreadable-${serviceOrder.id}-testId`}
            >
              {t('features.product.serialNumberUnreadable')}
            </Typography>
          )}
        </Box>
      </Box>
    );
  };

  const MultiToolOrderInfo = (): React.ReactNode => {
    return (
      <>
        {props.group.serviceOrders.map((serviceOrder) => (
          <Image
            key={serviceOrder.id}
            src={props.productDataMap[serviceOrder.sku].image ?? defaultImage}
            alt={t('common.logo')}
            width={70}
            height={70}
            data-testid={`toolInfo-image-${serviceOrder.id}-testId`}
          />
        ))}
      </>
    );
  };

  const ToolOrderInfo = (): React.ReactNode => {
    if (hasMultipleServiceOrders) {
      return <MultiToolOrderInfo />;
    }

    return <SingleToolOrderInfo />;
  };

  const getActionButtonText = (
    status: ServiceOrderOracleStatus,
    systemOrigin: OrderType,
    serviceOrderNumber?: string,
    needsPickup?: boolean
  ) => {
    if (props.onCustomProcessOrderClick) {
      return t(`features.serviceRequestEstimate.linkSV`);
    }

    if (
      status === ServiceOrderOracleStatus.SrAwaitingCustomerPickUp &&
      needsPickup
    ) {
      return t(`features.order.confirmPickup`);
    }

    if (
      (isHubRole || selectedBranch?.countryCode === COUNTRY_CODES.CANADA) &&
      !serviceOrderNumber &&
      (systemOrigin === OrderType.EService || systemOrigin === OrderType.OneKey)
    ) {
      return t(`features.order.processOrder`);
    }

    if (
      isBranchRole &&
      serviceOrderNumber &&
      serviceOrderNumber.trim() !== ''
    ) {
      return t(`features.order.viewDetails`);
    }

    return t(`features.order.viewDetails`);
  };

  const [searchCustomersCdlTrigger, { isFetching: isFetchingAccountInfo }] =
    useLazySearchCustomersCdlQuery();

  const handleActionButtonClick = async (
    order: ServiceOrderSearch,
    systemOrigin: OrderType,
    serviceOrderNumber: string | undefined
  ) => {
    if (
      (isHubRole || selectedBranch?.countryCode === COUNTRY_CODES.CANADA) &&
      !serviceOrderNumber &&
      (systemOrigin === OrderType.EService ||
        systemOrigin === OrderType.OneKey) &&
      props.onProcessOrderClick
    ) {
      props.onProcessOrderClick(
        {
          groupId: props.group.id,
          orderId: order.id
        },
        systemOrigin
      );
    } else if (props.onCustomProcessOrderClick) {
      props.onCustomProcessOrderClick(order.id);
    } else {
      if (props.displayOrigin === OrderListDisplayOrigin.HomeAdvancedSearch) {
        dispatch(setPersistSearch(true));
      }

      let siteSearched: SiteItem | undefined;
      if (!props.site && !order.isGenericAccount) {
        siteSearched = await searchForSite(order);
      }

      goToRepairDetailOrderPage(
        props.group.id,
        isOPR([order.sku]),
        false,
        getId(order),
        props.redirectToHomePage ?? false,
        props.pageRedirect,
        props.displayOrigin,
        props.site?.siteNumber ?? siteSearched?.siteNumber,
        props.site?.postalCode ?? siteSearched?.postalCode,
        false
      );
    }
  };

  const searchForSite = async (order: ServiceOrderSearch) => {
    const filterFields: FilterField[] = [];
    const searchFields: SearchField[] = [];
    if (order.shipToSiteNumber === undefined) return undefined;
    filterFields.push({
      field: 'SiteNumber',
      filter: order.shipToSiteNumber!
    });

    const params = {
      pageNumber: 1,
      pageSize: 1,
      searchFields,
      filterFields
    };

    const parameters: SearchCdlCustomersRequest = params;

    const response = await searchCustomersCdlTrigger(parameters).unwrap();

    const allAccounts = response.customers.flatMap(
      (customer) => customer.accounts
    );

    const allSiteItems: SiteItem[] = allAccounts.flatMap((account) => {
      const { sites, ...parentAccount } = account;
      return sites.map((site) => ({
        ...site,
        parentAccount
      }));
    });

    const site = allSiteItems.find(
      (site) => site.siteNumber === order.shipToSiteNumber
    );

    return site;
  };

  const { addListener, removeListener, joinServiceRequestGroup } =
    props.signalRHandlers;

  useEffect(() => {
    const signalrMethod = 'RefreshServiceRequest';

    const handleUpdate = (update: {
      id: string;
      eServiceStatusId: number;
      onePointStatusId: number;
      serviceRequestNumber: string;
    }) => {
      if (update.id === props.group.serviceOrders[0].id) {
        if (props.group.systemOrigin === OrderType.BranchOrder) {
          setStatusId(update.onePointStatusId);
        } else if (
          props.group.systemOrigin === OrderType.EService ||
          props.group.systemOrigin === OrderType.OneKey
        ) {
          setStatusId(update.eServiceStatusId);
        }
        setServiceRequestNumber(update.serviceRequestNumber);
      }
    };

    addListener(signalrMethod, handleUpdate);

    const joinGroup = async () => {
      try {
        await joinServiceRequestGroup(props.group.serviceOrders[0].id);
      } catch (err) {
        console.error(
          'Error joining SignalR group for service request:',
          props.group.serviceOrders[0].id,
          err
        );
      }
    };

    joinGroup();

    return () => {
      removeListener(signalrMethod);
    };
  }, [
    addListener,
    removeListener,
    joinServiceRequestGroup,
    props.group.serviceOrders,
    props.group.systemOrigin
  ]);

  const getIdDisplay = (id: string) => {
    return ` ${id}`;
  };

  const getId = (
    groupOrOrder?: ServiceOrderGroupSearch | ServiceOrderSearch
  ): string => {
    if (!groupOrOrder) {
      groupOrOrder = props.group;
    }
    if ('serviceOrders' in groupOrOrder) {
      if (!hasMultipleServiceOrders) {
        return groupOrOrder.serviceOrders[0].id;
      }
    }
    return groupOrOrder.id;
  };

  const getHeaderId = (
    groupOrOrder?: ServiceOrderGroupSearch | ServiceOrderSearch
  ): string => {
    if (!groupOrOrder) {
      groupOrOrder = props.group;
    }
    if ('serviceOrders' in groupOrOrder) {
      if (!hasMultipleServiceOrders) {
        return serviceRequestNumber != undefined
          ? serviceRequestNumber
          : groupOrOrder.inboundShipments?.join(' , ');
      }
    }
    return '';
  };

  const disableOrderButton = useCallback(
    (order: ServiceOrderSearch): boolean => {
      if (
        props.onCustomProcessOrderClick &&
        props.productDataMap[order.sku].pricingStrategy !== 'Estimate'
      ) {
        return true;
      }

      return (props.onCustomProcessOrderClick && order.hasEstimate) ?? false;
    },
    [props.onCustomProcessOrderClick, props.productDataMap]
  );

  const customerAccountAddress = useMemo(() => {
    const address = props.group.serviceOrders[0].shipToMetadata;
    if (address) {
      return {
        addressLine1: address.addressLine1,
        city: address.city,
        state: address.state,
        countryCode: address.country,
        postalCode: address.postalCode
      } as Address;
    }
  }, [props.group.serviceOrders]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%'
      }}
    >
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        data-testid="accordion-testId"
        elevation={0}
        sx={{
          bgcolor: 'neutral.50',
          ['&:before']: {
            bgcolor: 'transparent'
          },
          '& .MuiAccordionDetails-root': {
            p: 0
          }
        }}
      >
        <Card sx={{ boxShadow: 'none' }}>
          <ServiceOrderHeader
            id={props.group.id}
            serviceOrderNumber={
              hasMultipleServiceOrders
                ? props.group.id
                : getIdDisplay(getHeaderId())
            }
            multipleServiceOrder={hasMultipleServiceOrders}
            orderDateSubmitted={props.group.serviceOrders[0].submittedDate}
            systemOrigin={props.group?.systemOrigin ?? 0}
            sx={{
              padding: '12px 32px',
              backgroundColor: 'neutral.50',
              borderBottom: '1px solid',
              borderBottomColor: 'neutral.300'
            }}
          />
          {props.customer && props.group.serviceOrders[0].shipToMetadata && (
            <>
              <Box
                data-testid={'customer-generic-account-details'}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  px: 3,
                  py: 2
                }}
              >
                <AccountSubTitle
                  accountName={
                    props.group.serviceOrders[0].shipToMetadata
                      .shipToSiteName ?? ''
                  }
                  siteNumber={props.group.serviceOrders[0].shipToSiteNumber!}
                  accountAddress={customerAccountAddress}
                />
              </Box>
              <CustomerCard
                customer={props.customer}
                showOnlyCustomerInfo
                containerSxProps={{
                  borderBottom: '1px solid',
                  borderBottomColor: 'neutral.200',
                  backgroundColor: 'common.white'
                }}
              />
            </>
          )}

          <CardContent sx={{ flexGrow: 1, padding: '0 !important' }}>
            <Box
              sx={{
                backgroundColor: 'common.white',
                py: '12px',
                pl: '36px',
                borderBottom: '1px solid',
                borderBottomColor: 'neutral.400',
                display: 'flex',
                gap: '32px'
              }}
            >
              <Box sx={{ width: props.showMultiToolCheckbox ? '31%' : '27%' }}>
                <Box>
                  <Typography
                    variant="p2"
                    color="secondary.900"
                    sx={{ fontWeight: 'bold' }}
                  >
                    {getToolMessage(props.group.serviceOrders.length)}
                  </Typography>
                </Box>
                <ToolOrderInfo />
              </Box>
              <Box
                sx={{
                  width: '20%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Typography
                  variant="p2"
                  color="secondary.900"
                  sx={{ fontWeight: 'bold' }}
                  data-testid="orderStatus-testId"
                >
                  {t('features.order.orderStatus')}
                </Typography>
                {/* State progress bar  */}
                <>
                  {hasMultipleServiceOrders ? (
                    <MuiChip
                      sx={{
                        p: '8px',
                        justifyContent: 'left',
                        width: 'fit-content',
                        borderRadius: '8px',
                        mt: '20px'
                      }}
                      label={
                        <>
                          <Typography
                            variant="p2"
                            fontWeight={600}
                            color="neutral.700"
                            data-testid={`orderStatusChip-testId`}
                          >
                            {t('features.order.expandMultiToolOrder')}
                          </Typography>
                        </>
                      }
                    />
                  ) : calculateState(props.group.serviceOrders[0]) ===
                    ServiceOrderStatusCode.ActionRequired ? (
                    <>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '16px 0px',
                          gap: '6px'
                        }}
                      >
                        <CircleExclamationIcon
                          sx={{
                            color: 'error.600',
                            width: '16px',
                            height: '16px'
                          }}
                          viewBox="0 0 16 16"
                        />
                        <Typography
                          variant="p2"
                          sx={{
                            whiteSpace: 'nowrap',
                            color: 'error.600',
                            fontWeight: 600
                          }}
                          data-testid={`orderStatusValue${props.group.serviceOrders[0].id}-testId`}
                        >
                          {t(`features.order.deniedWarrantyStatusText`)}
                        </Typography>
                      </Box>
                      <ProgressBar
                        value={calculateStatusCodeProgressPercentage(
                          calculatedState,
                          props.group.serviceOrders[0].deliveryType,
                          props.group.systemOrigin
                        )}
                        sx={{
                          [`& .${linearProgressClasses.bar}`]: {
                            borderRadius: 8,
                            backgroundColor: 'error.600'
                          }
                        }}
                      />
                    </>
                  ) : props.group.serviceOrders[0].repairDecision ===
                      RepairDecisionType.Scrap &&
                    calculateState(props.group.serviceOrders[0]) ===
                      ServiceOrderStatusCode.OrderComplete ? (
                    <ToolRecycledAlert
                      dateRecycled={
                        props.group.serviceOrders[0].repairDecisionDate ?? ''
                      }
                    />
                  ) : isDecisionMadeFromOracle(
                      props.group.serviceOrders[0].status,
                      props.group.serviceOrders[0].repairDecision
                    ) ? (
                    <ToolRecycledAlert
                      dateRecycled={
                        props.group.serviceOrders[0].lastStatusUpdateDate
                      }
                    />
                  ) : calculateState(props.group.serviceOrders[0]) !==
                    ServiceOrderStatusCode.OrderComplete ? (
                    <>
                      <Typography
                        variant="p1"
                        sx={{
                          whiteSpace: 'nowrap',
                          padding: '16px 0px',
                          color: 'black'
                        }}
                        data-testid={`orderStatusValue${props.group.serviceOrders[0].id}-testId`}
                      >
                        {t(`features.order.${getStatusText(calculatedState)}`)}
                      </Typography>

                      <ProgressBar
                        value={calculateStatusCodeProgressPercentage(
                          calculatedState,
                          props.group.serviceOrders[0].deliveryType,
                          props.group.systemOrigin
                        )}
                      />

                      {props.group.serviceOrders[0].oracleTrapReference && (
                        <Box
                          data-testid={`authBadge-${props.group.serviceOrders[0].id}-testId`}
                          sx={{
                            p: '16px 0px',
                            width: 'fit-content'
                          }}
                        >
                          <CreditCardAuthorizedBadge />
                        </Box>
                      )}
                    </>
                  ) : (
                    <ToolRetrievedAlert
                      dateRetrieved={
                        props.group.serviceOrders[0].pickedUpDate ?? ''
                      }
                      serviceCreditDate={
                        props.group.serviceOrders[0].serviceCreditDate
                      }
                    />
                  )}
                </>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '38%',
                  gap: '8px'
                }}
              >
                {props.group.systemOrigin === OrderType.EService ||
                props.group.systemOrigin === OrderType.OneKey ? (
                  <>
                    <Typography
                      variant="p1"
                      color="secondary.900"
                      sx={{ fontWeight: 'bold' }}
                      data-testid="orderAddressType-testId"
                    >
                      {DeliveryTypeNames[DeliveryType.DropShip]}
                    </Typography>
                    <Box
                      sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}
                    >
                      {shippingAddress && (
                        <CondensedAddress
                          address={shippingAddress}
                          truncateAddress={true}
                        />
                      )}

                      {props.group.serviceOrders[0].purchaseOrder &&
                        !hasMultipleServiceOrders && (
                          <PoNumberOrderCard
                            poNumber={
                              props.group.serviceOrders[0].purchaseOrder
                            }
                          />
                        )}
                    </Box>
                  </>
                ) : (
                  <>
                    <Typography
                      variant="p1"
                      color="secondary.900"
                      sx={{ fontWeight: 'bold' }}
                      data-testid="orderAddressType-testId"
                    >
                      {
                        DeliveryTypeNames[
                          props.group.serviceOrders[0].deliveryType ===
                            DeliveryType.DropShip &&
                          !props.group.serviceOrders[0].dropShipAddressId
                            ? DeliveryType.ShipTo
                            : props.group.serviceOrders[0].deliveryType
                        ]
                      }
                    </Typography>
                    {props.group.serviceOrders[0].deliveryType ===
                      DeliveryType.WillCall && (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1
                        }}
                      >
                        <Typography variant="p2">
                          {props.branch?.name}
                        </Typography>
                        {props.group.serviceOrders[0].purchaseOrder &&
                          !hasMultipleServiceOrders && (
                            <PoNumberOrderCard
                              poNumber={
                                props.group.serviceOrders[0].purchaseOrder
                              }
                            />
                          )}
                      </Box>
                    )}
                    {(props.group.serviceOrders[0].deliveryType ===
                      DeliveryType.DropShip ||
                      props.group.serviceOrders[0].deliveryType ===
                        DeliveryType.International) && (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1
                        }}
                      >
                        {shippingAddress && (
                          <CondensedAddress
                            address={shippingAddress}
                            truncateAddress={true}
                          />
                        )}

                        {props.group.serviceOrders[0].purchaseOrder &&
                          !hasMultipleServiceOrders && (
                            <PoNumberOrderCard
                              poNumber={
                                props.group.serviceOrders[0].purchaseOrder
                              }
                            />
                          )}
                      </Box>
                    )}
                  </>
                )}
              </Box>
              <Box
                sx={{
                  justifyContent: 'end !important',
                  width: singleOrderIsReadyForPickup ? '13%' : '11%',
                  display: 'flex !important'
                }}
              >
                {hasMultipleServiceOrders ? (
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon fontSize="large" />}
                  />
                ) : (
                  <Button
                    variant={
                      singleOrderIsReadyForPickup ? 'primary' : 'secondary'
                    }
                    onClick={() =>
                      handleActionButtonClick(
                        props.group.serviceOrders[0],
                        props.group.systemOrigin,
                        props.group.serviceOrders[0].serviceRequestNumber
                      )
                    }
                    data-testid="buttonViewOrder-testId"
                    sx={{
                      alignSelf: 'center',
                      padding: '6px 8px'
                    }}
                    disabled={disableOrderButton(props.group.serviceOrders[0])}
                  >
                    {getActionButtonText(
                      props.group.serviceOrders[0].status,
                      props.group.systemOrigin,
                      props.group.serviceOrders[0].serviceRequestNumber,
                      props.group.serviceOrders.some((x) => !x.pickedUpDate)
                    )}
                  </Button>
                )}
              </Box>
              <Box />
            </Box>
          </CardContent>
          {isFetchingAccountInfo && (
            <Grid data-testid="loadingSpinner-testId">
              <Card
                sx={{
                  background: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  alignSelf: 'stretch'
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <CircularProgress />
                </CardContent>
              </Card>
            </Grid>
          )}
        </Card>
        {hasMultipleServiceOrders && (
          <AccordionDetails>
            {props.group.serviceOrders.map((order) => {
              return (
                <OrderChildRow
                  key={order.id}
                  order={order}
                  systemOrigin={props.group.systemOrigin}
                  signalRHandlers={props.signalRHandlers}
                  getActionButtonText={getActionButtonText}
                  getStatusText={getStatusText}
                  group={props.group}
                  handleActionButtonClick={handleActionButtonClick}
                  showMultiToolCheckbox={props.showMultiToolCheckbox}
                  isMultiToolCheckboxDisabled={isMultiToolCheckboxDisabled(
                    order
                  )}
                  productDataMap={props.productDataMap}
                  disableOrderButton={disableOrderButton}
                  onToolCheck={props.onToolCheck}
                />
              );
            })}
          </AccordionDetails>
        )}
      </Accordion>
    </Box>
  );
};
