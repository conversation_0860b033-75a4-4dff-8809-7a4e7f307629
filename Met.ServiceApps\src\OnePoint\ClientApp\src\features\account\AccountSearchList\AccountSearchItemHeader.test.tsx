import { AccountSearchItemHeader } from '@/features/account';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';

jest.mock('next-export-i18n', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

describe('AccountSearchItemHeader component tests', () => {
  it('Renders successfully with all props', () => {
    const mockProps = {
      id: '1',
      parentAccountName: 'ACE Hardware',
      parentAccountNumber: '001234',
      'data-testid': 'account-header-test'
    };

    render(<AccountSearchItemHeader {...mockProps} />);

    const header = screen.getByTestId('account-header-test');
    const parentAccountNameTitle = screen.getByTestId(
      `parentAccountNameTitle-${mockProps.id}-testId`
    );
    const parentAccountName = screen.getByTestId(
      `parentAccountName-${mockProps.id}-testId`
    );
    const parentAccountNumber = screen.getByTestId(
      `parentAccountNumber-${mockProps.id}-testId`
    );

    expect(header).toBeInTheDocument();
    expect(parentAccountNameTitle).toHaveTextContent(
      'features.account.search.searchResult.parentAccountHeader:'
    );
    expect(parentAccountName).toHaveTextContent('ACE Hardware');
    expect(parentAccountNumber).toHaveTextContent('001234');
  });
});
