{"common": {"milwaukeeTool": "Milwaukee Tool", "onePoint": "OnePoint", "logo": "Logo", "other": "<PERSON><PERSON>", "optional": "opcional", "optionalWithParenthesis": "(opcional)", "remove": "Remover", "view": "<PERSON>er", "loading": "cargando...", "confirm": "Confirm", "greeting": "Hi {{name}}!", "next": "Next", "branch": "Branch", "customer": "Cliente", "serviceContact": "Contacto de servicio", "orderNumber": "Order #", "edit": "Edit", "id": "ID: {{id}}", "company": "Company", "phone": "Phone", "trades": "Trades", "email": "Email", "inPerson": "En-persona", "select": "Select", "back": "Back", "pressEnterToAccept": "Press ENTER to accept", "pressEnterToSave": "Presione ENTER para guardar", "save": "Save", "cancel": "<PERSON><PERSON><PERSON>", "unreadable": "Unreadable", "saveAndSelect": "Guardar & seleccionar", "saveAndView": "Guardar & ver", "dateCannotBeFuture": "Date can't be in the future", "required": "*", "savedInformation": "Information saved successfully", "confirmationButton": "<PERSON>, estoy seguro", "none": "<PERSON><PERSON><PERSON>", "backToHome": "Volver a la página de inicio", "pageNotFound": "Página no encontrada", "esckeyMessage": "Tap ESC key to go back", "snackBarError": "Oops! Estamos experimentando problemas técnicos. Por favor refresca la página e inténtalo nuevamente.", "date": "Date", "saveForLater": "Guardar para más tarde", "delete": "Bo<PERSON>r", "allDone": "Listo", "yesExit": "Si, salir", "areYouSureToExitThisPage": "¿Estás seguro de que quieres salir de esta página?", "continue": "<PERSON><PERSON><PERSON><PERSON>", "dotChar": ".", "loadMore": "<PERSON>gar más", "confirmAndSubmit": "Confirmar y enviar", "submitAndAuthorize": "Enviar y autorizar", "colonChar": ":", "paymentMethod": "Método de pago", "transactionDetails": "Detalles de la transacción", "orderFulfillmentDetails": "Detalles del cumplimiento de la orden", "resendEmail": "<PERSON><PERSON><PERSON><PERSON> correo", "creditCard": "Tarjeta de crédito", "cash": "Efectivo", "inviteToPay": "Invitación de pago", "payAtPickup": "Pagar al recoger", "poNumber": "PO number", "itemCount": "Artí<PERSON>lo {{current}} de {{all}}", "payNow": "<PERSON><PERSON> ahora", "close": "<PERSON><PERSON><PERSON>", "clear": "Limpiar", "scan": "Escanear", "expand": "Expandir", "collapse": "Colapsar", "discardChanges": "Descartar <PERSON>", "submit": "Enviar", "advancedSearchTooltip": "Búsqueda avanzada", "description": "Descripción", "empty": "<PERSON><PERSON><PERSON>", "serviceCategory": "Categoría de servicio", "itemPrice": "Precio unitario", "quantityAbbreviation": "Cantidad", "workInProgress": "Trabajo en proceso", "notProvided": "No proveído", "profileIncomplete": "Perfil incompleto", "profileIncompleteTooltip": "Falta información obligatoria en este perfil. Por favor, actualice el perfil del cliente para continuar con este pedido", "addItem": "Agregar elemento", "removeItem": "Remover elemento", "repair": "Reparar", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replacement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sku": "S<PERSON>", "quantity": "Cantidad", "notes": "Notas", "total": "Total", "saveAsDraft": "Guardar borrador", "subtotal": "Sub total", "guest": "<PERSON><PERSON><PERSON><PERSON>", "moneyQuantity": "${{amount}}", "somethingWentWrong": "Oh, algo salio mal", "couldNotLoadCardContent": "No pudimos cargar el contenido de esta tarjeta", "apply": "Aplicar", "information": "Información", "goBack": "Regresar", "undo": "<PERSON><PERSON><PERSON>", "yesText": "Si", "noText": "No", "taxExempt": "Exento de impuestos", "n/a": "N/A", "system": "Sistema", "unknown": "Desconocido", "unitPrice": "Precio unitario", "taxes": "Impuestos", "accountSuccessfullyUpdated": "Cuenta actualizada correctamente!", "accountAuthorizationNumberLabel": "Número de autorización de cuenta", "status": "Estado", "all": "Todos"}, "appInsights": {"errorBoundary": {"genericError": "Something went wrong"}}, "auth": {"unauthorized": "No autorizado", "rolePicker": {"role": "Role", "Admin": "Admin", "BranchManager": "Branch Manager", "BranchAssociate": "Branch Associate", "Hub": "<PERSON><PERSON>", "CX": "CX", "JSS": "JSS", "DigitalCare": "Digital Care", "ProductMarketing": "Product Marketing"}}, "design": {"molecules": {"navbar": {"openSettings": "Open settings"}, "textField": {"charactersRemaining": "{{count}} caracteres restantes"}}}, "features": {"account": {"search": {"searchResult": {"parentAccountHeader": "<PERSON><PERSON>ta padre", "siteNumber": "Núero de sitio", "serviceContact": "Contacto de servicio", "noServiceContact": "Sin contacto de servicio"}, "searchResultsMoreThan50": "Su búsqueda arrojó demasiados resultados, refine los parámetros de ubicación"}, "details": {"pageTitle": "Detalles de la cuenta", "accountParentName": "Parent account—{{accountName}}", "accountNumber": "Account number: {{accountNumber}}", "siteNumber": "Site number:", "accountNumberSubTitle": "Número de cuenta:", "taxExempt": "Tax exempt", "redTote": "Red Tote", "nationalAccountHold": "National account hold", "serviceContact": "Service contact", "serviceContactEmail": "Contacto del programa de servicio", "salesRep": "Sales rep", "billingContact": "Billing contact", "accountManager": "Account manager", "paymentTerms": "Payment terms", "poAccepted": "PO accepted", "creditCardAccepted": "Credit card accepted", "inviteToPayAccepted": "Invite to pay accepted", "transactionHistory": "Transaction history", "accountNotFound": "Cuenta no encontrada", "noServiceProgramBadge": "Sin programa de servicio", "contactDetailsModalTitle": "Datos de contacto de la orden de servicio", "responsibleParty": "Parte responsable *", "noUserSelected": "No tienes ningún perfil de usuario seleccionado para el servicio", "selectProfile": "Selecciona un perfil", "dropOffPartyOptional": "Fiesta de entrega (opcional)", "dropOffPartyPlaceHolder": "Nombre *", "profilePageTitle": "Seleccione el contacto para la orden de servicio", "salesOrderProfilePageTitle": "Seleccionar perfil de pedido de venta", "differentProfile": "Selecciona un perfil diferente", "responsiblePartyTooltip": "La parte responsable es el contacto principal para las actualizaciones de las órdenes de servicio y la persona responsable de las decisiones de reparación", "dropOffParty": "Fiesta de entrega:"}}, "branch": {"branchModal": {"currentBranch": "Select your current branch", "personalizeExperience": "Personalize your experience and access branch-specific information.", "saveDefault": "Save this location as my default", "selectServiceBranch": "Select a service branch"}, "branchRedirect": "Estás siendo redirigido—Selecciona tu sucursal de servicio actual para continuar con este pedido", "serviceBranch": "Service Branch", "serviceHub": "Service Hub"}, "home": {"mainButtons": {"startRepairOrder": "New Service Request", "startSaleOrder": "Start a sale", "customerLookUp": "Búsqueda de cliente", "createProfile": "Create new profile", "newServiceRequest": "Nueva solicitud de servicio", "scanQROrSR": "Escanear QR o SR", "accountLookup": "Búsqueda de cliente", "newToteOrder": "New Tote Order", "guestCheckout": "Compra como invitado"}, "welcomeModal": {"welcome": "Welcome to OnePoint!", "setup": "Let's get things set up for you."}, "savedOrdersNotification1": "You have 1 or more Saved Orders that were not completed. View your", "savedOrders": "Saved Orders", "savedOrdersNotification2": "to finish your work.", "profile": "Perfil", "account": "C<PERSON><PERSON>", "dashboard": "Dashboard", "logout": "<PERSON><PERSON><PERSON>"}, "search": {"searchBy": "Búsqueda de solicitud de orden", "selectAProduct": "Select a Product", "searchByCustomer": "Busca por correo electrónico o teléfono", "searchByOrder": "Search by order #, SKU, date", "searchByProduct": "Search by product name, keyword, serial number, or scan by UPC", "recordsFound": "records found", "noRecordsFound": "No records found", "searchByHome": "Buscar o escanear por número de serie, número de seguimiento o número de orden", "selectProductToContinue": "Select a product to continue", "selectCustomer": "Selecciona una cuenta o un cliente", "errorMessage": "Por favor agrega el {{fieldName}}", "serviceRequestLookupTitle": "Búsqueda de solicitud de orden", "serviceRequestLookupPlaceholder": "Buscar o escanear por número de serie, número de seguimiento o número de orden", "advancedSearch": {"titleAdvancedSearch": "Búsqueda avanzada", "filterRepairOrigin": {"title": "Origen de la reparación", "eServiceOption": "eService", "serviceBranchOption": "Selecciona una sucursal de servicio", "oneKeyOption": "OneKey"}, "filterRepairLocation": {"title": "Ubicación de reparación", "repairLocationOption": "Selecciona una ubicación de reparación"}, "filterOrderStatus": {"title": "Estado del pedido"}, "filterDateRange": {"title": "<PERSON><PERSON>", "errors": {"startDateInvalid": "La fecha de inicio no es válida", "endDateInvalid": "La fecha de fin no es válida", "startDateLater": "La fecha de inicio no puede ser posterior a la fecha de fin", "selectStartDate": "Seleccione la fecha de inicio", "selectEndDate": "Seleccione la fecha de fin"}}, "buttonReset": "Limpiar", "buttonApplyFilters": "Aplicar filtros"}, "orderSearchFilter": {"title": "Filtrar por"}}, "order": {"start": "Start a Service Order", "repairItems": "Repair Items", "searchByOrder": "Search by order #, SKU, date", "searchByProduct": "Search by product name, keyword, serial number, or scan by UPC", "eServiceOrderType": "eService Order", "branchOrderTypeGroup": "Service branch group ID", "supportCenterOrderTypeGroup": "eService group ID", "oneKeyOrderTypeGroup": "OneKey group ID", "branchOrderType": "Service branch order", "supportCenterOrderType": "eService order", "oneKeyOrderType": "OneKey order", "eServiceAddress": "Shipping Address", "repairOriginOrderCard": "Origen de Reparación", "dateSubmitted": "Date submitted", "print": "Imprimir", "orderStatus": "Estado de orden", "toolsCounterText": "Tools in this order", "viewOrder": "View Order", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "processOrder": "Procesar orden", "requiresAttention": "This order requires your attention", "startRepair": "Start a repair", "startRealtimeRepair": "Servicio en tiempo real", "addToCart": "<PERSON><PERSON><PERSON> al <PERSON>", "repairDetailsComplete": "Detalles de reparación completos", "serviceCartHeader": "Carrito de servicio", "repairItemAddedToCart": "¡Artículo de reparación agregado al carrito!", "dateStarted": "Día de Inicio", "completeOrder": "Completar orden", "savedOrdersCount": "<PERSON><PERSON><PERSON> grabadas ({{itemsCount}})", "savedOrders": "<PERSON><PERSON><PERSON> grabadas", "repairOrigin": "Ubicación de origen", "repairLocation": "Ubicacion de reparación", "shelfLocation": "Ubicación de anaquel", "shelfLocationPlaceHolder": "Ubicación de anaquel (opcional)", "deliveryType": "<PERSON><PERSON><PERSON><PERSON>", "customerReferenceId": "Id de referencia del cliente", "masterTracking": "Número de seguimiento", "masterTrackingNumber": "Número de seguimiento maestro (opcional)", "poNumberInputPlaceHolder": "Número de orden de compra (opcional)", "accountAuthorizationNumberPlaceHolder": "Número de autorización de la cuenta (optional)", "accountNumberInputPlaceHolder": "Número de cuenta (opcional)", "repairCompleted": "Fecha de finalización", "repairTypeSummary": "[RepairType] summary", "warrantyEvaluation": "Nuestro técnico capacitado en fábrica ha completado su evaluación de garantía. ", "customerDecisionType": "Decisión del cliente", "enteredBy": "Ingresado por", "customerDecisionDate": "Fecha de decisión del cliente", "decisionDate": "Fecha de decisión", "modeOfContact": "Modo de contacto", "authorizedBy": "Autorizado por", "itemsTableButton": "Tabla detallada incluyendo partes y trabajo si fuera aplicable", "repairOrderDetails": "Detalle de órdenes de reparación", "editRepairOrderDetails": "Editar detalles de reparación", "decisionNotes": "Notas de reparación:", "subTotal": "Total parcial", "salesTax": "impuesto sobre las ventas", "estimatedSaleTax": "Impuesto sobre las ventas estimado", "amountDue": "Amount Due", "totalAmountDue": "Monto total adeudado", "changeAmountDue": "Cambio debido", "confirmPickup": "Confirmar recojo", "pickupParty": "Pickup party", "pickupDate": "<PERSON><PERSON> de recojo", "pickupPartyInitialsOptional": "Iniciales de la parte de recogida (opcional)", "pickupPartyInitialsPlaceholder": "Ingrese solo las iniciales de la parte de recogida (opcional)", "createEstimate": "<PERSON><PERSON><PERSON> estimado", "viewEstimate": "Ver estimado", "viewPrimaryRequest": "Ver orden de servicio principal", "serviceOrder": "Orden de servicio", "expandMultiToolOrder": "Expandir para ver herramientas de la orden", "replacementItem": "<PERSON><PERSON> de remplazo", "warrantyApproved": "Garantía aprobada", "warrantyDenied": "Garantía denegada", "warrantyPending": "Garantía pendiente", "repairByReplacement": "RBR", "emailReceipt": "Enviar recibo", "emailReceiptMessage": "Este recibo será enviado a:", "emailReceiptPlaceholder": "Correo electrónico", "confirmEmailReceipt": "Confirmar y enviar", "receiptSent": "El recibo ha sido enviado!", "orderCompleted": "Tu orden de reparación se marcó correctamente como completa!", "addedItemsToServiceCart": "Agregaste ({{itemsCount}}) artículos a tu carrito de servicio!", "orderCompleteHeader": "<PERSON><PERSON> completada", "markOrderComplete": "Marcar pedido como completado", "markMultiToolOrdersComplete": "Marcar ({{ordersCount}}) pedidos como completados", "printReceipt": "Imprimir recibo", "startReturn": "Start a return", "printAndEmailReceipt": "Ambos", "abandonToolPickupQuestion": "¿Abandonar la recogida de herramientas?", "deniedWarrantyDescription": "Después de la evaluación realizada por nuestros técnicos de reparación capacitados y certificados, hemos determinado que esta herramienta no califica para la cobertura de la garantía debido a ", "deniedWarrantyChooseAnOption": "Seleccione una opción para continuar con su pedido:", "deniedWarrantyReturnActionTitle": "Regresar", "deniedWarrantyReturnActionDescription": "Su herramienta le será devuelta sin reparar.", "deniedWarrantyRepairActionTitle": "Reparar", "deniedWarrantyRepairActionDescription": "Su herramienta será reparada con nuestra tarifa de reparación Lightning Max.", "deniedWarrantyRecycleActionTitle": "Reciclar", "deniedWarrantyRecycleActionDescription": "Su herramienta será reciclada en nuestras instalaciones y no será devuelta.", "deniedWarrantyWarning1": "Una vez que haya presentado su decisión, ", "deniedWarrantyWarning2": "no se puede cambiar", "deniedWarrantyWarning3": ". Si no se toma ninguna decisión después ", "deniedWarrantyWarning4": "35 días hábiles ", "deniedWarrantyWarning5": "y ", "deniedWarrantyWarning6": "(3) <PERSON><PERSON> ", "deniedWarrantyWarning7": "por Experiencia del Cliente para llegar a usted, ", "deniedWarrantyWarning8": "su herramienta será reciclada automáticamente", "paymentMethodModalTitle": "Método de pago de la herramienta de reparación", "paymentMethodModalInstruction": "Seleccione un método de pago para continuar con la reparación de esta herramienta.", "paymentMethodModalInputHint": "Selecciona el método de pago", "paymentMethodLegend1": "Su herramienta será reparada por técnicos certificados de Milwaukee Tool. Se le cobrará utilizando el método de pago seleccionado:", "paymentMethodLegend2": "Una vez que se ha presentado una decisión, no se puede cambiar. ¿Está seguro de que desea enviar esta decisión?", "recipientEmailAddress": "Dirección de correo electrónico del destinatario", "customerWarrantyDecisionType": "Tipo de decisión del cliente", "customerWarrantyDecisionEnteredBy": "Introducido por", "customerWarrantyDecisionDate": "Fecha de decisión del cliente", "paidOn": "Pagado el", "requestedOn": "Solicitado el", "completedOn": "Completado el", "paymentAuthorizedOn": "Pago autorizado el", "nationalAccountNumber": "Número de cuenta nacional", "amountPaid": "Monto pagado: ", "noPaymentMethod": "No se ha seleccionado ningún método de pago", "totalPricePickup": "Total de recogida de múltiples herramientas: ", "paymentInviteSent": "Invitación de pago enviada el: ", "returnModalTitle": "Confirmación de devolución de herramienta", "returnModalDescription": "Su herramienta le será devuelta desarmada en múltiples piezas, sin empaque y sin reparar. Según la política de seguridad de Milwaukee Tool, no podemos devolver herramientas rotas o no reparadas para evitar daños o muerte.", "returnModalQuestion": "Una vez que se ha presentado una decisión, no se puede cambiar. ¿Está seguro de que desea enviar esta decisión?", "recycleModalTitle": "Confirmación de reciclaje de herramienta", "recycleModalDescription": "Su herramienta será reciclada en nuestras instalaciones y no será reparada ni devuelta a usted.", "recycleModalQuestion": "Una vez que se ha presentado una decisión, no se puede cambiar. ¿Está seguro de que desea enviar esta decisión?", "repairActionModalTitle": "Confirmación de reparación de herramienta", "repairActionModalDescription": "Su herramienta será reparada por técnicos certificados de Milwaukee Tool. Se le cobrará utilizando el método de pago seleccionado. ¿Desea continuar?", "totalEstimate": "Estimado total", "lmrEstimate": "LMR estimado", "taxEstimate": "Est. Tax", "modeOfContactInputHint": "Modo de contacto", "authorizedByInputHint": "Autorizado por", "notesInputHint": "Notas (opcional)", "inRepairStatus": "En reparación", "pickupStatus": "Listo para recoger", "unknownStatus": "Desconocido", "deniedWarrantyStatusText": "En reparación—se requiere acción", "processMultipleOrders": "Procesar múl<PERSON>les pedidos ({{ordersCheckedLength}})", "bulkOrderPickup": "Recogida de pedidos al por mayor ({{ordersToPickupLength}})", "continueToPayment": "Continuar con el pago", "cashReceived": "Efectivo recibido", "readyForPickup": "Listos para recoger", "operationalReplacement": "OPR", "decisionSnackbarMessage": "{{decisionType}} decisión enviada exitosamente!", "taxesLabel": "Est. imp<PERSON><PERSON>", "addRepairLines": "Formulario de Reparación", "deniedWarrantyModalTitle": "Formulario de Garantia Denegada", "selectResolution": "Seleccione una resolución", "selectRepairType": "Seleccione un tipo de reparación", "selectOpeningStatement": "Seleccion un enunciado de apertura", "selectDiagnosisArea": "Seleccione un área de diagnóstico", "selectDiagnosisCode": "Seleccione un código de diagnóstico", "selectSymptomArea": "Seleccione un área de síntoma", "selectSymptomCode": "Seleccione un código de síntoma", "supportCenterOriginLocation": "eService", "oneKeyOriginLocation": "OneKey", "supportCenterReturnLocation": "<PERSON><PERSON>no", "editDetails": "<PERSON><PERSON>", "nonWarrantyDetails": "Detalles de la garantía", "repairLinesUpdated": "Formulario de reparación actualizado exitosamente", "repairLinesUpdateFailed": "Formulario de reparación no pudo ser actualizado", "orderSubmitted": "Orden enviada exitosamente", "orderSubmitFailed": "Orden no pudo ser enviada", "orderDetailsSaved": "Detalles del pedido guardados correctamente!", "orderSubmittedTitle": "Orden enviada", "serviceRequestNumber": "Número de solicitud", "insufficientCashError": "por favor, ingrese un número igual o superior al monto adeudado", "serviceOrderNotCreatedWarning": "Su orden de reparación no ha sido enviada. Por favor inténtalo de nuevo", "decisionNotesGenerated": "Esta es una decisión generada por el sistema debido a la falta de respuesta del cliente después de 35 días.", "inboundTrackingNumber": "Número de rastreo", "outboundTrackingNumber": "Número de rastreo saliente", "payWithDifferentMethod": "Pagar con un método diferente", "serviceOrderStates": {"onePointStates": {"submittedStatus": "Enviado", "inTransit": "En Tránsito", "receivedAtHub": "Recibido En El Centro", "inRepairStatus": "En Reparación", "shipped": "<PERSON><PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON><PERSON>", "pickupStatus": "Listo para retirar", "orderComplete": "<PERSON><PERSON> completada", "pendingStatus": "Pendiente", "orderCanceled": "Cancelada", "actionRequiredStatus": "En reparación - acción requerida"}, "eServiceStates": {"submittedStatus": "Enviado", "inTransit": "En Tránsito", "receivedAtHub": "Recibido En El Centro", "inRepairStatus": "En Reparación", "shipped": "<PERSON><PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON><PERSON>", "pickupStatus": "Listo para retirar", "orderComplete": "Completado"}, "oracleStates": {"created": "Pendiente", "submitted": "Enviado", "new": "Nuevo", "shippedtohub": "Enviar a Ubicación de Reparación", "receivedathub": "Recibido en Ubicación de Reparación", "onekeyhold": "Retención de OneKey", "authorizeforscrap": "Autorizar Para Desechar", "holdingforparts": "Esperando Por Partes", "pendingauthorization": "Autorización Pendiente", "authorizetorepair": "Autorizar Para Reparar", "postedstageforshipment": "Etapa Publicada Para Envío", "techevaluation": "Evaluación Técnica", "awaitingnationalauthorizationnumber": "Esperando Número de Autorización Nacional", "provideestimate": "Proporcionar Estimado", "cancel": "<PERSON><PERSON><PERSON>", "repaircomplete": "Reparación Completa", "scrap": "DESECHO", "repaircompleteawaitingcustomerpickup": "Esperando Recogida del Cliente", "repaircompleteshiptocustomer": "Enviar al Cliente", "srshipwillcall": "Envío - Se Llamará"}}, "completeOrderFailed": "No se pudo completar la(s) orden(es).", "processInviteToPayFailed": "No se puede procesar la Invitación al pago este momento", "processInviteToPayCompleted": "Recogida de la orden de servicio completa", "noServiceOrderNumberLabel": "Número de orden de servicio pendiente", "transitDetails": "Detalles del tránsito del pedido", "decisionDetails": "Detalles de la decisión no garantizada", "payments": {"terminalModal": {"modalHeader": "Selección de datáfono Square", "modalDescription": "Seleccione uno de los siguientes datáfonos para procesar el pago con tarjeta de crédito:", "selectAndPay": "Seleccione y pague"}, "payWithSquare": "Pagar con Square", "terminalStatusModal": {"processingPayment": "Tratamiento", "pleaseWait": "<PERSON>spere por favor...", "paymentCanceled": "Pago cancelado", "paymentCompleted": "Terminado", "verifyCheckout": "Verificar pago"}}, "detail": {"uploadTaxExemptCertificate": "Subir certificado de exención de impuestos", "taxExemptCertificateButtonText": "Certificado de exención de impuestos", "uploadDocument": "Subir documento", "startServiceCredit": "Crédito de servicio", "evaluationOverview": "Resumen de evaluación", "cart": "<PERSON><PERSON> ({{count}})", "totalCreditAvailable": "Crédito total disponible", "itemPrice": "Precio del artículo", "creditItemAmount": "${{amount}}"}, "toolRetrievedOnGivenDate": "Herramienta recuperada {{retrievalDate}}", "toolRetrievedComplete": "Completado", "toolRecycledOnGivenDate": "Herramienta reciclada {{recycledDate}}", "toolRecycledComplete": "Completado", "lastUpdated": "Ultima actualización—{{date}}", "toolRecycled": "Herramienta reciclada—{{date}}", "toolRetrieved": "Herramienta recuperada—{{date}}", "taxResponseError": "Error al calcular impuestos", "creditCardAuthorized": "Tarjeta de crédito autorizada", "creditCardAuthorizationPending": "Autorización pendiente", "creditCardAuthorizedOn": "Fecha de autorización", "serviceCredit": {"serviceCreditHeader": "Crédito de servicio", "creditAmount": "Monto del crédito", "applyCredit": "Aplicar crédito", "summary": "Resumen de crédito de servicio", "transaction": "Transacción de crédito de servicio", "noChargesError": "No se pudieron recuperar los cargos del pedido de servicio", "applyServiceCreditError": "Se ha producido un error al aplicar su crédito de servicio. Si persiste, póngase en contacto con nuestro equipo de soporte.", "serviceCreditLimitExceeded": "El monto del crédito de servicio excede el límite de crédito de servicio disponible.", "negativeCreditError": "El monto del crédito actual debe ser mayor que 0. Revise las cantidades/selecciones de su cargo."}, "serviceCreditDate": "Crédito de servicio {{serviceCreditDate}}", "salesOrderReturn": "Sales return order", "return": {"restock": {"recall": "Recall Product", "recon": "30 Day Satisfaction", "reject": "Return to customer", "scrap": "Scrap Product", "restock": "Return to Inventory", "placeholder": "Restock disposition"}, "paymentMethod": "Payment method", "taxCalculationPending": "pending", "plusTax": "plus tax", "refundAmount": "Refund amount", "confirmReturn": "Confirm return", "completeReturn": "Complete return", "returnItemsSelectedWarning": {"start": "You're selecting ", "items": "{{count}} item(s)", "end": ". Please, review before submitting."}, "error": "An error has occurred trying to create your return sales order. If it persists, please contact our support team.", "returnCreatedSuccess": "Sales return order {{returnOrderNumber}} created successfully"}, "ErrorPricingRequestMessage": "No se puede recuperar el precio de este pedido, inténtelo de nuevo más tarde."}, "product": {"serialNumber": "Serial number", "productSerialNumber": "Product serial number", "realTimeItems": "elementos de servicio en tiempo real", "realTimeItemsLabel": "Habilitar servicio en tiempo real", "realTimeService": "Servicio en tiempo real", "inputPlaceholder": "Ingrese o escanee un número de serie de 7 a 21 dígitos", "realTimeServiceSummary": "Resumen de orden en tiempo real", "uploadReceipt": "Cargar comprobante", "serialNumberUnreadable": "Serial number unreadable", "describeProblem": "Describe the problem with this tool", "selectAllThatApply": "Select all that apply:", "customerReferenceId": "Customer reference ID (optional)", "customerReferenceIdPlaceHolder": "Ex. Fred's Handyman Services", "proofOfPurchase": "Proof of purchase", "proofOfPurchaseIncluded": "Included", "proofOfPurchaseNotIncluded": "Not included", "purcharseDateOptional": "<PERSON>cha de compra (opcional)", "yourFileWasUploaded": "Your file was successfully uploaded!", "detectedPurchaseDate": "<PERSON><PERSON>", "inclusionsTitle": "Included items", "lmrText": "Lightning Max Repair Est.", "problemDescription": "Problemas", "inclusionsDescription": "Inclusiones", "serialNumberHelperText": "Digite un número de serie de a 7 a 21 digitos o selecciona 'Número de serie no leible'", "replaceFile": "Reemplazar archivo", "provideValidPurchaseDate": "Por favor introduzca una fecha de compra válida", "replacementItemSerialRestrictionTooltip": "Se debe registrar el número de serie del artículo de reemplazo para marcar el pedido completo", "partialSerialNumberPlaceholder": "Introduzca un asterisco (*) para caracteres ilegibles.", "suggestedItem": "<PERSON><PERSON><PERSON><PERSON>", "suggestedReplacementItem": "Artículo de remplazo su<PERSON>ido", "selectADifferentItem": "Seleccionar un artículo diferente", "selectAReplacementItem": "Seleccionar un artículo de reemplazo", "warrantyRequest": "Solicitud de garantía", "nonWarranty": "Sin garantía", "deniedWarrantyLMROption": "LMR", "deniedWarrantyQuoteOption": "Cotización", "nonWarrantyHelperText": "Seleccione una opción de garantía", "warranty": "Garantía", "repairRequest": "Solicitud de reparación: {{repairRequestStatus}}", "editSerialNumber": "Editar número de serie", "editProofOfPurchase": "Editar comprobante de compra", "selectedItem": "<PERSON><PERSON><PERSON><PERSON>", "uploadProofOfPurchaseModalTitle": "<PERSON>gar comprobante de compra", "uploadProofOfPurchaseDropzoneText1": "Arrastra y suelta tu archivo aquí", "uploadProofOfPurchaseDropzoneText2": "O", "uploadProofOfPurchaseSnackbarMessage": "Recibo adjunto satisfactoriam<PERSON>", "describeProblemPlaceHolder": "Describe el problema o añade comentarios", "problemIsNotProvided": "Problema no proveído", "problemIsNotProvidedLabel": "No proveído", "noProductData": "No hay datos de Producto", "replacementReasonLabel": "Código de la razón del reemplazo *", "serviceCategory": "Categoría de servicio *", "realTimeReplaceSerialNumberLabel": "Número de serie del producto *", "realTimeReplaceSerialNumberPlaceholder": "Ingrese o escanee un número de serie de 7 a 21 dígitos", "replacementReason": "Razón del remplazo", "serviceCategoryLabel": "Categoría de servicio", "realTimeReplaceSerialNumberTooltip": "Se debe ingresar el número de serie del artículo de reemplazo para completar el pedido", "realTimeReplaceSerialNumberHelperText": "Ingrese un número de serie de 7 a 21 dígitos O ingrese un asterisco (*) para caracteres ilegibles", "checkoutFieldsTitle": "Campos de pago"}, "customer": {"lookup": "Customer lookup", "profileTabs": {"toolBox": "Toolbox", "serviceHistory": "Service history", "engagement": "Engagements", "notes": "Notes"}, "profileForm": {"firstName": "First name", "lastName": "Last name", "emailAddress": "Email address", "phoneNumber": "Phone number", "billingAddress": "Billing address", "country": "Country", "addressLine1": "Street address", "addressLine2": "Apt, suite, etc (optional)", "addressLine3": "Address line 3", "city": "City", "state": "State or Province", "postalCode": "Postal code", "companyName": "Company name", "businessOrCompanyNameOptional": "Nombre del negocio (opcional)", "preferredLanguage": "Preferred language", "additionalAddressLine": "+ Additional address line", "preferredContactMethod": "Preferred method of contact", "creationSuccess": "Customer profile successfully created", "duplicateEmail": "Ya existe un usuario con esta dirección de correo electrónico", "userNotFound": "Usuario no encontrado", "countryCode": "Código"}, "primaryAddress": "Primary address", "customerDetails": "Customer details", "contactInfo": "Info de contacto", "customerName": "Nombre de cliente", "customerProfile": "Perfil de cliente", "editModalTitle": "<PERSON><PERSON> perfil", "search": {"accountSearchToggle": "Búsqueda de sitio", "customerSearchToggle": "Búsqueda de perfiles", "orSeparator": "O", "customerNameInputPlaceHolder": "Nombre *", "customerLastNameInputPlaceHolder": "Apellido *", "accountSearchInputPlaceHolder": "Buscar por nombre del sitio o (#) número de sitio *", "zipCodeSearchInputPlaceHolder": "Ingrese ciudad, estado, provincia o código postal", "zipCodeSearchInputEmptyError": "Ingrese ciudad, estado, provincia o código postal", "continueAsGuest": "Continuar como invitado", "parentAccountNameOptional": "Nombre de la cuenta principal (opcional)", "cityOptional": "Ciudad (opcional)", "postalCodeOptional": "Código postal (opcional)", "partialEmailProvided": "La búsqueda por correo no admite parciales; ingrese el correo completo."}, "details": {"taxExemptLabelmodal": "Estado de exención de impuestos"}}, "repair": {"repairItems": "Artículos de reparación ({{itemsCount}})", "continueToCheckout": "Continuar a la caja", "cartIsEmpty": "El carrito está vacío", "orderCompleted": "Tu orden de reparación fue enviada satisfactoriamente!", "viewServiceAgreement": "Ver / imprimir acuerdo de servicio", "printServiceAgreement": "Imprimir acuerdo de servicio", "printServiceReceipt": "Imprimir recibo de servicio", "addItemToTote": "<PERSON><PERSON><PERSON> otro artí<PERSON>lo al bolso", "nextStep": "Algo más para este cliente?", "addRepairItem": "Añadir item de reparación", "serviceCartItems": "Artículos en el carrito de servicio ({{itemsCount}})", "serviceCartItems2": "<PERSON><PERSON> de servicio ({{itemsCount}})", "orderSummary": "Resumen del pedido", "orderSummaryItems": "Artículos en este grupo ({{itemsCount}})", "print": {"print": "Imprimir", "zebraTags": "Imprimir <PERSON> Zeb<PERSON>", "error": "Error al Imprimir: ", "selectPrinter": "Seleccione una impresora", "numberOfCopies": "Número de copias"}, "submitOrder": "En<PERSON><PERSON>", "repairLocation": "Sede de reparación", "noItemsSelected": "No tienes artículos seleccionados para reparación", "addItems": "Agregar artículo de reparación", "addWorkOrderItems": "Agregar elementos de la orden de trabajo", "addMoreItems": "Agregar más artículos de reparación", "fullSerial": "Serie completa", "partialSerial": "Serie parcial", "viewCustomerServiceHistory": "Ver historial de servicio del cliente", "addSelectedItems": "Agregar seleccionados ({{counter}})", "nonWarrantableItem": "Artículo no garantizado", "repairLinesWarning": "Por favor revise las selecciones cuidadosamente antes de enviar.", "confirmRepairDetails": "Confirmar detalles de reparación", "confirmDetails": "Confirmar de<PERSON><PERSON>", "workOrderItems": "Ordenes de trabajo:", "workOrderItemsNoColon": "Ordenes de trabajo", "attachWorkOrderItems": "Vincular ordenes de trabajo", "getPricing": "Obtener precios", "returnLocation": {"accordionTitle": "M<PERSON><PERSON><PERSON>", "selectServiceBranch": "Seleccione una rama de servicio *", "willCallLabel": "Llamará", "dropShipLabel": "Envío directo", "shipToLabel": "Envío a", "internationalShippingLabel": "Envío internacional", "branchAddressLabel": "Sucursal de servicio:", "customerAddressLabel": "Dirección principal:", "additionalAddressLabel": "Dirección adicional:", "AddNewAddressButton": "Agregar una nueva dirección", "additionalAddressModalTitle": "Dirección adicional", "saveButtonTextAdditionalAddressModal": "Guardar y seleccionar", "additionalAddressForm": {"addressNameLabel": "Nombre"}}, "repairLocationAlert": {"verbiage1": "Se detectó un cambio en la ubicación de reparación en el artículo: ", "verbiage2": "{{commaSeparatedListOfItems}}. ", "verbiage3": "Estás anulando una regla empresarial. ", "verbiage4": "Por favor, verifica que esto sea correcto ", "verbiage5": "antes de enviar el pedido."}, "subtotal": "Sub-total:", "itemPrice": "Precio del Item", "itemPriceTooltipMessage": "De click en 'Obtener Precios' para obtener el precio del item", "calculatePricingPiorContinue": "Calcular el precio y agregar un elemento de mano de obra antes de continuar", "repairLinesLabel": "Líneas de reparación", "messages": {"warningBeforeProceedToCheckoutHead": "¿Son correctos todos los detalles de la reparación?", "warningBeforeProceedToCheckoutContent": "Una vez que continúe con el pago, no se podrán editar los detalles."}, "realtime": {"orderComplete": "Orden de servicio completada!", "checkoutWarningTitleText": "¿Son correctos todos los detalles de la reparación?", "checkoutWarningDetailsText": "Una vez que continúe con el proceso de pago, no podrá editar los detalles", "changeDue": "<PERSON><PERSON> completado—Cambio pendiente: ${{change}}", "orderSummary": "Resumen del pedido", "pickupUpdateError": "Error al actualizar la información de recogida", "alertManyLaborItemsAddedTitle": "Ha añadido más de un elemento de trabajo. Solo se permite continuar con uno.", "alertManyLaborItemsAddedContent": "Elimine el elemento de trabajo adicional para continuar.", "alertReplacementItemChanged": "El artículo de reemplazo ha sido cambiado. Regrese al carrito de artículos de la orden de trabajo y recalcule el precio para continuar con el siguiente paso.", "addLaborB": "<PERSON><PERSON><PERSON> mano de obra B", "addLaborH": "<PERSON><PERSON><PERSON> mano de obra <PERSON>"}}, "error": {"unauthorizedAlertText": "No pudimos autenticar las credenciales del usuario. Por favor inicie sesión nuevamente.", "returnLoginBtnText": "Volver para iniciar sesión", "sessionExpired1": "¡Ope!", "sessionExpired2": "Por favor inicie sesión nuevamente"}, "hub": {"hubModal": {"currentHub": "Seleccione su centro de servicio actual", "personalizeExperience": "Personalice su experiencia y acceda a información específica del centro de servicio.", "saveDefault": "Guardar esta ubicación como mi predeterminada", "selectServiceHub": "Seleccione un centro de servicios"}}, "serviceRequestEstimate": {"itemPrice": "Precio unitario", "expectedPrice": "<PERSON><PERSON> estimado", "expectedPriceTooltip": "Los precios mostrados son solo estimaciones y no reflejan el costo final.", "removeOption": "Remover opción", "navbarTitle": "Estimación de solicitud de servicio", "addOption": "Agregar opción", "getPricing": "Obtener precios", "downloadPdf": "Descargar PDF", "optionsNeededForEstimate": "Opciones necesarias para la estimación", "noOptionsAreAvailableForAnEstimateYet": "No hay opciones disponibles para una estimación todavía. Por favor, proporciona al menos una opción para guardar un borrador.", "noItemsAreAvailableForAnOptionYet": "Un artículo es necesario para proceder con esta opción de estimación.", "duplicateOption": "Duplicar opción", "publishEstimate": "Publicar", "addToServiceOrder": "Agregar a orden de servicio", "publishSuccess": "Estimado publicado satisfactoriamente", "publishError": "Error al publicar el estimado. Por favor intenta de nuevo.", "createNewEstimate": "Crear nuevo estimado", "createNewVersion": "Crear nueva versión", "createNewVersionSuccess": "Nueva versión creada satisfactoriamente", "createNewVersionError": "Error al crear nueva versión. Por favor intenta de nuevo.", "currentVersion": "Versión actual: {{version}}", "lastActiveVersion": "Versión publicada: {{version}}", "downloadEstimateSuccess": "PDF de estimado descargado satisfactoriamente", "downloadEstimateError": "Error al descargar PDF de estimado. Por favor intenta de nuevo.", "downloadAgreementSuccess": "PDF de acuerdo de servicio descargado satisfactoriamente", "downloadAgreementError": "Error al descargar PDF de acuerdo de servicio. Por favor intenta de nuevo.", "associatedServiceRequests": "<PERSON><PERSON><PERSON> asociadas: {{count}}", "linkSV": "Asociar", "associatedServiceRequestSuccess": "Órden asociada satisfactoriamente", "associatedServiceRequestError": "Error al asociar la orden. Por favor intenta de nuevo.", "removeSecondaryServiceRequestSuccess": "Órden removida satisfactoriamente", "removeSecondaryServiceRequestError": "Error al remover la orden. Por favor intenta de nuevo.", "errorMessage": "<PERSON><PERSON><PERSON>", "getPriceErrorMessage": "Desafortunadamente, algo salió mal al obtener los precios. No podra generar generar el estimado sin los últimos precios.", "internalNotes": "Notas internas", "estimateOptionNumber": "Opción de estimado #{{number}}", "estimateQuote": "Cotización estimada", "associatedProblemDescription": "Problema", "externalNotes": "Notas externas", "addNote": "Agregar nota", "saveNote": "Guardar nota", "editNote": "<PERSON><PERSON> nota", "removeNote": "<PERSON><PERSON><PERSON> nota", "estimateNotePlaceholder": "Notas (opcional)", "noteTitle": "<PERSON>a"}, "salesOrder": {"startSale": "Iniciar una venta", "cart": "<PERSON><PERSON> ({{count}})", "noProductsSelected": "No tienes productos en tu carrito", "addProduct": "Añadir un producto", "continueToCheckout": "Continuar con el pago", "enterPromoCode": "Ingresar código promocional", "reviewAndPay": "Rev<PERSON>r y pagar", "orderSummary": "Resumen del pedido", "counterSale": "Venta de mostrador", "getPrice": "Obtener precio", "unitPrice": "Precio unitario", "orderItems": "Artículos del pedido", "salesOrderDetails": "Detalles del pedido de venta", "paymentDetails": "Detalles de pago", "orderCompleted": {"title": "Pedido enviado", "text": "¡Tu pago se realizó con éxito y tu pedido ha sido enviado!", "pendingPaymentText": "Su pedido ha sido enviado y está pendiente de pago.", "orderIdentifier": " Pedido de venta "}, "statuses": {"created": "<PERSON><PERSON><PERSON>", "paymentPending": "Pago pendiente", "submitted": "Enviado"}, "returnOrder": {"title": "Orden de devolución", "created": "Orden de devolución creada exitosamente.", "error": "Ocurrió un error al intentar crear su orden de devolución. Si persiste, por favor contacte a nuestro equipo de soporte.", "transaction": "Transacción de orden de devolución", "orderIdentifier": "Devolución de orden de venta", "returnNotice": "Artículo(s) en esta orden están configurados para ser devueltos"}, "refundAmount": "Monto de reembolso", "estPoReturnAmount": "Monto estimado de devolución de orden de compra", "promoCodeInvalid": "Código promocional inválido", "promoCodeApplied": "Código promocional aplicado:", "availableInventory": "Inventario: {{availableInventory}}", "inventoryCheckError": "Error al obtener el inventario para el SKU: {{sku}}"}}, "prompt": {"orderRepair": {"confirmationHeader": "Desea salir de la orden de reparación?", "confirmationMessage": "¿Está seguro de salir de la orden de reparación?"}, "removeOrderItem": {"confirmationHeader": "Remover artículo de la orden?", "confirmationMessage": "¿Está seguro de remover este artículo de la orden? Esta acción puede causuar que pierda su trabajo."}, "removeCartItem": {"confirmationHeader": "Remover del carrito?", "confirmationMessage": "¿Está seguro de que desea eliminar este artículo de reparación de su carrito y comenzar de nuevo?"}, "removeSavedOrder": {"confirmationHeader": "Delete saved order?", "confirmationMessage": "Are you sure you want to delete this from your saved orders?"}, "serviceRequestEstimate": {"confirmationHeader": "Desea publicar el estimado?", "confirmationMessage": "Esta seguro que desea publicar el estimado?", "newVersionHeader": "Crear una nueva versión", "newVersionMessage": "Esta seguro que desea crear una nueva versión del estimado?", "associateSVHeader": "Agregar orden de servicio", "associateSVMessage": "¿Está seguro que desea asociar la órden de servicio al estimado?", "removeSecondaryServiceRequestHeader": "Remover órden de servicio", "removeSecondaryServiceRequestMessage": "¿Está seguro que desea remover la órden de servicio del estimado?", "cancelEstimateEditingHeader": "Cancelar edición de estimado", "cancelEstimateEditingMessage": "¿Está seguro que desea cancelar la edición del estimado? Perderá todos los cambios no guardados."}, "endBulkOrderSession": {"confirmationHeader": "¿Terminaste de agregar artículos a este pedido al por mayor?", "confirmationMessage": "No podrá agregar más artículos al pedido si continúa."}}, "pages": {"customer": {"serviceHistory": "Service history", "salesHistory": "Sales history", "engagements": "Engagements", "reason": "Reason", "type": "Type", "status": "Status", "viewDetailsButton": "View details"}}}