'use client';

import {
  Loading,
  PageHeaderContainerWrapper,
  Snackbar
} from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { Account } from '@/features/account';
import { Location, useGetAllLocationsDataQuery } from '@/features/branch';
import { Customer } from '@/features/customer';
import { CurrencyType, PricingItem } from '@/features/pricing';
import {
  ProductDescription,
  SelectedProduct,
  useLazyGetProductsDescriptionBySkusQuery
} from '@/features/product';
import { onServiceCategoriesDataChange } from '@/features/repair';
import {
  DeliveryType,
  DeniedWarrantyDecision,
  EmployeeResponse,
  GroupResponse,
  hasPoNumberPaymentTerm,
  onPaymentMethodsChange,
  onTaxChange,
  OrderType,
  removeSelectedProduct,
  ServiceOrder,
  ServiceOrderDetails,
  ServiceOrderStatusCode,
  ServiceOrderStepCode,
  ServiceRequestCharge,
  useLazyGetCategoriesQuery,
  useLazyGetServiceOrderGroupQuery,
  useLazyGetServiceOrderPricingQuery,
  useUpdateServiceOrderMutation,
  WarrantyActionCode,
  WarrantyStatusFromRepairLines
} from '@/features/serviceOrder';
import { useAppDispatch } from '@/store/hooks';
import { SnackbarState } from '@/util';
import { useTranslation } from 'next-export-i18n';
import React, { useEffect, useMemo } from 'react';
import { useCalculatePaymentMethods } from '..';

interface Props {
  groupId: string;
  orderId: string;
  customer?: Customer;
  customerAccount?: Account;
  product?: SelectedProduct;
  isRepairByReplacement: boolean;
  pricingStrategy?: string | null;
  isOperationalReplacement: boolean;
  hasDeniedWarranty?: boolean;
  isOutOfWarranty: boolean;
  group: GroupResponse | undefined;
  serviceRequestNumber?: string;
  displayOrderStatus?: number;
  submittedDate?: string;
  pricingData?: PricingItem | undefined;
  estimateButtonText?: string;
  signalRTimestamp?: number | null;
  isGetServicePricingFetching?: boolean;
  getLmrPricing?: (sku: string) => void;
  setSubmittedDate: (value?: string) => void;
  onNextClick: (step: ServiceOrderStepCode) => void;
  onCreateEstimateClick: () => void;
  handleCustomerSearch: (id: string) => Promise<void>;
  onProductChange: (product: SelectedProduct) => void;
  onCloseClick: () => void;
  taxExemptCertLink?: string;
  currencyType: CurrencyType;
  showCreditCardAuthorized: boolean;
  showCreditCardAuthorizationPending: boolean;
  creditCardAuthorizedDate?: string;
  inviteToPayProcessed?: boolean;
  authorizedCreditCardLast4?: string;
  authorizedCreditCardBrand?: string;
  isInviteToPayWithoutPickup?: boolean;
  inviteToPayWithoutPickupDateRequested?: string;
}

export const ServiceOrderDetailsContainer = (props: Props) => {
  const { t } = useTranslation();
  const snackBarError = t('common.snackBarError');
  const dispatch = useAppDispatch();
  const selectedOrder = useMemo(() => {
    const order = props.group?.serviceOrders.find((x) => x.id == props.orderId);
    return order;
  }, [props.group?.serviceOrders, props.orderId]);
  const { calculatePaymentMethods } = useCalculatePaymentMethods();

  // #region Props
  const {
    handleCustomerSearch,
    onProductChange,
    setSubmittedDate,
    getLmrPricing,
    group,
    pricingData,
    isGetServicePricingFetching
  } = props;
  // #endregion

  // #region State hooks
  const [warrantyStatusFromRepairLines, setWarrantyStatusFromRepairLines] =
    React.useState<WarrantyStatusFromRepairLines>();
  const [isDeniedWarranty, setIsDeniedWarranty] = React.useState<
    boolean | undefined
  >(undefined);
  const [employee, setEmployee] = React.useState<EmployeeResponse>();
  const [order, setOrder] = React.useState<ServiceOrder>();

  const [onepointStatusId, setOnepointStatusId] =
    React.useState<ServiceOrderStatusCode>();

  const [eServiceStatusId, setEServiceStatusId] =
    React.useState<ServiceOrderStatusCode>();

  const [statusActiveId, setStatusActiveId] =
    React.useState<ServiceOrderStatusCode>(ServiceOrderStatusCode.Unknown);

  const [deniedWarrantyDecision, setDeniedWarrantyDecision] =
    React.useState<DeniedWarrantyDecision>();

  const [lastSignalRUpdate, setLastSignalRUpdate] = React.useState<
    number | null | undefined
  >(props.signalRTimestamp);

  const [snackbarState, setSnackbarState] = React.useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });

  const [selectedBranch, setSelectedBranch] = React.useState<
    Location | undefined
  >();

  const [purchaseNumber, setPurchaseNumber] = React.useState<
    string | undefined
  >(selectedOrder?.purchaseOrder);

  const [chargeProductDescriptions, setChargeProductDescriptions] =
    React.useState<ProductDescription[] | undefined>();
  // #endregion

  // #region Queries
  const [searchServiceOrderGroup] = useLazyGetServiceOrderGroupQuery();

  const { data: { locations } = {}, isError: isErrorSearchBranchesQuery } =
    useGetAllLocationsDataQuery();

  const [
    searchServiceOrderPricing,
    { isError: isSearchServiceOrderPricingError }
  ] = useLazyGetServiceOrderPricingQuery();

  const [updateServiceOrder] = useUpdateServiceOrderMutation();

  const [getCategoriesTrigger] = useLazyGetCategoriesQuery();

  const [getChargesProductDescriptions] =
    useLazyGetProductsDescriptionBySkusQuery();
  // #endregion

  const updatePoNumber = async (poNumber: string, orderId?: string) => {
    if (orderId) {
      setPurchaseNumber(poNumber);
      const response = await updateServiceOrder({
        orderId: orderId,
        body: {
          poNumber: poNumber
        }
      }).unwrap();

      if (
        response &&
        response.serviceOrder &&
        response.serviceOrder.purchaseOrder === poNumber
      ) {
        setOrder(
          (order) =>
            ({
              ...order,
              purchaseOrder: response.serviceOrder.purchaseOrder
            }) as ServiceOrder
        );
      }
    }
  };

  const getCategoriesData = React.useCallback(async () => {
    const response = await getCategoriesTrigger();

    if (response.data && response.data.categories.length > 0) {
      const data = response.data.categories.map((x) => {
        return { id: x.code, value: x.description };
      });

      dispatch(onServiceCategoriesDataChange(data));
    }
  }, [dispatch, getCategoriesTrigger]);

  const getProductDescriptions = React.useCallback(
    async (charges: ServiceRequestCharge[]) => {
      const skus = charges.map((charge) => charge.sku);
      const productDescriptions = await getChargesProductDescriptions(skus);

      if (
        productDescriptions.data?.products &&
        productDescriptions.data.products.length > 0
      ) {
        setChargeProductDescriptions(productDescriptions.data?.products);
      }
    },
    [getChargesProductDescriptions]
  );

  const handleCloseSnackbar = () => {
    setSnackbarState({
      ...snackbarState,
      open: false
    });
  };

  const handleApiError = React.useCallback(() => {
    setSnackbarState({
      message: snackBarError,
      severity: 'error',
      open: true
    });
  }, [snackBarError]);

  useEffect(() => {
    const operations = calculatePaymentMethods({
      deliveryType: selectedOrder?.deliveryType,
      isGenericAccount: !!selectedOrder?.isGenericAccount,
      hasPoNumberPaymentTerm: hasPoNumberPaymentTerm(
        selectedOrder?.shipToMetadata?.paymentTerms
      ),
      onepointStatusId: selectedOrder?.onepointStatusId,
      isItpConfirmationAuthorized: !!order?.oracleTrapReference
    });

    dispatch(onPaymentMethodsChange(operations));
  }, [
    calculatePaymentMethods,
    dispatch,
    selectedOrder,
    order?.oracleTrapReference
  ]);

  const getDataForOrderDetails = React.useCallback(async () => {
    if (!props.groupId && !props.orderId) {
      handleApiError();
      return;
    }

    const serviceGroup = props.group as GroupResponse;
    const order = props.group?.serviceOrders.find(
      (x) => x.id == props.orderId
    ) as ServiceOrder;
    setOrder(order);
    const selectedProduct = props.product as SelectedProduct;
    const deniedWarrantyDecision = selectedProduct.deniedWarrantyDecision;

    setDeniedWarrantyDecision(deniedWarrantyDecision);
    setEmployee(order.employee);
    setSubmittedDate(order.submittedDate);
    setIsDeniedWarranty(
      order.isDeniedWarranty ||
        order.warrantyStatusFromRepairLines ===
          WarrantyStatusFromRepairLines.Denied
    );
    setOnepointStatusId(order.onepointStatusId);
    setEServiceStatusId(order.eserviceStatusId);
    onProductChange(selectedProduct);

    if (getLmrPricing) {
      getLmrPricing(selectedProduct.sku);
    }

    const pickupWithoutTaxes =
      !!order?.serviceOrderPickup &&
      (order.serviceOrderPickup?.taxes ?? 0) <= 0;

    if (
      (order.onepointStatusId === ServiceOrderStatusCode.Pickup ||
        pickupWithoutTaxes) &&
      order.deliveryType === DeliveryType.WillCall &&
      order.serviceRequestCharges.length > 0
    ) {
      const taxResponse = await searchServiceOrderPricing({
        srNumbers: [order.serviceRequestNumber!]
      });
      if (!isSearchServiceOrderPricingError) {
        dispatch(onTaxChange(taxResponse.data!));
      }
    }

    setWarrantyStatusFromRepairLines(order.warrantyStatusFromRepairLines);

    await handleCustomerSearch(serviceGroup.customerId);

    if (serviceGroup.branchId && !isErrorSearchBranchesQuery && locations) {
      setSelectedBranch(locations.find((x) => x.id == serviceGroup.branchId));
    }

    await getCategoriesData();

    if (order.serviceRequestCharges.length > 0) {
      await getProductDescriptions(order.serviceRequestCharges);
    }
  }, [
    props.groupId,
    props.orderId,
    props.group,
    props.product,
    isErrorSearchBranchesQuery,
    isSearchServiceOrderPricingError,
    getLmrPricing,
    dispatch,
    searchServiceOrderPricing,
    handleCustomerSearch,
    onProductChange,
    setSubmittedDate,
    locations,
    handleApiError,
    getCategoriesData,
    getProductDescriptions
  ]);

  const handleCloseIconClick = () => {
    dispatch(
      removeSelectedProduct((props.product as SelectedProduct).selectionId)
    );
    props.onCloseClick();
  };

  const handleRepairActionTaken = async (
    product: SelectedProduct,
    orderId: string
  ) => {
    const orderGroupResponse = await searchServiceOrderGroup(props.groupId);
    if (orderGroupResponse.data) {
      const order =
        orderGroupResponse.data.serviceOrderGroup.serviceOrders.find(
          (x) => x.id === orderId
        ) as ServiceOrder;
      setEmployee(order?.employee);
      const deniedWarrantyDecision = product.deniedWarrantyDecision;
      setDeniedWarrantyDecision(deniedWarrantyDecision);
      setPurchaseNumber(order?.purchaseOrder);
    }
    onProductChange(product);
  };

  // #region Effect hooks
  React.useEffect(() => {
    if (props.displayOrderStatus !== undefined) {
      setOnepointStatusId(props.displayOrderStatus);
      setEServiceStatusId(props.displayOrderStatus);
    }
  }, [props.displayOrderStatus]);

  React.useEffect(() => {
    if (!order && props.group) {
      getDataForOrderDetails();
    }
  }, [order, props.group, getDataForOrderDetails]);

  React.useEffect(() => {
    if (
      props.signalRTimestamp &&
      props.signalRTimestamp !== lastSignalRUpdate
    ) {
      getDataForOrderDetails();
      setLastSignalRUpdate(props.signalRTimestamp);
    }
  }, [props.signalRTimestamp, getDataForOrderDetails, lastSignalRUpdate]);

  React.useEffect(() => {
    const getStatusActiveId = () => {
      if (
        deniedWarrantyDecision &&
        onepointStatusId !== ServiceOrderStatusCode.Submitted &&
        onepointStatusId !== ServiceOrderStatusCode.Pickup &&
        onepointStatusId !== ServiceOrderStatusCode.OrderComplete
      ) {
        return ServiceOrderStatusCode.InRepair;
      }

      if (group?.systemOrigin === OrderType.BranchOrder) {
        return onepointStatusId ?? 0;
      }

      return eServiceStatusId ?? 0;
    };

    setStatusActiveId(getStatusActiveId());
  }, [
    deniedWarrantyDecision,
    group,
    order,
    onepointStatusId,
    eServiceStatusId
  ]);

  React.useEffect(() => {
    if (selectedOrder?.purchaseOrder !== undefined) {
      setPurchaseNumber(selectedOrder.purchaseOrder);
    }
  }, [selectedOrder?.purchaseOrder]);
  // #endregion

  return (
    <>
      <PageHeaderContainerWrapper
        handleBackIconClick={handleCloseIconClick}
        handleCloseIconClick={handleCloseIconClick}
        serviceRequestNumber={props.serviceRequestNumber}
        pageTitle={t('features.order.repairOrderDetails')}
      />
      <PageAreaBox
        pageAreaBoxName="repairDetails"
        sx={{
          height: '100%',
          bgcolor: 'background.paper',
          mt: '88px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Snackbar
          open={snackbarState.open}
          message={snackbarState.message}
          severity={snackbarState.severity}
          handleClose={handleCloseSnackbar}
        />
        <Loading
          isLoading={!props.product || !props.customer}
          fallbackContainerProps={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <ServiceOrderDetails
            product={props.product as SelectedProduct}
            customer={props.customer as Customer}
            customerAccount={props.customerAccount}
            orderId={props.orderId}
            orderDateSubmitted={props.submittedDate}
            serviceOrderNumber={props.serviceRequestNumber ?? props.orderId}
            branch={selectedBranch}
            isRepairByReplacement={props.isRepairByReplacement}
            pricingStrategy={props.pricingStrategy}
            isOperationalReplacement={props.isOperationalReplacement}
            hasDeniedWarranty={isDeniedWarranty}
            isOutOfWarranty={props.isOutOfWarranty}
            onConfirmPickupClick={() =>
              props.onNextClick(ServiceOrderStepCode.Confirmation)
            }
            onCreateEstimateClick={props.onCreateEstimateClick}
            statusActiveId={statusActiveId}
            group={props.group}
            onProductChange={handleRepairActionTaken}
            employee={employee}
            isLmrPricingFetching={isGetServicePricingFetching}
            lmrPricing={pricingData}
            customerReferenceId={order?.toolNickname}
            masterTrackingId={order?.masterTrackingNumber}
            deliveryType={order?.deliveryType}
            warrantyStatusFromRepairLines={warrantyStatusFromRepairLines}
            estimateButtonText={props.estimateButtonText}
            order={order}
            currencyType={props.currencyType}
            onPoNumberSave={updatePoNumber}
            purchaseNumber={purchaseNumber}
            taxExemptCertLink={props.taxExemptCertLink}
            chargeProductDescriptions={chargeProductDescriptions}
            showCreditCardAuthorized={props.showCreditCardAuthorized}
            showCreditCardAuthorizationPending={
              props.showCreditCardAuthorizationPending
            }
            creditCardAuthorizedDate={props.creditCardAuthorizedDate}
            isInviteToPayWithoutPickup={props.isInviteToPayWithoutPickup}
            inviteToPayWithoutPickupDateRequested={
              props.inviteToPayWithoutPickupDateRequested
            }
            authorizedCreditCardBrand={props.authorizedCreditCardBrand}
            authorizedCreditCardLast4={props.authorizedCreditCardLast4}
            showBreakdownPayment={
              order?.repairDecision?.repairDecisionId !==
              WarrantyActionCode.Recycle
            }
          />
        </Loading>
      </PageAreaBox>
    </>
  );
};
