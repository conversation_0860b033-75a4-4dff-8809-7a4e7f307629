import { THEMES } from '@/design/atoms';
import { withThemeFromJSXProvider } from '@storybook/addon-styling';
import { CssBaseline, ThemeProvider } from '@mui/material';
import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport';
import type { Preview } from '@storybook/react';

const preview: Preview = {
  decorators: [
    withThemeFromJSXProvider({
      themes: THEMES,
      defaultTheme: 'light',
      Provider: ThemeProvider,
      GlobalStyles: CssBaseline
    })
  ],
  parameters: {
    viewport: {
      viewports: INITIAL_VIEWPORTS
    }
  }
};

export default preview;
