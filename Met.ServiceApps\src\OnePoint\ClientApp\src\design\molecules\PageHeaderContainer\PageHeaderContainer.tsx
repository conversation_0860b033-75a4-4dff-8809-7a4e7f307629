import { PageAreaBox } from '@/design/organisms';
import { SxProps } from '@mui/material';
import AppBar, { AppBarOwnProps } from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';

interface Props {
  children: React.ReactNode;
  sx?: SxProps;
  position?: AppBarOwnProps['position'];
}

export const PageHeaderContainer = (props: Props) => {
  return (
    <AppBar
      position={props.position ?? 'fixed'}
      sx={{
        bgcolor: 'common.white',
        borderBottomWidth: '1px',
        borderBottomStyle: 'solid',
        borderBottomColor: 'neutral.400',
        boxShadow: 1
      }}
    >
      <PageAreaBox pageAreaBoxName="PageHeaderContainer">
        <Toolbar sx={{ p: { xs: 0, xxl: 0 }, ...props.sx }}>
          {props.children}
        </Toolbar>
      </PageAreaBox>
    </AppBar>
  );
};
