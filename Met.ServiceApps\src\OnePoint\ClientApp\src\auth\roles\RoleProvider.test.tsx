import { getStorage, updateStorage } from '@/store/local';
import { useAccount } from '@azure/msal-react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { RoleProvider, useRoleContext } from './RoleProvider';
import { Role } from './role';

jest.mock('@/../store/local');
const getStorageMock = getStorage as jest.Mock;
const updateStorageMock = updateStorage as jest.Mock;

const TestComponent = () => {
  const roleContext = useRoleContext();

  return (
    <>
      <p data-testid="isAdmin">{roleContext.isAdmin.toString()}</p>
      <p data-testid="currentRole">{roleContext.currentRole.toString()}</p>
      <p data-testid="roles">{roleContext.roles.toString()}</p>
    </>
  );
};

const renderTestComponent = () => {
  render(
    <RoleProvider>
      <TestComponent />
    </RoleProvider>
  );
};

describe('RoleProvider', () => {
  describe('when user has admin role', () => {
    it('returns isAdmin true ', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['Admin']
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('isAdmin')).toHaveTextContent('true');
    });

    it('returns all roles', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['Admin']
        }
      });

      renderTestComponent();

      Object.keys(Role)
        .filter((role) => role !== Role.Admin)
        .forEach((role) => {
          expect(screen.getByTestId('roles')).toHaveTextContent(role);
        });
    });
  });

  describe('when user does not have admin role', () => {
    it('returns isAdmin false', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['BranchAssociate']
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('isAdmin')).toHaveTextContent('false');
    });
  });

  describe('when getting user roles', () => {
    it('returns all roles in user claims', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['BranchAssociate', 'BranchManager']
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('roles')).toHaveTextContent('BranchAssociate');
      expect(screen.getByTestId('roles')).toHaveTextContent('BranchManager');
    });
  });

  describe('when the user does not have a selected role cached', () => {
    beforeEach(() => {
      getStorageMock.mockReturnValue({});
    });

    it('defaults to BranchManager for initial role if available', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['BranchAssociate', 'BranchManager']
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('currentRole')).toHaveTextContent(
        Role.BranchManager
      );
    });

    it('defaults to first role if BranchManager not available', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['BranchAssociate', 'Cx']
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('currentRole')).toHaveTextContent(
        Role.BranchAssociate
      );
    });

    it('defaults to BranchAssociate if no roles available', () => {
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: []
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('currentRole')).toHaveTextContent(
        Role.BranchAssociate
      );
    });
  });

  describe('when the user has a selected role cached', () => {
    it('returns the cached role if the user still has that role', () => {
      const username = '<EMAIL>';
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['BranchAssociate', 'CX']
        },
        username
      });

      getStorageMock.mockReturnValue({
        selectedRoles: {
          [username]: Role.CX
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('currentRole')).toHaveTextContent(Role.CX);
      expect(updateStorageMock).not.toHaveBeenCalled();
    });

    it('returns the first role if the user does not have the cached role anymore', () => {
      const username = '<EMAIL>';
      (useAccount as jest.Mock).mockReturnValue({
        idTokenClaims: {
          roles: ['CX', 'JSS']
        },
        username
      });

      getStorageMock.mockReturnValue({
        selectedRoles: {
          [username]: Role.Hub
        }
      });

      renderTestComponent();

      expect(screen.getByTestId('currentRole')).toHaveTextContent(Role.CX);
      expect(updateStorageMock).toHaveBeenCalledWith({
        selectedRoles: {
          [username]: Role.CX
        }
      });
    });
  });
});
