'use client';

import { formatPhoneNumber } from '@/util';
import {
  Box,
  FilledInputProps,
  InputBaseComponentProps,
  InputProps,
  TextField as MuiTextField,
  OutlinedInputProps,
  SxProps,
  TextFieldProps,
  TextFieldPropsSizeOverrides,
  TextFieldVariants,
  Typography
} from '@mui/material';
import { OverridableStringUnion } from '@mui/types';
import { useTranslation } from 'next-export-i18n';
import React, { FC, HTMLInputTypeAttribute, ReactNode, useState } from 'react';
import {
  Control,
  Controller,
  FieldError,
  FieldErrorsImpl,
  FieldValues,
  Merge
} from 'react-hook-form';
import { TextFieldVariant, mergeTextFieldProps } from './variants';

interface FormProps {
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  required?: boolean;
  hasError?: boolean;
}

interface Props extends Omit<TextFieldProps, 'variant'> {
  maxCharacters?: number | null;
  maxCharactersHelperText?: boolean;
  backgroundColor?: string;
  isMultiline?: boolean;
  placeHolder?: string;
  form?: FormProps;
  type?: HTMLInputTypeAttribute;
  isDisabled?: boolean;
  textValue?: string;
  variant?: TextFieldVariant;
  inputProps?: InputBaseComponentProps;
  labelText?: string;
  label?: ReactNode;
  isPhoneFormat?: boolean;
  prefix?: string;
  validateNaN?: boolean;
  ariaLabel?: string;
  muiInputProps?:
    | Partial<FilledInputProps>
    | Partial<OutlinedInputProps>
    | Partial<InputProps>
    | undefined;
  countryCode?: string;
  addCountryCode?: boolean;
  withMaxDecimals?: boolean;
}

interface TextFieldInternalProps extends Props {
  onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  onChange?: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  value?: unknown;
  'data-testid': string;
}

function TextFieldInternal(props: TextFieldInternalProps) {
  const {
    isPhoneFormat,
    isMultiline = false,
    placeHolder = '',
    isDisabled = false,
    type = 'text',
    variant = 'outlined',
    countryCode = '',
    addCountryCode = true,
    size = 'small',
    ...rest
  } = props;

  return (
    <MuiTextField
      {...mergeTextFieldProps(variant, rest)}
      InputProps={props.muiInputProps}
      fullWidth
      type={type}
      multiline={isMultiline}
      inputProps={props.inputProps}
      placeholder={placeHolder}
      disabled={isDisabled}
      minRows={4}
      size={size}
      onBlur={props.onBlur}
      onChange={props.onChange}
      value={
        isPhoneFormat
          ? formatPhoneNumber(
              props?.value?.toString() ?? '',
              countryCode,
              addCountryCode
            )
          : props.value
      }
      data-testid={props['data-testid']}
      error={props.form?.hasError}
      label={props.label ?? props.labelText}
    />
  );
}

export const TextField: FC<Props> = (props: Props) => {
  const {
    maxCharacters = null,
    maxCharactersHelperText = false,
    labelText,
    textValue,
    prefix,
    validateNaN,
    ariaLabel = props.form?.name,
    withMaxDecimals: maxDecimals,
    ...internal
  } = props;

  const [text, setText] = useState(props.textValue ? props.textValue : '');
  const { t } = useTranslation();
  const label = props.form?.required
    ? `${labelText} ${t('common.required')}`
    : labelText;

  const remainingCharacters =
    maxCharacters !== null ? maxCharacters - text.length : null;

  const inputProps = {
    ...props.inputProps,
    maxLength: props.maxCharacters,
    'aria-label': label ?? ariaLabel
  };

  const getValue = (value: string) => {
    let localValue = value;
    if (validateNaN) {
      localValue = isNaN(parseInt(value))
        ? value.replaceAll(/[^0-9.]/g, '')
        : value;
    }

    const localText =
      prefix && localValue
        ? `${prefix}${localValue.replace(prefix, '')}`
        : localValue;

    return localText;
  };

  return (
    <>
      {props.form ? (
        <Controller
          control={props.form.control}
          name={props.form.name}
          defaultValue={textValue}
          render={({ field: { onBlur, onChange, value } }) => {
            return (
              <TextFieldInternal
                {...internal}
                data-testid={`textField-${props.form?.name}`}
                onBlur={onBlur}
                onChange={(e) => {
                  if (
                    maxDecimals &&
                    !/^(\d+\.?\d{0,2}|\d*\.?\d{0,2})?$/.test(
                      e.target.value.replace('$', '')
                    )
                  ) {
                    return;
                  }

                  setText(e.target.value);
                  onChange(e);
                }}
                value={getValue(value)}
                inputProps={inputProps}
                label={
                  label !== undefined ? (
                    <Typography variant="p1" fontWeight={400} color="inherit">
                      {label}
                    </Typography>
                  ) : undefined
                }
                isPhoneFormat={props.isPhoneFormat}
              />
            );
          }}
        />
      ) : (
        <TextFieldInternal
          {...internal}
          data-testid={`textField-${internal.name}-testId`}
          value={getValue(text)}
          onChange={(e) => {
            setText(e.target.value);
            if (props.onChange) props.onChange(e);
          }}
          inputProps={inputProps}
        />
      )}
      {maxCharacters && maxCharactersHelperText && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Typography
            variant="caption"
            color={
              maxCharacters && remainingCharacters === 0
                ? 'error'
                : 'textSecondary'
            }
          >
            {t('design.molecules.textField.charactersRemaining', {
              count: remainingCharacters
            })}
          </Typography>
        </Box>
      )}
    </>
  );
};

interface TextFieldHoCProps {
  name: string;
  value?: string;
  disabled?: boolean;
  placeHolder?: string;
  error?: FieldError | Merge<FieldError, FieldErrorsImpl<FieldValues>>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  required?: boolean;
  labelText?: string;
  variant?: TextFieldVariants;
  isPhoneFormat?: boolean;
  sx?: SxProps;
  prefix?: string;
  type?: string;
  validateNaN?: boolean;
  inputTestId?: string;
  maxCharacters?: number;
  maxCharactersHelperText?: boolean;
  countryCode?: string;
  addCountryCode?: boolean;
  withMaxDecimals?: boolean;
  size?: OverridableStringUnion<
    'small' | 'medium',
    TextFieldPropsSizeOverrides
  >;
}

export const TextFieldHoC = ({
  name,
  value,
  placeHolder,
  required,
  error,
  control,
  labelText,
  sx,
  prefix,
  inputTestId,
  variant = 'filled',
  disabled = false,
  isPhoneFormat = false,
  validateNaN = false,
  type = 'text',
  maxCharacters,
  maxCharactersHelperText,
  countryCode = '',
  addCountryCode = true,
  withMaxDecimals,
  size
}: TextFieldHoCProps) => {
  return (
    <TextField
      form={{
        name: name,
        control: control,
        hasError: error != undefined,
        required: required
      }}
      textValue={value ?? ''}
      variant={variant}
      labelText={labelText}
      isDisabled={disabled}
      placeHolder={placeHolder}
      inputProps={{
        'aria-label': `${labelText}${required ? ' *' : ''}`,
        'data-testid': inputTestId,
        'max-length': { maxCharacters }
      }}
      isPhoneFormat={isPhoneFormat}
      prefix={prefix}
      type={type}
      helperText={error?.message?.toString() ?? ''}
      validateNaN={validateNaN}
      sx={{ ...sx }}
      maxCharacters={maxCharacters}
      maxCharactersHelperText={maxCharactersHelperText}
      countryCode={countryCode ?? ''}
      addCountryCode={addCountryCode}
      withMaxDecimals={withMaxDecimals}
      size={size}
    />
  );
};
