import {
  CheckCircleIcon,
  ChevronDownIcon,
  CloseIcon,
  QuestionCircleOutlineIcon
} from '@/design/atoms';
import { Button, Select, SelectItem, Tooltip } from '@/design/molecules';
import { Modal } from '@/design/organisms';
import { Customer, CustomerCard } from '@/features/customer';
import { TaxExemptCertificate } from '@/features/serviceOrder';
import { Box, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

interface Props {
  customer: Customer;
  showModal: boolean;
  isTaxExempt?: boolean | undefined;
  hideRepairDetails?: boolean;
  setIsTaxExempt: (value: boolean) => void;
  onNextClickHandler: (file?: File) => void;
  onCloseClickHandler: () => void;
}

const CustomerServiceRequestModalComponent = ({
  customer,
  showModal,
  isTaxExempt,
  hideRepairDetails = false,
  setIsTaxExempt,
  onNextClickHandler,
  onCloseClickHandler
}: Props) => {
  const { t } = useTranslation();
  const [file, setFile] = useState<File>();
  const serviceOrderContactDetailsLabel = t(
    'features.account.details.contactDetailsModalTitle'
  );
  const responsiblePartyLabel = t('features.account.details.responsibleParty');
  const taxExemptLabel = t('features.customer.details.taxExemptLabelmodal');
  const responsiblePartyLabelTooltip = t(
    'features.account.details.responsiblePartyTooltip'
  );
  const taxExemptCertLabel = t(
    'features.order.detail.uploadTaxExemptCertificate'
  );

  const yesText = t('common.yesText');
  const noText = t('common.noText');

  const taxExemptDropdownData: SelectItem[] = [
    { id: 'true', value: yesText },
    { id: 'false', value: noText }
  ];

  useEffect(() => {
    if (!isTaxExempt) {
      setFile(undefined);
    }
  }, [isTaxExempt]);

  const canSubmit = useMemo((): boolean => {
    return !isTaxExempt || !!file;
  }, [isTaxExempt, file]);

  const onNextBtnClick = useCallback(() => {
    onNextClickHandler(file);
  }, [onNextClickHandler, file]);

  const onCloseBtnClick = useCallback(() => {
    onCloseClickHandler();
  }, [onCloseClickHandler]);

  const handleTaxExemptChange = useCallback(
    (isTaxExempt: string) => {
      setIsTaxExempt(isTaxExempt === 'true');
    },
    [setIsTaxExempt]
  );

  const IconDropdownComponent = useCallback(
    (props: { className: string }) => (
      <ChevronDownIcon
        {...props}
        viewBox="0 0 16 10"
        sx={{ width: '16px', height: '10px' }}
      />
    ),
    []
  );

  const handleRemoveFile = useCallback(() => {
    setFile(undefined);
  }, []);

  const taxExemptSection = useMemo(() => {
    if (!isTaxExempt) return null;
    return (
      <Box>
        <Typography
          variant="h6"
          color="neutral.800"
          data-testid="upload-tax-exempt-cert-testId"
          sx={{ fontWeight: '600', mr: 1 }}
        >
          {taxExemptCertLabel}
        </Typography>
        <TaxExemptCertificate
          file={file}
          setFile={setFile}
          onRemoveFile={handleRemoveFile}
          containerSx={{
            border: '3px dashed',
            borderColor: 'neutral.400',
            backgroundColor: 'neutral.100'
          }}
        />
      </Box>
    );
  }, [isTaxExempt, file, handleRemoveFile, taxExemptCertLabel]);

  return (
    <Modal
      open={showModal}
      width="760px"
      modalName="CustomerServiceRequestModal"
      sx={{
        '.MuiPaper-root': {
          borderRadius: '4px'
        }
      }}
    >
      {!hideRepairDetails && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: '16px 20px'
          }}
        >
          <Typography
            variant="h5"
            color="neutral.800"
            sx={{ fontWeight: '600', mr: 1 }}
            data-testid="serviceOrderContactDetailsLabel-testId"
          >
            {serviceOrderContactDetailsLabel}
          </Typography>
          <IconButton
            data-testid="closeIconBtn-testId"
            onClick={onCloseBtnClick}
            sx={{
              color: 'neutral.800'
            }}
          >
            <CloseIcon
              style={{
                width: '24px',
                height: '24px',
                color: 'neutral.800'
              }}
              viewBox="0 0 24 24"
              data-testid="closeBtn-testId"
            />
          </IconButton>
        </Box>
      )}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          backgroundColor: 'common.white',
          bgcolor: 'common.white',
          width: '100%',
          px: '24px',
          py: hideRepairDetails ? '1rem' : 'inherit',
          gap: '24px'
        }}
      >
        {!hideRepairDetails && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '16px'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h6"
                color="neutral.800"
                data-testid="responsiblePartyLabel-testId"
                sx={{ fontWeight: '600', mr: 1 }}
              >
                {responsiblePartyLabel}
              </Typography>

              <Tooltip placement="right" title={responsiblePartyLabelTooltip}>
                <Box
                  sx={{
                    alignItems: 'center',
                    display: 'flex'
                  }}
                >
                  <QuestionCircleOutlineIcon
                    sx={{
                      width: '18px',
                      height: '18px'
                    }}
                    viewBox="0 0 24 24"
                    aria-hidden={undefined}
                  />
                </Box>
              </Tooltip>
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px'
              }}
            >
              {/* Profile selected */}
              <Box
                sx={{
                  display: 'flex',
                  gap: '16px',
                  border: '2px solid',
                  borderColor: 'primary.500',
                  borderRadius: '4px',
                  alignItems: 'center',
                  padding: '0 16px 16px 16px'
                }}
              >
                <CheckCircleIcon
                  width="24"
                  height="24"
                  viewBox="0 0 16 16"
                  data-testid={`checkIcon-testId`}
                />
                <CustomerCard
                  customer={customer}
                  iconSx={{
                    width: '40px',
                    height: '40px'
                  }}
                  containerSxProps={{
                    alignItems: 'center',
                    p: 0
                  }}
                  textVariant="p1"
                  titleVariant="p2"
                  showOnlyCustomerInfo
                />
              </Box>
            </Box>
          </Box>
        )}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          }}
          data-testid="selectTaxExempt-container"
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              color="neutral.800"
              sx={{ fontWeight: '600', mr: 1 }}
              data-testid="taxtExemptLabel-testId"
            >
              {taxExemptLabel}
            </Typography>
            <QuestionCircleOutlineIcon
              sx={{
                width: '18px',
                height: '18px'
              }}
              viewBox="0 0 24 24"
            />
          </Box>
          <Select
            value={
              isTaxExempt === undefined ? '' : isTaxExempt ? 'true' : 'false'
            }
            onSelectChange={handleTaxExemptChange}
            data-testid="selectTaxExempt-testId"
            items={taxExemptDropdownData}
            error={false}
            name="selectTaxExempt"
            sx={{
              '.MuiSvgIcon-root': {
                right: '16px',
                top: 'unset'
              },
              '.MuiInputBase-root': {
                color: 'neutral.800',
                bgcolor: 'common.white',
                ':before': {
                  borderBottomColor: 'neutral.800'
                },
                fontWeight: 400,
                ':hover:not(.Mui-disabled, .Mui-error)': {
                  bgcolor: 'neutral.100'
                },
                ':hover:not(.Mui-disabled, .Mui-error):before': {
                  borderBottomColor: 'neutral.300'
                }
              },
              '.MuiSelect-select': {
                paddingTop: '20px',
                paddingBottom: '20px',
                display: 'flex',
                alignItems: 'center'
              }
            }}
            iconComponent={IconDropdownComponent}
          />
        </Box>
        {taxExemptSection}
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            justifyContent: 'flex-end',
            gap: '16px'
          }}
        >
          <Button
            variant="tertiary"
            data-testid="cancelBtn-testId"
            onClick={onCloseBtnClick}
          >
            {t('common.cancel')}
          </Button>

          <Button
            variant="primary"
            onClick={onNextBtnClick}
            data-testid="nextBtn-testId"
            disabled={isTaxExempt === undefined || !canSubmit}
          >
            {t('common.next')}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export const CustomerServiceRequestModal = React.memo(
  CustomerServiceRequestModalComponent
);
