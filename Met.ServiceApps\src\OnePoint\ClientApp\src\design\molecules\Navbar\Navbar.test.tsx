import { renderWithProviders } from '@/../__test-utils__/renderWithProviders';
import { Role, useRoleContext } from '@/auth';
import { useMsal } from '@azure/msal-react';
import '@testing-library/jest-dom';
import { fireEvent, screen } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { Navbar } from './Navbar';
import { NavbarMenuOptionCode } from './models';

jest.mock('@/../auth/roles/RoleProvider');

describe('Navbar Component', () => {
  const mockLogout = jest.fn();
  const mockPush = jest.fn();

  beforeEach(() => {
    (useMsal as jest.Mock).mockReturnValue({
      instance: { logout: mockLogout }
    });
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useRoleContext as jest.Mock).mockReturnValue({
      roles: [
        <PERSON><PERSON>,
        <PERSON>.BranchAssociate,
        Role.Hub
      ] satisfies Role[]
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    savedOrderGroups: [],
    closeNotification: jest.fn()
  };

  it('Renders the Navbar component', () => {
    renderWithProviders(<Navbar {...defaultProps} />);
    expect(screen.getByAltText('OnePoint Logo')).toBeInTheDocument();
  });

  it('Opens and closes the user menu', () => {
    renderWithProviders(<Navbar {...defaultProps} />);
    const userMenuButton = screen.getByTestId('navOptionsUser');
    const menuList = screen
      .getByTestId('menu-appbar-testId')
      .querySelector('ul');

    expect(menuList).not.toBeVisible();
    fireEvent.click(userMenuButton);
    expect(menuList).toBeVisible();
  });

  it('Calls logout when logout menu item is clicked', () => {
    renderWithProviders(
      <Navbar
        {...defaultProps}
        savedOrderGroups={[
          {
            createdDate: null,
            isOpen: true,
            forceRealtimeService: false
          }
        ]}
      />
    );
    const userMenuButton = screen.getByTestId('navOptionsUser');
    fireEvent.click(userMenuButton);

    const logoutMenuItem = screen.getByTestId(
      `settingMenuItem-${NavbarMenuOptionCode.Logout}`
    );
    fireEvent.click(logoutMenuItem);

    expect(mockLogout).toHaveBeenCalled();
  });

  it('Navigates to saved orders when saved orders menu item is clicked', () => {
    renderWithProviders(
      <Navbar
        {...defaultProps}
        savedOrderGroups={[
          {
            createdDate: null,
            isOpen: true,
            forceRealtimeService: false
          }
        ]}
      />
    );
    const userMenuButton = screen.getByTestId('navOptionsUser');
    fireEvent.click(userMenuButton);

    const savedOrdersMenuItem = screen.getByTestId(
      `settingMenuItem-${NavbarMenuOptionCode.SavedOrders}`
    );
    fireEvent.click(savedOrdersMenuItem);

    expect(mockPush).toHaveBeenCalledWith('/serviceOrder/savedorders');
  });

  it('Renders menu items correctly', () => {
    renderWithProviders(<Navbar {...defaultProps} />);

    const userMenuButton = screen.getByTestId('navOptionsUser');
    const menuList = screen
      .getByTestId('menu-appbar-testId')
      .querySelector('ul');

    fireEvent.click(userMenuButton);
    expect(menuList).toBeVisible();
    expect(
      screen.getByTestId(`settingMenuItem-${NavbarMenuOptionCode.SavedOrders}`)
    ).toBeVisible();
    expect(
      screen.getByTestId(`settingMenuItem-${NavbarMenuOptionCode.Logout}`)
    ).toBeVisible();

    const listItems = menuList?.querySelectorAll('li');
    expect(listItems).toHaveLength(2);
  });

  it('Renders the RolePicker component', () => {
    renderWithProviders(<Navbar {...defaultProps} />);
    const userMenuButton = screen.getByTestId('navOptionsUser');
    const menuList = screen
      .getByTestId('menu-appbar-testId')
      .querySelector('ul');

    fireEvent.click(userMenuButton);
    expect(menuList).toBeVisible();
    expect(screen.getByTestId('navbarRolePicker')).toBeInTheDocument();
  });
});
