// OnePoint API Type Definitions
// Generated from API Documentation

// Common Types
export interface ApiResponse<T = any> {
  data?: T;
  success: boolean;
  message?: string;
  errorCode?: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
}

// Enums
export enum PriceType {
  Service = 1,
  Retail = 2,
  Wholesale = 3
}

export enum CustomerType {
  Retail = 1,
  Commercial = 2,
  Dealer = 3,
  Internal = 4
}

export enum Currency {
  USD = 1,
  CAD = 2,
  EUR = 3
}

export enum ServiceType {
  Repair = 'repair',
  Replace = 'replace',
  Maintenance = 'maintenance',
  Diagnostic = 'diagnostic'
}

// Pricing API Types
export interface ServicePricingRequest {
  priceType: PriceType;
  customerType: CustomerType;
  currency: Currency;
  svcCategory?: string;
  itemCodes: string[];
}

export interface ServicePricingItem {
  itemCode: string;
  price: number;
  currency: string;
  priceType: PriceType;
  customerType: CustomerType;
  effectiveDate: string;
  expirationDate: string;
}

export interface ServicePricingResponse extends ApiResponse {
  items: ServicePricingItem[];
}

// Warranty API Types
export interface WarrantyCheckRequest {
  skuAlias: string;
}

export interface WarrantyCheckResponse extends ApiResponse {
  skuAlias: string;
  isUnderWarranty: boolean;
  warrantyType: string;
  warrantyPeriod: string;
  purchaseDate: string;
  expirationDate: string;
  warrantyDescription: string;
}

// Product API Types
export interface ProductProblem {
  id: number;
  code: string;
  description: string;
  category: string;
}

export interface ProductProblemsResponse extends ApiResponse {
  problems: ProductProblem[];
}

export interface ProductInclusion {
  id: number;
  sku: string;
  description: string;
  quantity: number;
  isOptional: boolean;
}

export interface ProductInclusionsResponse extends ApiResponse {
  inclusions: ProductInclusion[];
}

export interface ReplacementReason {
  id: number;
  code: string;
  description: string;
  category: string;
}

export interface ReplacementReasonsResponse extends ApiResponse {
  reasons: ReplacementReason[];
}

// Service Order API Types
export interface ServiceOrderEstimateItem {
  sku: string;
  quantity: number;
  serviceType: ServiceType;
}

export interface ServiceOrderEstimatePricingRequest {
  items: ServiceOrderEstimateItem[];
  customerType: CustomerType;
  currency: Currency;
  taxExempt: boolean;
}

export interface ServiceOrderItem {
  sku: string;
  quantity: number;
  serviceType: ServiceType;
  problemCodes: string[];
  symptoms: string[];
}

export interface RealtimeServiceOrderRequest {
  customerId: string;
  branchId: string;
  items: ServiceOrderItem[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

export interface ServiceOrderChargeItem {
  sku: string;
  quantity: number;
  laborHours: number;
  partsCost: number;
}

export interface ServiceOrderChargesRequest {
  orderId: string;
  items: ServiceOrderChargeItem[];
  customerType: CustomerType;
  discountPercent: number;
}

export interface ServiceOrderTaxesRequest {
  orderId: string;
  zipCode?: string;
  stateCode?: string;
}

// Repair Details API Types
export interface SymptomCode {
  id: number;
  code: string;
  description: string;
  areaId: number;
  severity: 'low' | 'medium' | 'high';
}

export interface SymptomCodesResponse extends ApiResponse {
  symptomCodes: SymptomCode[];
}

// Payment API Types
export interface PaymentTerminal {
  id: string;
  name: string;
  branchId: string;
  status: 'active' | 'inactive' | 'maintenance';
  type: 'credit_card' | 'cash' | 'check' | 'gift_card';
  capabilities: string[];
  lastHeartbeat: string;
}

export interface PaymentTerminalsRequest {
  branchId?: string;
  status?: 'active' | 'inactive';
}

export interface PaymentTerminalsResponse extends ApiResponse {
  terminals: PaymentTerminal[];
}

// Error Types
export interface ApiError {
  success: false;
  message: string;
  errorCode: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Common Error Codes
export enum ApiErrorCode {
  INVALID_SKU = 'INVALID_SKU',
  PRICING_UNAVAILABLE = 'PRICING_UNAVAILABLE',
  WARRANTY_EXPIRED = 'WARRANTY_EXPIRED',
  INVALID_CUSTOMER_TYPE = 'INVALID_CUSTOMER_TYPE',
  INSUFFICIENT_INVENTORY = 'INSUFFICIENT_INVENTORY',
  PAYMENT_TERMINAL_OFFLINE = 'PAYMENT_TERMINAL_OFFLINE'
}

// API Service Interface
export interface IOnePointApiService {
  getServicePricing(params: ServicePricingRequest): Promise<ServicePricingResponse>;
  checkWarranty(skuAlias: string): Promise<WarrantyCheckResponse>;
  getProductProblems(sku: string): Promise<ProductProblemsResponse>;
  getProductInclusions(sku: string): Promise<ProductInclusionsResponse>;
  getReplacementReasons(): Promise<ReplacementReasonsResponse>;
  calculateServiceOrderEstimatePricing(
    estimateId: string,
    request: ServiceOrderEstimatePricingRequest
  ): Promise<ApiResponse>;
  createRealtimeServiceOrder(request: RealtimeServiceOrderRequest): Promise<ApiResponse>;
  calculateServiceOrderCharges(request: ServiceOrderChargesRequest): Promise<ApiResponse>;
  getServiceOrderTaxes(request: ServiceOrderTaxesRequest): Promise<ApiResponse>;
  getSymptomCodes(areaId: number): Promise<SymptomCodesResponse>;
  getPaymentTerminals(request?: PaymentTerminalsRequest): Promise<PaymentTerminalsResponse>;
}

// HTTP Client Configuration
export interface ApiClientConfig {
  baseUrl: string;
  authToken: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

// Request/Response Interceptors
export interface RequestInterceptor {
  (config: RequestInit): RequestInit | Promise<RequestInit>;
}

export interface ResponseInterceptor {
  (response: Response): Response | Promise<Response>;
}

export interface ErrorInterceptor {
  (error: Error): Error | Promise<Error>;
}

// Rate Limiting
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
}

// Utility Types
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface ApiEndpoint {
  method: ApiMethod;
  path: string;
  requiresAuth: boolean;
  rateLimit?: {
    requests: number;
    window: number; // in seconds
  };
}

// Query Builder Types
export interface QueryParams {
  [key: string]: string | number | boolean | string[] | number[] | undefined;
}

export interface ApiRequestOptions extends RequestInit {
  params?: QueryParams;
  timeout?: number;
  retries?: number;
}
