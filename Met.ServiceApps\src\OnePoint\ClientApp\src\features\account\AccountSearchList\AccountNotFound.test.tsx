import { AccountNotFound } from '@/features/account';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';

describe('CustomerNotFound component tests', () => {
  it('Renders successfully', () => {
    render(<AccountNotFound />);

    const notFoundLabel = screen.getByTestId('noRecordsFoundText-testId');
    const icon = screen.getByTestId('noRecordsFoundIcon-testId');

    expect(notFoundLabel).toHaveTextContent('No records found');
    expect(icon).toBeInTheDocument();
  });
});
