import { UsersIcon } from '@/design/atoms';
import { GenericAccount, SiteItem } from '@/features/account';
import { Address } from '@/features/customer';
import { concatAddressLineText } from '@/util';
import { Box, SxProps, Typography } from '@mui/material';
import { Variant } from '@mui/material/styles/createTypography';
import { useCallback, useMemo } from 'react';

interface Props {
  containerSxProps?: SxProps;
  'data-testid'?: string;
  iconSx?: SxProps;
  textVariant?: string;
  titleVariant?: string;
  site?: SiteItem;
  genericAccount?: GenericAccount;
}

export const AccountCard = (props: Props) => {
  const variants = {
    text: (props.textVariant as Variant) ?? 'p2',
    title: (props.titleVariant as Variant) ?? 'p3'
  };

  const getSiteNameAndNumber = useCallback(() => {
    if (props.genericAccount)
      return `${props.genericAccount?.accountName} ${props.genericAccount?.accountNumber}`;

    return `${props.site?.siteName} ${props.site?.siteNumber}`;
  }, [props.site?.siteName, props.site?.siteNumber, props.genericAccount]);

  const getAddressFromSite = useMemo(() => {
    if (props.site) {
      return {
        addressLine1: props.site.addressLine1,
        addressLine2: props.site.addressLine2,
        city: props.site.city,
        state: props.site.state,
        countryCode: props.site.countryCode,
        postalCode: props.site.postalCode
      } as Address;
    }
  }, [props.site]);

  return (
    <Box
      data-testid={props['data-testid']}
      sx={{
        display: 'flex',
        gap: '12px',
        pb: '8px',
        ...props.containerSxProps
      }}
    >
      <Box sx={{ display: 'flex', gap: '8px' }}>
        <UsersIcon
          viewBox="0 0 24 24"
          sx={{
            width: '24px',
            height: '24px',
            color: 'primary.700',
            ...props.iconSx
          }}
        />
        <Typography
          variant={variants.title}
          color="neutral.800"
          data-testid="siteName"
          sx={{ fontWeight: '600' }}
        >
          {getSiteNameAndNumber()}
        </Typography>
      </Box>
      <Typography
        variant={variants.text}
        color="neutral.600"
        data-testid="siteAddress"
        noWrap={true}
      >
        {concatAddressLineText(getAddressFromSite)}
      </Typography>
    </Box>
  );
};
