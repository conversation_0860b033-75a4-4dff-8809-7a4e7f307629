import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography
} from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { FC } from 'react';
import { useRoleContext } from './RoleProvider';
import { Role } from './role';

interface Props {
  onChange: () => void;
  'data-testid'?: string;
}

export const RolePicker: FC<Props> = ({
  onChange,
  'data-testid': dataTestId
}) => {
  const { currentRole, roles, setCurrentRole } = useRoleContext();
  const { t } = useTranslation();
  const roleText = t('auth.rolePicker.role');

  const handleChange = (event: SelectChangeEvent<Role>) => {
    setCurrentRole(event.target.value as Role);
    onChange();
  };

  if (roles.length < 2) {
    return null;
  }

  return (
    <FormControl
      sx={{ m: 1, minWidth: 120 }}
      size="small"
      data-testid={dataTestId}
    >
      <InputLabel>{roleText}</InputLabel>
      <Select
        data-testid="rolePickerSelect"
        value={currentRole}
        label={roleText}
        onChange={handleChange}
      >
        {roles.map((role) => (
          <MenuItem
            data-testid={`rolePickerMenuItem${role}`}
            key={role}
            value={role}
          >
            <Typography textAlign="center">
              {t(`auth.rolePicker.${role}`)}
            </Typography>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
