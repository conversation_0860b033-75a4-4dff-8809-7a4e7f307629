'use client';

import { Loading, PageHeaderContainerWrapper } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import {
  Customer,
  CustomerCard,
  useLazySearchCustomersQuery
} from '@/features/customer';
import { CurrencyType } from '@/features/pricing';
import { SelectedProduct } from '@/features/product';
import {
  GroupResponse,
  OrderCreditContainer,
  OrderListDisplayOrigin,
  removeSelectedProduct,
  ServiceOrder,
  useGetOrderDetails
} from '@/features/serviceOrder';
import { useNavigation } from '@/hooks';
import { useAppDispatch } from '@/store/hooks';
import { getCurrencyType, isOPR } from '@/util';
import { useTranslation } from 'next-export-i18n';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

export default function ServiceCreditPage() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { goToRepairDetailOrderPage } = useNavigation();

  const searchParams = useSearchParams();
  const groupId = searchParams?.get('groupId') ?? '';
  const orderId = searchParams?.get('orderId') ?? '';

  const { getDetails: getOrderDetails, isErrorSearchServiceOrderGroup } =
    useGetOrderDetails();
  const [searchCustomersQuery, { isFetching: isCustomerFetching }] =
    useLazySearchCustomersQuery();

  const [isLoading, setIsLoading] = useState(true);
  const [customer, setCustomer] = useState<Customer | undefined>();
  const [group, setGroup] = useState<GroupResponse | undefined>();
  const [order, setOrder] = useState<ServiceOrder | undefined>();
  const [currencyType, setCurrencyType] = useState<CurrencyType>(
    CurrencyType.Usd
  );
  const [product, setProduct] = React.useState<SelectedProduct | undefined>();

  useEffect(() => {
    async function fetchCustomer() {
      if (!group) return;

      const customerResponse = await searchCustomersQuery(
        {
          ids: [group.customerId]
        },
        true
      );

      setCustomer(customerResponse?.data?.users[0]);
    }

    fetchCustomer();
  }, [group, searchCustomersQuery]);

  useEffect(() => {
    if (!groupId) return;

    async function fetchData() {
      const orderGroupResponse = await getOrderDetails(groupId, orderId);
      setIsLoading(false);

      if (!isErrorSearchServiceOrderGroup) {
        const serviceRequestGroup =
          orderGroupResponse?.orderGroupResponse.data?.serviceOrderGroup;

        if (serviceRequestGroup) {
          const serviceOrder = serviceRequestGroup.serviceOrders.find(
            (x) => x.id == orderId
          );

          if (!serviceOrder) {
            return;
          }

          setGroup(serviceRequestGroup);
          setProduct(orderGroupResponse.selectedProduct);
          setOrder(serviceOrder);
          const currencyType = getCurrencyType(
            serviceOrder.shipToMetadata?.currency
          );
          setCurrencyType(currencyType);
        }
      }
    }
    setIsLoading(true);
    fetchData();
  }, [
    groupId,
    isErrorSearchServiceOrderGroup,
    orderId,
    getOrderDetails,
    searchCustomersQuery,
    setOrder
  ]);

  const handleCloseIconClick = () => {
    dispatch(removeSelectedProduct((product as SelectedProduct).selectionId));

    goToRepairDetailOrderPage(
      groupId,
      isOPR([order?.sku || '']),
      false,
      orderId,
      false,
      1,
      OrderListDisplayOrigin.OrderHistory,
      '',
      '',
      false
    );
  };

  return (
    <>
      <PageHeaderContainerWrapper
        handleBackIconClick={handleCloseIconClick}
        handleCloseIconClick={handleCloseIconClick}
        pageTitle={t('features.order.serviceCredit.serviceCreditHeader')}
      />
      <PageAreaBox
        pageAreaBoxName="customer"
        sx={{
          height: '100%',
          bgcolor: 'background.paper',
          padding: '0px 56px 0px 56px !important',
          mt: '88px'
        }}
      >
        <Loading
          isLoading={isLoading}
          fallbackContainerProps={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <OrderCreditContainer
            serviceOrder={order}
            currencyType={currencyType}
            onClose={handleCloseIconClick}
            customerCard={
              group &&
              order &&
              customer && (
                <CustomerCard
                  customer={customer}
                  dropOffParty={group.dropOffParty}
                  isLoadingData={isCustomerFetching}
                />
              )
            }
            product={product as SelectedProduct}
          />
        </Loading>
      </PageAreaBox>
    </>
  );
}
