import { getScopes, msalInstance } from '@/auth';
import { InteractionRequiredAuthError } from '@azure/msal-browser';
import { getLanguage } from './helpers/languageContextHelper';

export const getAccessToken = async (): Promise<string | undefined> => {
  const activeAccount = msalInstance?.application?.getActiveAccount();
  const accounts = msalInstance?.application?.getAllAccounts() || [];

  if (activeAccount || accounts.length > 0) {
    const request = {
      scopes: getScopes(msalInstance?.clientId),
      account: activeAccount || accounts[0]
    };

    const response = await msalInstance?.application
      .acquireTokenSilent(request)
      .catch(async (error: unknown) => {
        if (error instanceof InteractionRequiredAuthError) {
          return msalInstance?.application.acquireTokenRedirect(request);
        }
      });

    return response?.accessToken;
  }

  return undefined;
};

export const requestHeaders = async (headers: Headers): Promise<Headers> => {
  const accessToken = await getAccessToken();

  headers.set('Authorization', `Bearer ${accessToken}`);

  const lang = getLanguage();
  headers.set('Accept-Language', lang);

  return headers;
};
