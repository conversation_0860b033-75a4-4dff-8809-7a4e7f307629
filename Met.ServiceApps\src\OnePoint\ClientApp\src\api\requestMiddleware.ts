import { showUnauthorizedScreen } from '@/features/error';
import { ApiErrorCode } from '@/features/search';
import { showSnackbar } from '@/features/snackbar';
import type { Middleware, MiddlewareAPI } from '@reduxjs/toolkit';
import { isRejectedWithValue } from '@reduxjs/toolkit';
import { ApiExceptionPayload } from './models';

function isSuppressingError(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  action: any
): action is {
  meta: { arg: { originalArgs: { suppressErrorMiddleware?: boolean } } };
} {
  return !!action?.meta?.arg?.originalArgs?.suppressErrorMiddleware;
}

export const rtkQueryErrorLogger: Middleware =
  (api: MiddlewareAPI) => (next) => (action) => {
    if (isRejectedWithValue(action) && !isSuppressingError(action)) {
      const response: ApiExceptionPayload =
        action.payload as ApiExceptionPayload;

      switch (response.status) {
        case ApiErrorCode.InternalError:
          api.dispatch(
            showSnackbar({
              messageKey: 'common.snackBarError',
              severity: 'error',
              isApiError: true
            })
          );
          break;
        case ApiErrorCode.BadRequest:
          api.dispatch(
            showSnackbar({
              messageKey: response.data.detail
                ? undefined
                : 'common.snackBarError',
              message: response.data.detail,
              severity: 'error'
            })
          );
          break;
        case ApiErrorCode.Unauthorized:
          api.dispatch(
            showUnauthorizedScreen({ unauthorizedScreenOpen: true })
          );
          break;
      }
    }

    return next(action);
  };
