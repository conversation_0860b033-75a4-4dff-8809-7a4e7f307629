"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zod";
exports.ids = ["vendor-chunks/zod"];
exports.modules = {

/***/ "(ssr)/./node_modules/zod/lib/index.mjs":
/*!****************************************!*\
  !*** ./node_modules/zod/lib/index.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BRAND: () => (/* binding */ BRAND),\n/* harmony export */   DIRTY: () => (/* binding */ DIRTY),\n/* harmony export */   EMPTY_PATH: () => (/* binding */ EMPTY_PATH),\n/* harmony export */   INVALID: () => (/* binding */ INVALID),\n/* harmony export */   NEVER: () => (/* binding */ NEVER),\n/* harmony export */   OK: () => (/* binding */ OK),\n/* harmony export */   ParseStatus: () => (/* binding */ ParseStatus),\n/* harmony export */   Schema: () => (/* binding */ ZodType),\n/* harmony export */   ZodAny: () => (/* binding */ ZodAny),\n/* harmony export */   ZodArray: () => (/* binding */ ZodArray),\n/* harmony export */   ZodBigInt: () => (/* binding */ ZodBigInt),\n/* harmony export */   ZodBoolean: () => (/* binding */ ZodBoolean),\n/* harmony export */   ZodBranded: () => (/* binding */ ZodBranded),\n/* harmony export */   ZodCatch: () => (/* binding */ ZodCatch),\n/* harmony export */   ZodDate: () => (/* binding */ ZodDate),\n/* harmony export */   ZodDefault: () => (/* binding */ ZodDefault),\n/* harmony export */   ZodDiscriminatedUnion: () => (/* binding */ ZodDiscriminatedUnion),\n/* harmony export */   ZodEffects: () => (/* binding */ ZodEffects),\n/* harmony export */   ZodEnum: () => (/* binding */ ZodEnum),\n/* harmony export */   ZodError: () => (/* binding */ ZodError),\n/* harmony export */   ZodFirstPartyTypeKind: () => (/* binding */ ZodFirstPartyTypeKind),\n/* harmony export */   ZodFunction: () => (/* binding */ ZodFunction),\n/* harmony export */   ZodIntersection: () => (/* binding */ ZodIntersection),\n/* harmony export */   ZodIssueCode: () => (/* binding */ ZodIssueCode),\n/* harmony export */   ZodLazy: () => (/* binding */ ZodLazy),\n/* harmony export */   ZodLiteral: () => (/* binding */ ZodLiteral),\n/* harmony export */   ZodMap: () => (/* binding */ ZodMap),\n/* harmony export */   ZodNaN: () => (/* binding */ ZodNaN),\n/* harmony export */   ZodNativeEnum: () => (/* binding */ ZodNativeEnum),\n/* harmony export */   ZodNever: () => (/* binding */ ZodNever),\n/* harmony export */   ZodNull: () => (/* binding */ ZodNull),\n/* harmony export */   ZodNullable: () => (/* binding */ ZodNullable),\n/* harmony export */   ZodNumber: () => (/* binding */ ZodNumber),\n/* harmony export */   ZodObject: () => (/* binding */ ZodObject),\n/* harmony export */   ZodOptional: () => (/* binding */ ZodOptional),\n/* harmony export */   ZodParsedType: () => (/* binding */ ZodParsedType),\n/* harmony export */   ZodPipeline: () => (/* binding */ ZodPipeline),\n/* harmony export */   ZodPromise: () => (/* binding */ ZodPromise),\n/* harmony export */   ZodReadonly: () => (/* binding */ ZodReadonly),\n/* harmony export */   ZodRecord: () => (/* binding */ ZodRecord),\n/* harmony export */   ZodSchema: () => (/* binding */ ZodType),\n/* harmony export */   ZodSet: () => (/* binding */ ZodSet),\n/* harmony export */   ZodString: () => (/* binding */ ZodString),\n/* harmony export */   ZodSymbol: () => (/* binding */ ZodSymbol),\n/* harmony export */   ZodTransformer: () => (/* binding */ ZodEffects),\n/* harmony export */   ZodTuple: () => (/* binding */ ZodTuple),\n/* harmony export */   ZodType: () => (/* binding */ ZodType),\n/* harmony export */   ZodUndefined: () => (/* binding */ ZodUndefined),\n/* harmony export */   ZodUnion: () => (/* binding */ ZodUnion),\n/* harmony export */   ZodUnknown: () => (/* binding */ ZodUnknown),\n/* harmony export */   ZodVoid: () => (/* binding */ ZodVoid),\n/* harmony export */   addIssueToContext: () => (/* binding */ addIssueToContext),\n/* harmony export */   any: () => (/* binding */ anyType),\n/* harmony export */   array: () => (/* binding */ arrayType),\n/* harmony export */   bigint: () => (/* binding */ bigIntType),\n/* harmony export */   boolean: () => (/* binding */ booleanType),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   custom: () => (/* binding */ custom),\n/* harmony export */   date: () => (/* binding */ dateType),\n/* harmony export */   \"default\": () => (/* binding */ z),\n/* harmony export */   defaultErrorMap: () => (/* binding */ errorMap),\n/* harmony export */   discriminatedUnion: () => (/* binding */ discriminatedUnionType),\n/* harmony export */   effect: () => (/* binding */ effectsType),\n/* harmony export */   \"enum\": () => (/* binding */ enumType),\n/* harmony export */   \"function\": () => (/* binding */ functionType),\n/* harmony export */   getErrorMap: () => (/* binding */ getErrorMap),\n/* harmony export */   getParsedType: () => (/* binding */ getParsedType),\n/* harmony export */   \"instanceof\": () => (/* binding */ instanceOfType),\n/* harmony export */   intersection: () => (/* binding */ intersectionType),\n/* harmony export */   isAborted: () => (/* binding */ isAborted),\n/* harmony export */   isAsync: () => (/* binding */ isAsync),\n/* harmony export */   isDirty: () => (/* binding */ isDirty),\n/* harmony export */   isValid: () => (/* binding */ isValid),\n/* harmony export */   late: () => (/* binding */ late),\n/* harmony export */   lazy: () => (/* binding */ lazyType),\n/* harmony export */   literal: () => (/* binding */ literalType),\n/* harmony export */   makeIssue: () => (/* binding */ makeIssue),\n/* harmony export */   map: () => (/* binding */ mapType),\n/* harmony export */   nan: () => (/* binding */ nanType),\n/* harmony export */   nativeEnum: () => (/* binding */ nativeEnumType),\n/* harmony export */   never: () => (/* binding */ neverType),\n/* harmony export */   \"null\": () => (/* binding */ nullType),\n/* harmony export */   nullable: () => (/* binding */ nullableType),\n/* harmony export */   number: () => (/* binding */ numberType),\n/* harmony export */   object: () => (/* binding */ objectType),\n/* harmony export */   objectUtil: () => (/* binding */ objectUtil),\n/* harmony export */   oboolean: () => (/* binding */ oboolean),\n/* harmony export */   onumber: () => (/* binding */ onumber),\n/* harmony export */   optional: () => (/* binding */ optionalType),\n/* harmony export */   ostring: () => (/* binding */ ostring),\n/* harmony export */   pipeline: () => (/* binding */ pipelineType),\n/* harmony export */   preprocess: () => (/* binding */ preprocessType),\n/* harmony export */   promise: () => (/* binding */ promiseType),\n/* harmony export */   quotelessJson: () => (/* binding */ quotelessJson),\n/* harmony export */   record: () => (/* binding */ recordType),\n/* harmony export */   set: () => (/* binding */ setType),\n/* harmony export */   setErrorMap: () => (/* binding */ setErrorMap),\n/* harmony export */   strictObject: () => (/* binding */ strictObjectType),\n/* harmony export */   string: () => (/* binding */ stringType),\n/* harmony export */   symbol: () => (/* binding */ symbolType),\n/* harmony export */   transformer: () => (/* binding */ effectsType),\n/* harmony export */   tuple: () => (/* binding */ tupleType),\n/* harmony export */   undefined: () => (/* binding */ undefinedType),\n/* harmony export */   union: () => (/* binding */ unionType),\n/* harmony export */   unknown: () => (/* binding */ unknownType),\n/* harmony export */   util: () => (/* binding */ util),\n/* harmony export */   \"void\": () => (/* binding */ voidType),\n/* harmony export */   z: () => (/* binding */ z)\n/* harmony export */ });\nvar util;\n(function(util) {\n    util.assertEqual = (val)=>val;\n    function assertIs(_arg) {}\n    util.assertIs = assertIs;\n    function assertNever(_x) {\n        throw new Error();\n    }\n    util.assertNever = assertNever;\n    util.arrayToEnum = (items)=>{\n        const obj = {};\n        for (const item of items){\n            obj[item] = item;\n        }\n        return obj;\n    };\n    util.getValidEnumValues = (obj)=>{\n        const validKeys = util.objectKeys(obj).filter((k)=>typeof obj[obj[k]] !== \"number\");\n        const filtered = {};\n        for (const k of validKeys){\n            filtered[k] = obj[k];\n        }\n        return util.objectValues(filtered);\n    };\n    util.objectValues = (obj)=>{\n        return util.objectKeys(obj).map(function(e) {\n            return obj[e];\n        });\n    };\n    util.objectKeys = typeof Object.keys === \"function\" // eslint-disable-line ban/ban\n     ? (obj)=>Object.keys(obj) // eslint-disable-line ban/ban\n     : (object)=>{\n        const keys = [];\n        for(const key in object){\n            if (Object.prototype.hasOwnProperty.call(object, key)) {\n                keys.push(key);\n            }\n        }\n        return keys;\n    };\n    util.find = (arr, checker)=>{\n        for (const item of arr){\n            if (checker(item)) return item;\n        }\n        return undefined;\n    };\n    util.isInteger = typeof Number.isInteger === \"function\" ? (val)=>Number.isInteger(val) // eslint-disable-line ban/ban\n     : (val)=>typeof val === \"number\" && isFinite(val) && Math.floor(val) === val;\n    function joinValues(array, separator = \" | \") {\n        return array.map((val)=>typeof val === \"string\" ? `'${val}'` : val).join(separator);\n    }\n    util.joinValues = joinValues;\n    util.jsonStringifyReplacer = (_, value)=>{\n        if (typeof value === \"bigint\") {\n            return value.toString();\n        }\n        return value;\n    };\n})(util || (util = {}));\nvar objectUtil;\n(function(objectUtil) {\n    objectUtil.mergeShapes = (first, second)=>{\n        return {\n            ...first,\n            ...second\n        };\n    };\n})(objectUtil || (objectUtil = {}));\nconst ZodParsedType = util.arrayToEnum([\n    \"string\",\n    \"nan\",\n    \"number\",\n    \"integer\",\n    \"float\",\n    \"boolean\",\n    \"date\",\n    \"bigint\",\n    \"symbol\",\n    \"function\",\n    \"undefined\",\n    \"null\",\n    \"array\",\n    \"object\",\n    \"unknown\",\n    \"promise\",\n    \"void\",\n    \"never\",\n    \"map\",\n    \"set\"\n]);\nconst getParsedType = (data)=>{\n    const t = typeof data;\n    switch(t){\n        case \"undefined\":\n            return ZodParsedType.undefined;\n        case \"string\":\n            return ZodParsedType.string;\n        case \"number\":\n            return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;\n        case \"boolean\":\n            return ZodParsedType.boolean;\n        case \"function\":\n            return ZodParsedType.function;\n        case \"bigint\":\n            return ZodParsedType.bigint;\n        case \"symbol\":\n            return ZodParsedType.symbol;\n        case \"object\":\n            if (Array.isArray(data)) {\n                return ZodParsedType.array;\n            }\n            if (data === null) {\n                return ZodParsedType.null;\n            }\n            if (data.then && typeof data.then === \"function\" && data.catch && typeof data.catch === \"function\") {\n                return ZodParsedType.promise;\n            }\n            if (typeof Map !== \"undefined\" && data instanceof Map) {\n                return ZodParsedType.map;\n            }\n            if (typeof Set !== \"undefined\" && data instanceof Set) {\n                return ZodParsedType.set;\n            }\n            if (typeof Date !== \"undefined\" && data instanceof Date) {\n                return ZodParsedType.date;\n            }\n            return ZodParsedType.object;\n        default:\n            return ZodParsedType.unknown;\n    }\n};\nconst ZodIssueCode = util.arrayToEnum([\n    \"invalid_type\",\n    \"invalid_literal\",\n    \"custom\",\n    \"invalid_union\",\n    \"invalid_union_discriminator\",\n    \"invalid_enum_value\",\n    \"unrecognized_keys\",\n    \"invalid_arguments\",\n    \"invalid_return_type\",\n    \"invalid_date\",\n    \"invalid_string\",\n    \"too_small\",\n    \"too_big\",\n    \"invalid_intersection_types\",\n    \"not_multiple_of\",\n    \"not_finite\"\n]);\nconst quotelessJson = (obj)=>{\n    const json = JSON.stringify(obj, null, 2);\n    return json.replace(/\"([^\"]+)\":/g, \"$1:\");\n};\nclass ZodError extends Error {\n    constructor(issues){\n        super();\n        this.issues = [];\n        this.addIssue = (sub)=>{\n            this.issues = [\n                ...this.issues,\n                sub\n            ];\n        };\n        this.addIssues = (subs = [])=>{\n            this.issues = [\n                ...this.issues,\n                ...subs\n            ];\n        };\n        const actualProto = new.target.prototype;\n        if (Object.setPrototypeOf) {\n            // eslint-disable-next-line ban/ban\n            Object.setPrototypeOf(this, actualProto);\n        } else {\n            this.__proto__ = actualProto;\n        }\n        this.name = \"ZodError\";\n        this.issues = issues;\n    }\n    get errors() {\n        return this.issues;\n    }\n    format(_mapper) {\n        const mapper = _mapper || function(issue) {\n            return issue.message;\n        };\n        const fieldErrors = {\n            _errors: []\n        };\n        const processError = (error)=>{\n            for (const issue of error.issues){\n                if (issue.code === \"invalid_union\") {\n                    issue.unionErrors.map(processError);\n                } else if (issue.code === \"invalid_return_type\") {\n                    processError(issue.returnTypeError);\n                } else if (issue.code === \"invalid_arguments\") {\n                    processError(issue.argumentsError);\n                } else if (issue.path.length === 0) {\n                    fieldErrors._errors.push(mapper(issue));\n                } else {\n                    let curr = fieldErrors;\n                    let i = 0;\n                    while(i < issue.path.length){\n                        const el = issue.path[i];\n                        const terminal = i === issue.path.length - 1;\n                        if (!terminal) {\n                            curr[el] = curr[el] || {\n                                _errors: []\n                            };\n                        // if (typeof el === \"string\") {\n                        //   curr[el] = curr[el] || { _errors: [] };\n                        // } else if (typeof el === \"number\") {\n                        //   const errorArray: any = [];\n                        //   errorArray._errors = [];\n                        //   curr[el] = curr[el] || errorArray;\n                        // }\n                        } else {\n                            curr[el] = curr[el] || {\n                                _errors: []\n                            };\n                            curr[el]._errors.push(mapper(issue));\n                        }\n                        curr = curr[el];\n                        i++;\n                    }\n                }\n            }\n        };\n        processError(this);\n        return fieldErrors;\n    }\n    toString() {\n        return this.message;\n    }\n    get message() {\n        return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);\n    }\n    get isEmpty() {\n        return this.issues.length === 0;\n    }\n    flatten(mapper = (issue)=>issue.message) {\n        const fieldErrors = {};\n        const formErrors = [];\n        for (const sub of this.issues){\n            if (sub.path.length > 0) {\n                fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n                fieldErrors[sub.path[0]].push(mapper(sub));\n            } else {\n                formErrors.push(mapper(sub));\n            }\n        }\n        return {\n            formErrors,\n            fieldErrors\n        };\n    }\n    get formErrors() {\n        return this.flatten();\n    }\n}\nZodError.create = (issues)=>{\n    const error = new ZodError(issues);\n    return error;\n};\nconst errorMap = (issue, _ctx)=>{\n    let message;\n    switch(issue.code){\n        case ZodIssueCode.invalid_type:\n            if (issue.received === ZodParsedType.undefined) {\n                message = \"Required\";\n            } else {\n                message = `Expected ${issue.expected}, received ${issue.received}`;\n            }\n            break;\n        case ZodIssueCode.invalid_literal:\n            message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;\n            break;\n        case ZodIssueCode.unrecognized_keys:\n            message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, \", \")}`;\n            break;\n        case ZodIssueCode.invalid_union:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_union_discriminator:\n            message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;\n            break;\n        case ZodIssueCode.invalid_enum_value:\n            message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;\n            break;\n        case ZodIssueCode.invalid_arguments:\n            message = `Invalid function arguments`;\n            break;\n        case ZodIssueCode.invalid_return_type:\n            message = `Invalid function return type`;\n            break;\n        case ZodIssueCode.invalid_date:\n            message = `Invalid date`;\n            break;\n        case ZodIssueCode.invalid_string:\n            if (typeof issue.validation === \"object\") {\n                if (\"includes\" in issue.validation) {\n                    message = `Invalid input: must include \"${issue.validation.includes}\"`;\n                    if (typeof issue.validation.position === \"number\") {\n                        message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;\n                    }\n                } else if (\"startsWith\" in issue.validation) {\n                    message = `Invalid input: must start with \"${issue.validation.startsWith}\"`;\n                } else if (\"endsWith\" in issue.validation) {\n                    message = `Invalid input: must end with \"${issue.validation.endsWith}\"`;\n                } else {\n                    util.assertNever(issue.validation);\n                }\n            } else if (issue.validation !== \"regex\") {\n                message = `Invalid ${issue.validation}`;\n            } else {\n                message = \"Invalid\";\n            }\n            break;\n        case ZodIssueCode.too_small:\n            if (issue.type === \"array\") message = `Array must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;\n            else if (issue.type === \"string\") message = `String must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;\n            else if (issue.type === \"number\") message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;\n            else if (issue.type === \"date\") message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;\n            else message = \"Invalid input\";\n            break;\n        case ZodIssueCode.too_big:\n            if (issue.type === \"array\") message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;\n            else if (issue.type === \"string\") message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;\n            else if (issue.type === \"number\") message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"bigint\") message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"date\") message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;\n            else message = \"Invalid input\";\n            break;\n        case ZodIssueCode.custom:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_intersection_types:\n            message = `Intersection results could not be merged`;\n            break;\n        case ZodIssueCode.not_multiple_of:\n            message = `Number must be a multiple of ${issue.multipleOf}`;\n            break;\n        case ZodIssueCode.not_finite:\n            message = \"Number must be finite\";\n            break;\n        default:\n            message = _ctx.defaultError;\n            util.assertNever(issue);\n    }\n    return {\n        message\n    };\n};\nlet overrideErrorMap = errorMap;\nfunction setErrorMap(map) {\n    overrideErrorMap = map;\n}\nfunction getErrorMap() {\n    return overrideErrorMap;\n}\nconst makeIssue = (params)=>{\n    const { data, path, errorMaps, issueData } = params;\n    const fullPath = [\n        ...path,\n        ...issueData.path || []\n    ];\n    const fullIssue = {\n        ...issueData,\n        path: fullPath\n    };\n    let errorMessage = \"\";\n    const maps = errorMaps.filter((m)=>!!m).slice().reverse();\n    for (const map of maps){\n        errorMessage = map(fullIssue, {\n            data,\n            defaultError: errorMessage\n        }).message;\n    }\n    return {\n        ...issueData,\n        path: fullPath,\n        message: issueData.message || errorMessage\n    };\n};\nconst EMPTY_PATH = [];\nfunction addIssueToContext(ctx, issueData) {\n    const issue = makeIssue({\n        issueData: issueData,\n        data: ctx.data,\n        path: ctx.path,\n        errorMaps: [\n            ctx.common.contextualErrorMap,\n            ctx.schemaErrorMap,\n            getErrorMap(),\n            errorMap\n        ].filter((x)=>!!x)\n    });\n    ctx.common.issues.push(issue);\n}\nclass ParseStatus {\n    constructor(){\n        this.value = \"valid\";\n    }\n    dirty() {\n        if (this.value === \"valid\") this.value = \"dirty\";\n    }\n    abort() {\n        if (this.value !== \"aborted\") this.value = \"aborted\";\n    }\n    static mergeArray(status, results) {\n        const arrayValue = [];\n        for (const s of results){\n            if (s.status === \"aborted\") return INVALID;\n            if (s.status === \"dirty\") status.dirty();\n            arrayValue.push(s.value);\n        }\n        return {\n            status: status.value,\n            value: arrayValue\n        };\n    }\n    static async mergeObjectAsync(status, pairs) {\n        const syncPairs = [];\n        for (const pair of pairs){\n            syncPairs.push({\n                key: await pair.key,\n                value: await pair.value\n            });\n        }\n        return ParseStatus.mergeObjectSync(status, syncPairs);\n    }\n    static mergeObjectSync(status, pairs) {\n        const finalObject = {};\n        for (const pair of pairs){\n            const { key, value } = pair;\n            if (key.status === \"aborted\") return INVALID;\n            if (value.status === \"aborted\") return INVALID;\n            if (key.status === \"dirty\") status.dirty();\n            if (value.status === \"dirty\") status.dirty();\n            if (key.value !== \"__proto__\" && (typeof value.value !== \"undefined\" || pair.alwaysSet)) {\n                finalObject[key.value] = value.value;\n            }\n        }\n        return {\n            status: status.value,\n            value: finalObject\n        };\n    }\n}\nconst INVALID = Object.freeze({\n    status: \"aborted\"\n});\nconst DIRTY = (value)=>({\n        status: \"dirty\",\n        value\n    });\nconst OK = (value)=>({\n        status: \"valid\",\n        value\n    });\nconst isAborted = (x)=>x.status === \"aborted\";\nconst isDirty = (x)=>x.status === \"dirty\";\nconst isValid = (x)=>x.status === \"valid\";\nconst isAsync = (x)=>typeof Promise !== \"undefined\" && x instanceof Promise;\nvar errorUtil;\n(function(errorUtil) {\n    errorUtil.errToObj = (message)=>typeof message === \"string\" ? {\n            message\n        } : message || {};\n    errorUtil.toString = (message)=>typeof message === \"string\" ? message : message === null || message === void 0 ? void 0 : message.message;\n})(errorUtil || (errorUtil = {}));\nclass ParseInputLazyPath {\n    constructor(parent, value, path, key){\n        this._cachedPath = [];\n        this.parent = parent;\n        this.data = value;\n        this._path = path;\n        this._key = key;\n    }\n    get path() {\n        if (!this._cachedPath.length) {\n            if (this._key instanceof Array) {\n                this._cachedPath.push(...this._path, ...this._key);\n            } else {\n                this._cachedPath.push(...this._path, this._key);\n            }\n        }\n        return this._cachedPath;\n    }\n}\nconst handleResult = (ctx, result)=>{\n    if (isValid(result)) {\n        return {\n            success: true,\n            data: result.value\n        };\n    } else {\n        if (!ctx.common.issues.length) {\n            throw new Error(\"Validation failed but no issues detected.\");\n        }\n        return {\n            success: false,\n            get error () {\n                if (this._error) return this._error;\n                const error = new ZodError(ctx.common.issues);\n                this._error = error;\n                return this._error;\n            }\n        };\n    }\n};\nfunction processCreateParams(params) {\n    if (!params) return {};\n    const { errorMap, invalid_type_error, required_error, description } = params;\n    if (errorMap && (invalid_type_error || required_error)) {\n        throw new Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`);\n    }\n    if (errorMap) return {\n        errorMap: errorMap,\n        description\n    };\n    const customMap = (iss, ctx)=>{\n        if (iss.code !== \"invalid_type\") return {\n            message: ctx.defaultError\n        };\n        if (typeof ctx.data === \"undefined\") {\n            return {\n                message: required_error !== null && required_error !== void 0 ? required_error : ctx.defaultError\n            };\n        }\n        return {\n            message: invalid_type_error !== null && invalid_type_error !== void 0 ? invalid_type_error : ctx.defaultError\n        };\n    };\n    return {\n        errorMap: customMap,\n        description\n    };\n}\nclass ZodType {\n    constructor(def){\n        /** Alias of safeParseAsync */ this.spa = this.safeParseAsync;\n        this._def = def;\n        this.parse = this.parse.bind(this);\n        this.safeParse = this.safeParse.bind(this);\n        this.parseAsync = this.parseAsync.bind(this);\n        this.safeParseAsync = this.safeParseAsync.bind(this);\n        this.spa = this.spa.bind(this);\n        this.refine = this.refine.bind(this);\n        this.refinement = this.refinement.bind(this);\n        this.superRefine = this.superRefine.bind(this);\n        this.optional = this.optional.bind(this);\n        this.nullable = this.nullable.bind(this);\n        this.nullish = this.nullish.bind(this);\n        this.array = this.array.bind(this);\n        this.promise = this.promise.bind(this);\n        this.or = this.or.bind(this);\n        this.and = this.and.bind(this);\n        this.transform = this.transform.bind(this);\n        this.brand = this.brand.bind(this);\n        this.default = this.default.bind(this);\n        this.catch = this.catch.bind(this);\n        this.describe = this.describe.bind(this);\n        this.pipe = this.pipe.bind(this);\n        this.readonly = this.readonly.bind(this);\n        this.isNullable = this.isNullable.bind(this);\n        this.isOptional = this.isOptional.bind(this);\n    }\n    get description() {\n        return this._def.description;\n    }\n    _getType(input) {\n        return getParsedType(input.data);\n    }\n    _getOrReturnCtx(input, ctx) {\n        return ctx || {\n            common: input.parent.common,\n            data: input.data,\n            parsedType: getParsedType(input.data),\n            schemaErrorMap: this._def.errorMap,\n            path: input.path,\n            parent: input.parent\n        };\n    }\n    _processInputParams(input) {\n        return {\n            status: new ParseStatus(),\n            ctx: {\n                common: input.parent.common,\n                data: input.data,\n                parsedType: getParsedType(input.data),\n                schemaErrorMap: this._def.errorMap,\n                path: input.path,\n                parent: input.parent\n            }\n        };\n    }\n    _parseSync(input) {\n        const result = this._parse(input);\n        if (isAsync(result)) {\n            throw new Error(\"Synchronous parse encountered promise.\");\n        }\n        return result;\n    }\n    _parseAsync(input) {\n        const result = this._parse(input);\n        return Promise.resolve(result);\n    }\n    parse(data, params) {\n        const result = this.safeParse(data, params);\n        if (result.success) return result.data;\n        throw result.error;\n    }\n    safeParse(data, params) {\n        var _a;\n        const ctx = {\n            common: {\n                issues: [],\n                async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data)\n        };\n        const result = this._parseSync({\n            data,\n            path: ctx.path,\n            parent: ctx\n        });\n        return handleResult(ctx, result);\n    }\n    async parseAsync(data, params) {\n        const result = await this.safeParseAsync(data, params);\n        if (result.success) return result.data;\n        throw result.error;\n    }\n    async safeParseAsync(data, params) {\n        const ctx = {\n            common: {\n                issues: [],\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n                async: true\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data)\n        };\n        const maybeAsyncResult = this._parse({\n            data,\n            path: ctx.path,\n            parent: ctx\n        });\n        const result = await (isAsync(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));\n        return handleResult(ctx, result);\n    }\n    refine(check, message) {\n        const getIssueProperties = (val)=>{\n            if (typeof message === \"string\" || typeof message === \"undefined\") {\n                return {\n                    message\n                };\n            } else if (typeof message === \"function\") {\n                return message(val);\n            } else {\n                return message;\n            }\n        };\n        return this._refinement((val, ctx)=>{\n            const result = check(val);\n            const setError = ()=>ctx.addIssue({\n                    code: ZodIssueCode.custom,\n                    ...getIssueProperties(val)\n                });\n            if (typeof Promise !== \"undefined\" && result instanceof Promise) {\n                return result.then((data)=>{\n                    if (!data) {\n                        setError();\n                        return false;\n                    } else {\n                        return true;\n                    }\n                });\n            }\n            if (!result) {\n                setError();\n                return false;\n            } else {\n                return true;\n            }\n        });\n    }\n    refinement(check, refinementData) {\n        return this._refinement((val, ctx)=>{\n            if (!check(val)) {\n                ctx.addIssue(typeof refinementData === \"function\" ? refinementData(val, ctx) : refinementData);\n                return false;\n            } else {\n                return true;\n            }\n        });\n    }\n    _refinement(refinement) {\n        return new ZodEffects({\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: {\n                type: \"refinement\",\n                refinement\n            }\n        });\n    }\n    superRefine(refinement) {\n        return this._refinement(refinement);\n    }\n    optional() {\n        return ZodOptional.create(this, this._def);\n    }\n    nullable() {\n        return ZodNullable.create(this, this._def);\n    }\n    nullish() {\n        return this.nullable().optional();\n    }\n    array() {\n        return ZodArray.create(this, this._def);\n    }\n    promise() {\n        return ZodPromise.create(this, this._def);\n    }\n    or(option) {\n        return ZodUnion.create([\n            this,\n            option\n        ], this._def);\n    }\n    and(incoming) {\n        return ZodIntersection.create(this, incoming, this._def);\n    }\n    transform(transform) {\n        return new ZodEffects({\n            ...processCreateParams(this._def),\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: {\n                type: \"transform\",\n                transform\n            }\n        });\n    }\n    default(def) {\n        const defaultValueFunc = typeof def === \"function\" ? def : ()=>def;\n        return new ZodDefault({\n            ...processCreateParams(this._def),\n            innerType: this,\n            defaultValue: defaultValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodDefault\n        });\n    }\n    brand() {\n        return new ZodBranded({\n            typeName: ZodFirstPartyTypeKind.ZodBranded,\n            type: this,\n            ...processCreateParams(this._def)\n        });\n    }\n    catch(def) {\n        const catchValueFunc = typeof def === \"function\" ? def : ()=>def;\n        return new ZodCatch({\n            ...processCreateParams(this._def),\n            innerType: this,\n            catchValue: catchValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodCatch\n        });\n    }\n    describe(description) {\n        const This = this.constructor;\n        return new This({\n            ...this._def,\n            description\n        });\n    }\n    pipe(target) {\n        return ZodPipeline.create(this, target);\n    }\n    readonly() {\n        return ZodReadonly.create(this);\n    }\n    isOptional() {\n        return this.safeParse(undefined).success;\n    }\n    isNullable() {\n        return this.safeParse(null).success;\n    }\n}\nconst cuidRegex = /^c[^\\s-]{8,}$/i;\nconst cuid2Regex = /^[a-z][a-z0-9]*$/;\nconst ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/;\n// const uuidRegex =\n//   /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;\nconst uuidRegex = /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/i;\n// from https://stackoverflow.com/a/46181/1550155\n// old version: too slow, didn't support unicode\n// const emailRegex = /^((([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+(\\.([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))$/i;\n//old email regex\n// const emailRegex = /^(([^<>()[\\].,;:\\s@\"]+(\\.[^<>()[\\].,;:\\s@\"]+)*)|(\".+\"))@((?!-)([^<>()[\\].,;:\\s@\"]+\\.)+[^<>()[\\].,;:\\s@\"]{1,})[^-<>()[\\].,;:\\s@\"]$/i;\n// eslint-disable-next-line\n// const emailRegex =\n//   /^(([^<>()[\\]\\\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@((\\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\])|(\\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\\.[A-Za-z]{2,})+))$/;\n// const emailRegex =\n//   /^[a-zA-Z0-9\\.\\!\\#\\$\\%\\&\\'\\*\\+\\/\\=\\?\\^\\_\\`\\{\\|\\}\\~\\-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n// const emailRegex =\n//   /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])$/i;\nconst emailRegex = /^(?!\\.)(?!.*\\.\\.)([A-Z0-9_+-\\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\\-]*\\.)+[A-Z]{2,}$/i;\n// const emailRegex =\n//   /^[a-z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\\.[a-z0-9\\-]+)*$/i;\n// from https://thekevinscott.com/emojis-in-javascript/#writing-a-regular-expression\nconst _emojiRegex = `^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$`;\nlet emojiRegex;\nconst ipv4Regex = /^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/;\nconst ipv6Regex = /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;\n// Adapted from https://stackoverflow.com/a/3143231\nconst datetimeRegex = (args)=>{\n    if (args.precision) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        } else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}Z$`);\n        }\n    } else if (args.precision === 0) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        } else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}Z$`);\n        }\n    } else {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        } else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?Z$`);\n        }\n    }\n};\nfunction isValidIP(ip, version) {\n    if ((version === \"v4\" || !version) && ipv4Regex.test(ip)) {\n        return true;\n    }\n    if ((version === \"v6\" || !version) && ipv6Regex.test(ip)) {\n        return true;\n    }\n    return false;\n}\nclass ZodString extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = String(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.string) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.string,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks){\n            if (check.kind === \"min\") {\n                if (input.data.length < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"max\") {\n                if (input.data.length > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"length\") {\n                const tooBig = input.data.length > check.value;\n                const tooSmall = input.data.length < check.value;\n                if (tooBig || tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    if (tooBig) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_big,\n                            maximum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message\n                        });\n                    } else if (tooSmall) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_small,\n                            minimum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message\n                        });\n                    }\n                    status.dirty();\n                }\n            } else if (check.kind === \"email\") {\n                if (!emailRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"email\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"emoji\") {\n                if (!emojiRegex) {\n                    emojiRegex = new RegExp(_emojiRegex, \"u\");\n                }\n                if (!emojiRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"emoji\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"uuid\") {\n                if (!uuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"uuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"cuid\") {\n                if (!cuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"cuid2\") {\n                if (!cuid2Regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid2\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"ulid\") {\n                if (!ulidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ulid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"url\") {\n                try {\n                    new URL(input.data);\n                } catch (_a) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"url\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"regex\") {\n                check.regex.lastIndex = 0;\n                const testResult = check.regex.test(input.data);\n                if (!testResult) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"regex\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"trim\") {\n                input.data = input.data.trim();\n            } else if (check.kind === \"includes\") {\n                if (!input.data.includes(check.value, check.position)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: {\n                            includes: check.value,\n                            position: check.position\n                        },\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"toLowerCase\") {\n                input.data = input.data.toLowerCase();\n            } else if (check.kind === \"toUpperCase\") {\n                input.data = input.data.toUpperCase();\n            } else if (check.kind === \"startsWith\") {\n                if (!input.data.startsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: {\n                            startsWith: check.value\n                        },\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"endsWith\") {\n                if (!input.data.endsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: {\n                            endsWith: check.value\n                        },\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"datetime\") {\n                const regex = datetimeRegex(check);\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: \"datetime\",\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"ip\") {\n                if (!isValidIP(input.data, check.version)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ip\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: input.data\n        };\n    }\n    _regex(regex, validation, message) {\n        return this.refinement((data)=>regex.test(data), {\n            validation,\n            code: ZodIssueCode.invalid_string,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    _addCheck(check) {\n        return new ZodString({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                check\n            ]\n        });\n    }\n    email(message) {\n        return this._addCheck({\n            kind: \"email\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    url(message) {\n        return this._addCheck({\n            kind: \"url\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    emoji(message) {\n        return this._addCheck({\n            kind: \"emoji\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    uuid(message) {\n        return this._addCheck({\n            kind: \"uuid\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    cuid(message) {\n        return this._addCheck({\n            kind: \"cuid\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    cuid2(message) {\n        return this._addCheck({\n            kind: \"cuid2\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    ulid(message) {\n        return this._addCheck({\n            kind: \"ulid\",\n            ...errorUtil.errToObj(message)\n        });\n    }\n    ip(options) {\n        return this._addCheck({\n            kind: \"ip\",\n            ...errorUtil.errToObj(options)\n        });\n    }\n    datetime(options) {\n        var _a;\n        if (typeof options === \"string\") {\n            return this._addCheck({\n                kind: \"datetime\",\n                precision: null,\n                offset: false,\n                message: options\n            });\n        }\n        return this._addCheck({\n            kind: \"datetime\",\n            precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)\n        });\n    }\n    regex(regex, message) {\n        return this._addCheck({\n            kind: \"regex\",\n            regex: regex,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    includes(value, options) {\n        return this._addCheck({\n            kind: \"includes\",\n            value: value,\n            position: options === null || options === void 0 ? void 0 : options.position,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)\n        });\n    }\n    startsWith(value, message) {\n        return this._addCheck({\n            kind: \"startsWith\",\n            value: value,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    endsWith(value, message) {\n        return this._addCheck({\n            kind: \"endsWith\",\n            value: value,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    min(minLength, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minLength,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    max(maxLength, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxLength,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    length(len, message) {\n        return this._addCheck({\n            kind: \"length\",\n            value: len,\n            ...errorUtil.errToObj(message)\n        });\n    }\n    /**\n     * @deprecated Use z.string().min(1) instead.\n     * @see {@link ZodString.min}\n     */ nonempty(message) {\n        return this.min(1, errorUtil.errToObj(message));\n    }\n    trim() {\n        return new ZodString({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind: \"trim\"\n                }\n            ]\n        });\n    }\n    toLowerCase() {\n        return new ZodString({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind: \"toLowerCase\"\n                }\n            ]\n        });\n    }\n    toUpperCase() {\n        return new ZodString({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind: \"toUpperCase\"\n                }\n            ]\n        });\n    }\n    get isDatetime() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"datetime\");\n    }\n    get isEmail() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"email\");\n    }\n    get isURL() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"url\");\n    }\n    get isEmoji() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"emoji\");\n    }\n    get isUUID() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"uuid\");\n    }\n    get isCUID() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"cuid\");\n    }\n    get isCUID2() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"cuid2\");\n    }\n    get isULID() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"ulid\");\n    }\n    get isIP() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"ip\");\n    }\n    get minLength() {\n        let min = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min) min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxLength() {\n        let max = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max) max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodString.create = (params)=>{\n    var _a;\n    return new ZodString({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodString,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params)\n    });\n};\n// https://stackoverflow.com/questions/3966484/why-does-modulus-operator-return-fractional-number-in-javascript/31711034#31711034\nfunction floatSafeRemainder(val, step) {\n    const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n    const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n    const valInt = parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n    const stepInt = parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n    return valInt % stepInt / Math.pow(10, decCount);\n}\nclass ZodNumber extends ZodType {\n    constructor(){\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n        this.step = this.multipleOf;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Number(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.number) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.number,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks){\n            if (check.kind === \"int\") {\n                if (!util.isInteger(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_type,\n                        expected: \"integer\",\n                        received: \"float\",\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"min\") {\n                const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"max\") {\n                const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"multipleOf\") {\n                if (floatSafeRemainder(input.data, check.value) !== 0) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"finite\") {\n                if (!Number.isFinite(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_finite,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: input.data\n        };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message)\n                }\n            ]\n        });\n    }\n    _addCheck(check) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                check\n            ]\n        });\n    }\n    int(message) {\n        return this._addCheck({\n            kind: \"int\",\n            message: errorUtil.toString(message)\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message)\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message)\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message)\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message)\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value: value,\n            message: errorUtil.toString(message)\n        });\n    }\n    finite(message) {\n        return this._addCheck({\n            kind: \"finite\",\n            message: errorUtil.toString(message)\n        });\n    }\n    safe(message) {\n        return this._addCheck({\n            kind: \"min\",\n            inclusive: true,\n            value: Number.MIN_SAFE_INTEGER,\n            message: errorUtil.toString(message)\n        })._addCheck({\n            kind: \"max\",\n            inclusive: true,\n            value: Number.MAX_SAFE_INTEGER,\n            message: errorUtil.toString(message)\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min) min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max) max = ch.value;\n            }\n        }\n        return max;\n    }\n    get isInt() {\n        return !!this._def.checks.find((ch)=>ch.kind === \"int\" || ch.kind === \"multipleOf\" && util.isInteger(ch.value));\n    }\n    get isFinite() {\n        let max = null, min = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"finite\" || ch.kind === \"int\" || ch.kind === \"multipleOf\") {\n                return true;\n            } else if (ch.kind === \"min\") {\n                if (min === null || ch.value > min) min = ch.value;\n            } else if (ch.kind === \"max\") {\n                if (max === null || ch.value < max) max = ch.value;\n            }\n        }\n        return Number.isFinite(min) && Number.isFinite(max);\n    }\n}\nZodNumber.create = (params)=>{\n    return new ZodNumber({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodNumber,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params)\n    });\n};\nclass ZodBigInt extends ZodType {\n    constructor(){\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = BigInt(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.bigint) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.bigint,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks){\n            if (check.kind === \"min\") {\n                const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        type: \"bigint\",\n                        minimum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"max\") {\n                const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        type: \"bigint\",\n                        maximum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"multipleOf\") {\n                if (input.data % check.value !== BigInt(0)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message\n                    });\n                    status.dirty();\n                }\n            } else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: input.data\n        };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message)\n                }\n            ]\n        });\n    }\n    _addCheck(check) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                check\n            ]\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message)\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message)\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message)\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message)\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value,\n            message: errorUtil.toString(message)\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min) min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max) max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodBigInt.create = (params)=>{\n    var _a;\n    return new ZodBigInt({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodBigInt,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params)\n    });\n};\nclass ZodBoolean extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Boolean(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.boolean) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.boolean,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodBoolean.create = (params)=>{\n    return new ZodBoolean({\n        typeName: ZodFirstPartyTypeKind.ZodBoolean,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params)\n    });\n};\nclass ZodDate extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = new Date(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.date) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.date,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        if (isNaN(input.data.getTime())) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_date\n            });\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks){\n            if (check.kind === \"min\") {\n                if (input.data.getTime() < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        minimum: check.value,\n                        type: \"date\"\n                    });\n                    status.dirty();\n                }\n            } else if (check.kind === \"max\") {\n                if (input.data.getTime() > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        maximum: check.value,\n                        type: \"date\"\n                    });\n                    status.dirty();\n                }\n            } else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: new Date(input.data.getTime())\n        };\n    }\n    _addCheck(check) {\n        return new ZodDate({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                check\n            ]\n        });\n    }\n    min(minDate, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minDate.getTime(),\n            message: errorUtil.toString(message)\n        });\n    }\n    max(maxDate, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxDate.getTime(),\n            message: errorUtil.toString(message)\n        });\n    }\n    get minDate() {\n        let min = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min) min = ch.value;\n            }\n        }\n        return min != null ? new Date(min) : null;\n    }\n    get maxDate() {\n        let max = null;\n        for (const ch of this._def.checks){\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max) max = ch.value;\n            }\n        }\n        return max != null ? new Date(max) : null;\n    }\n}\nZodDate.create = (params)=>{\n    return new ZodDate({\n        checks: [],\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        typeName: ZodFirstPartyTypeKind.ZodDate,\n        ...processCreateParams(params)\n    });\n};\nclass ZodSymbol extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.symbol) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.symbol,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodSymbol.create = (params)=>{\n    return new ZodSymbol({\n        typeName: ZodFirstPartyTypeKind.ZodSymbol,\n        ...processCreateParams(params)\n    });\n};\nclass ZodUndefined extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.undefined,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodUndefined.create = (params)=>{\n    return new ZodUndefined({\n        typeName: ZodFirstPartyTypeKind.ZodUndefined,\n        ...processCreateParams(params)\n    });\n};\nclass ZodNull extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.null) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.null,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodNull.create = (params)=>{\n    return new ZodNull({\n        typeName: ZodFirstPartyTypeKind.ZodNull,\n        ...processCreateParams(params)\n    });\n};\nclass ZodAny extends ZodType {\n    constructor(){\n        super(...arguments);\n        // to prevent instances of other classes from extending ZodAny. this causes issues with catchall in ZodObject.\n        this._any = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodAny.create = (params)=>{\n    return new ZodAny({\n        typeName: ZodFirstPartyTypeKind.ZodAny,\n        ...processCreateParams(params)\n    });\n};\nclass ZodUnknown extends ZodType {\n    constructor(){\n        super(...arguments);\n        // required\n        this._unknown = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodUnknown.create = (params)=>{\n    return new ZodUnknown({\n        typeName: ZodFirstPartyTypeKind.ZodUnknown,\n        ...processCreateParams(params)\n    });\n};\nclass ZodNever extends ZodType {\n    _parse(input) {\n        const ctx = this._getOrReturnCtx(input);\n        addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_type,\n            expected: ZodParsedType.never,\n            received: ctx.parsedType\n        });\n        return INVALID;\n    }\n}\nZodNever.create = (params)=>{\n    return new ZodNever({\n        typeName: ZodFirstPartyTypeKind.ZodNever,\n        ...processCreateParams(params)\n    });\n};\nclass ZodVoid extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.void,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodVoid.create = (params)=>{\n    return new ZodVoid({\n        typeName: ZodFirstPartyTypeKind.ZodVoid,\n        ...processCreateParams(params)\n    });\n};\nclass ZodArray extends ZodType {\n    _parse(input) {\n        const { ctx, status } = this._processInputParams(input);\n        const def = this._def;\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        if (def.exactLength !== null) {\n            const tooBig = ctx.data.length > def.exactLength.value;\n            const tooSmall = ctx.data.length < def.exactLength.value;\n            if (tooBig || tooSmall) {\n                addIssueToContext(ctx, {\n                    code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,\n                    minimum: tooSmall ? def.exactLength.value : undefined,\n                    maximum: tooBig ? def.exactLength.value : undefined,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: true,\n                    message: def.exactLength.message\n                });\n                status.dirty();\n            }\n        }\n        if (def.minLength !== null) {\n            if (ctx.data.length < def.minLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minLength.message\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxLength !== null) {\n            if (ctx.data.length > def.maxLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxLength.message\n                });\n                status.dirty();\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.all([\n                ...ctx.data\n            ].map((item, i)=>{\n                return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n            })).then((result)=>{\n                return ParseStatus.mergeArray(status, result);\n            });\n        }\n        const result = [\n            ...ctx.data\n        ].map((item, i)=>{\n            return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n        });\n        return ParseStatus.mergeArray(status, result);\n    }\n    get element() {\n        return this._def.type;\n    }\n    min(minLength, message) {\n        return new ZodArray({\n            ...this._def,\n            minLength: {\n                value: minLength,\n                message: errorUtil.toString(message)\n            }\n        });\n    }\n    max(maxLength, message) {\n        return new ZodArray({\n            ...this._def,\n            maxLength: {\n                value: maxLength,\n                message: errorUtil.toString(message)\n            }\n        });\n    }\n    length(len, message) {\n        return new ZodArray({\n            ...this._def,\n            exactLength: {\n                value: len,\n                message: errorUtil.toString(message)\n            }\n        });\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodArray.create = (schema, params)=>{\n    return new ZodArray({\n        type: schema,\n        minLength: null,\n        maxLength: null,\n        exactLength: null,\n        typeName: ZodFirstPartyTypeKind.ZodArray,\n        ...processCreateParams(params)\n    });\n};\nfunction deepPartialify(schema) {\n    if (schema instanceof ZodObject) {\n        const newShape = {};\n        for(const key in schema.shape){\n            const fieldSchema = schema.shape[key];\n            newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));\n        }\n        return new ZodObject({\n            ...schema._def,\n            shape: ()=>newShape\n        });\n    } else if (schema instanceof ZodArray) {\n        return new ZodArray({\n            ...schema._def,\n            type: deepPartialify(schema.element)\n        });\n    } else if (schema instanceof ZodOptional) {\n        return ZodOptional.create(deepPartialify(schema.unwrap()));\n    } else if (schema instanceof ZodNullable) {\n        return ZodNullable.create(deepPartialify(schema.unwrap()));\n    } else if (schema instanceof ZodTuple) {\n        return ZodTuple.create(schema.items.map((item)=>deepPartialify(item)));\n    } else {\n        return schema;\n    }\n}\nclass ZodObject extends ZodType {\n    constructor(){\n        super(...arguments);\n        this._cached = null;\n        /**\n         * @deprecated In most cases, this is no longer needed - unknown properties are now silently stripped.\n         * If you want to pass through unknown properties, use `.passthrough()` instead.\n         */ this.nonstrict = this.passthrough;\n        // extend<\n        //   Augmentation extends ZodRawShape,\n        //   NewOutput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_output\"]\n        //       : k extends keyof Output\n        //       ? Output[k]\n        //       : never;\n        //   }>,\n        //   NewInput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_input\"]\n        //       : k extends keyof Input\n        //       ? Input[k]\n        //       : never;\n        //   }>\n        // >(\n        //   augmentation: Augmentation\n        // ): ZodObject<\n        //   extendShape<T, Augmentation>,\n        //   UnknownKeys,\n        //   Catchall,\n        //   NewOutput,\n        //   NewInput\n        // > {\n        //   return new ZodObject({\n        //     ...this._def,\n        //     shape: () => ({\n        //       ...this._def.shape(),\n        //       ...augmentation,\n        //     }),\n        //   }) as any;\n        // }\n        /**\n         * @deprecated Use `.extend` instead\n         *  */ this.augment = this.extend;\n    }\n    _getCached() {\n        if (this._cached !== null) return this._cached;\n        const shape = this._def.shape();\n        const keys = util.objectKeys(shape);\n        return this._cached = {\n            shape,\n            keys\n        };\n    }\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.object) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const { status, ctx } = this._processInputParams(input);\n        const { shape, keys: shapeKeys } = this._getCached();\n        const extraKeys = [];\n        if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === \"strip\")) {\n            for(const key in ctx.data){\n                if (!shapeKeys.includes(key)) {\n                    extraKeys.push(key);\n                }\n            }\n        }\n        const pairs = [];\n        for (const key of shapeKeys){\n            const keyValidator = shape[key];\n            const value = ctx.data[key];\n            pairs.push({\n                key: {\n                    status: \"valid\",\n                    value: key\n                },\n                value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n                alwaysSet: key in ctx.data\n            });\n        }\n        if (this._def.catchall instanceof ZodNever) {\n            const unknownKeys = this._def.unknownKeys;\n            if (unknownKeys === \"passthrough\") {\n                for (const key of extraKeys){\n                    pairs.push({\n                        key: {\n                            status: \"valid\",\n                            value: key\n                        },\n                        value: {\n                            status: \"valid\",\n                            value: ctx.data[key]\n                        }\n                    });\n                }\n            } else if (unknownKeys === \"strict\") {\n                if (extraKeys.length > 0) {\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.unrecognized_keys,\n                        keys: extraKeys\n                    });\n                    status.dirty();\n                }\n            } else if (unknownKeys === \"strip\") ;\n            else {\n                throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);\n            }\n        } else {\n            // run catchall validation\n            const catchall = this._def.catchall;\n            for (const key of extraKeys){\n                const value = ctx.data[key];\n                pairs.push({\n                    key: {\n                        status: \"valid\",\n                        value: key\n                    },\n                    value: catchall._parse(new ParseInputLazyPath(ctx, value, ctx.path, key) //, ctx.child(key), value, getParsedType(value)\n                    ),\n                    alwaysSet: key in ctx.data\n                });\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.resolve().then(async ()=>{\n                const syncPairs = [];\n                for (const pair of pairs){\n                    const key = await pair.key;\n                    syncPairs.push({\n                        key,\n                        value: await pair.value,\n                        alwaysSet: pair.alwaysSet\n                    });\n                }\n                return syncPairs;\n            }).then((syncPairs)=>{\n                return ParseStatus.mergeObjectSync(status, syncPairs);\n            });\n        } else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get shape() {\n        return this._def.shape();\n    }\n    strict(message) {\n        errorUtil.errToObj;\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strict\",\n            ...message !== undefined ? {\n                errorMap: (issue, ctx)=>{\n                    var _a, _b, _c, _d;\n                    const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;\n                    if (issue.code === \"unrecognized_keys\") return {\n                        message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError\n                    };\n                    return {\n                        message: defaultError\n                    };\n                }\n            } : {}\n        });\n    }\n    strip() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strip\"\n        });\n    }\n    passthrough() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"passthrough\"\n        });\n    }\n    // const AugmentFactory =\n    //   <Def extends ZodObjectDef>(def: Def) =>\n    //   <Augmentation extends ZodRawShape>(\n    //     augmentation: Augmentation\n    //   ): ZodObject<\n    //     extendShape<ReturnType<Def[\"shape\"]>, Augmentation>,\n    //     Def[\"unknownKeys\"],\n    //     Def[\"catchall\"]\n    //   > => {\n    //     return new ZodObject({\n    //       ...def,\n    //       shape: () => ({\n    //         ...def.shape(),\n    //         ...augmentation,\n    //       }),\n    //     }) as any;\n    //   };\n    extend(augmentation) {\n        return new ZodObject({\n            ...this._def,\n            shape: ()=>({\n                    ...this._def.shape(),\n                    ...augmentation\n                })\n        });\n    }\n    /**\n     * Prior to zod@1.0.12 there was a bug in the\n     * inferred type of merged objects. Please\n     * upgrade if you are experiencing issues.\n     */ merge(merging) {\n        const merged = new ZodObject({\n            unknownKeys: merging._def.unknownKeys,\n            catchall: merging._def.catchall,\n            shape: ()=>({\n                    ...this._def.shape(),\n                    ...merging._def.shape()\n                }),\n            typeName: ZodFirstPartyTypeKind.ZodObject\n        });\n        return merged;\n    }\n    // merge<\n    //   Incoming extends AnyZodObject,\n    //   Augmentation extends Incoming[\"shape\"],\n    //   NewOutput extends {\n    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_output\"]\n    //       : k extends keyof Output\n    //       ? Output[k]\n    //       : never;\n    //   },\n    //   NewInput extends {\n    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_input\"]\n    //       : k extends keyof Input\n    //       ? Input[k]\n    //       : never;\n    //   }\n    // >(\n    //   merging: Incoming\n    // ): ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"],\n    //   NewOutput,\n    //   NewInput\n    // > {\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    setKey(key, schema) {\n        return this.augment({\n            [key]: schema\n        });\n    }\n    // merge<Incoming extends AnyZodObject>(\n    //   merging: Incoming\n    // ): //ZodObject<T & Incoming[\"_shape\"], UnknownKeys, Catchall> = (merging) => {\n    // ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"]\n    // > {\n    //   // const mergedShape = objectUtil.mergeShapes(\n    //   //   this._def.shape(),\n    //   //   merging._def.shape()\n    //   // );\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    catchall(index) {\n        return new ZodObject({\n            ...this._def,\n            catchall: index\n        });\n    }\n    pick(mask) {\n        const shape = {};\n        util.objectKeys(mask).forEach((key)=>{\n            if (mask[key] && this.shape[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: ()=>shape\n        });\n    }\n    omit(mask) {\n        const shape = {};\n        util.objectKeys(this.shape).forEach((key)=>{\n            if (!mask[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: ()=>shape\n        });\n    }\n    /**\n     * @deprecated\n     */ deepPartial() {\n        return deepPartialify(this);\n    }\n    partial(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key)=>{\n            const fieldSchema = this.shape[key];\n            if (mask && !mask[key]) {\n                newShape[key] = fieldSchema;\n            } else {\n                newShape[key] = fieldSchema.optional();\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: ()=>newShape\n        });\n    }\n    required(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key)=>{\n            if (mask && !mask[key]) {\n                newShape[key] = this.shape[key];\n            } else {\n                const fieldSchema = this.shape[key];\n                let newField = fieldSchema;\n                while(newField instanceof ZodOptional){\n                    newField = newField._def.innerType;\n                }\n                newShape[key] = newField;\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: ()=>newShape\n        });\n    }\n    keyof() {\n        return createZodEnum(util.objectKeys(this.shape));\n    }\n}\nZodObject.create = (shape, params)=>{\n    return new ZodObject({\n        shape: ()=>shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params)\n    });\n};\nZodObject.strictCreate = (shape, params)=>{\n    return new ZodObject({\n        shape: ()=>shape,\n        unknownKeys: \"strict\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params)\n    });\n};\nZodObject.lazycreate = (shape, params)=>{\n    return new ZodObject({\n        shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params)\n    });\n};\nclass ZodUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const options = this._def.options;\n        function handleResults(results) {\n            // return first issue-free validation if it exists\n            for (const result of results){\n                if (result.result.status === \"valid\") {\n                    return result.result;\n                }\n            }\n            for (const result of results){\n                if (result.result.status === \"dirty\") {\n                    // add issues from dirty option\n                    ctx.common.issues.push(...result.ctx.common.issues);\n                    return result.result;\n                }\n            }\n            // return invalid\n            const unionErrors = results.map((result)=>new ZodError(result.ctx.common.issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return Promise.all(options.map(async (option)=>{\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: []\n                    },\n                    parent: null\n                };\n                return {\n                    result: await option._parseAsync({\n                        data: ctx.data,\n                        path: ctx.path,\n                        parent: childCtx\n                    }),\n                    ctx: childCtx\n                };\n            })).then(handleResults);\n        } else {\n            let dirty = undefined;\n            const issues = [];\n            for (const option of options){\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: []\n                    },\n                    parent: null\n                };\n                const result = option._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: childCtx\n                });\n                if (result.status === \"valid\") {\n                    return result;\n                } else if (result.status === \"dirty\" && !dirty) {\n                    dirty = {\n                        result,\n                        ctx: childCtx\n                    };\n                }\n                if (childCtx.common.issues.length) {\n                    issues.push(childCtx.common.issues);\n                }\n            }\n            if (dirty) {\n                ctx.common.issues.push(...dirty.ctx.common.issues);\n                return dirty.result;\n            }\n            const unionErrors = issues.map((issues)=>new ZodError(issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors\n            });\n            return INVALID;\n        }\n    }\n    get options() {\n        return this._def.options;\n    }\n}\nZodUnion.create = (types, params)=>{\n    return new ZodUnion({\n        options: types,\n        typeName: ZodFirstPartyTypeKind.ZodUnion,\n        ...processCreateParams(params)\n    });\n};\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\n//////////                                 //////////\n//////////      ZodDiscriminatedUnion      //////////\n//////////                                 //////////\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\nconst getDiscriminator = (type)=>{\n    if (type instanceof ZodLazy) {\n        return getDiscriminator(type.schema);\n    } else if (type instanceof ZodEffects) {\n        return getDiscriminator(type.innerType());\n    } else if (type instanceof ZodLiteral) {\n        return [\n            type.value\n        ];\n    } else if (type instanceof ZodEnum) {\n        return type.options;\n    } else if (type instanceof ZodNativeEnum) {\n        // eslint-disable-next-line ban/ban\n        return Object.keys(type.enum);\n    } else if (type instanceof ZodDefault) {\n        return getDiscriminator(type._def.innerType);\n    } else if (type instanceof ZodUndefined) {\n        return [\n            undefined\n        ];\n    } else if (type instanceof ZodNull) {\n        return [\n            null\n        ];\n    } else {\n        return null;\n    }\n};\nclass ZodDiscriminatedUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const discriminator = this.discriminator;\n        const discriminatorValue = ctx.data[discriminator];\n        const option = this.optionsMap.get(discriminatorValue);\n        if (!option) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union_discriminator,\n                options: Array.from(this.optionsMap.keys()),\n                path: [\n                    discriminator\n                ]\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return option._parseAsync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx\n            });\n        } else {\n            return option._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx\n            });\n        }\n    }\n    get discriminator() {\n        return this._def.discriminator;\n    }\n    get options() {\n        return this._def.options;\n    }\n    get optionsMap() {\n        return this._def.optionsMap;\n    }\n    /**\n     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.\n     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must\n     * have a different value for each object in the union.\n     * @param discriminator the name of the discriminator property\n     * @param types an array of object schemas\n     * @param params\n     */ static create(discriminator, options, params) {\n        // Get all the valid discriminator values\n        const optionsMap = new Map();\n        // try {\n        for (const type of options){\n            const discriminatorValues = getDiscriminator(type.shape[discriminator]);\n            if (!discriminatorValues) {\n                throw new Error(`A discriminator value for key \\`${discriminator}\\` could not be extracted from all schema options`);\n            }\n            for (const value of discriminatorValues){\n                if (optionsMap.has(value)) {\n                    throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);\n                }\n                optionsMap.set(value, type);\n            }\n        }\n        return new ZodDiscriminatedUnion({\n            typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,\n            discriminator,\n            options,\n            optionsMap,\n            ...processCreateParams(params)\n        });\n    }\n}\nfunction mergeValues(a, b) {\n    const aType = getParsedType(a);\n    const bType = getParsedType(b);\n    if (a === b) {\n        return {\n            valid: true,\n            data: a\n        };\n    } else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {\n        const bKeys = util.objectKeys(b);\n        const sharedKeys = util.objectKeys(a).filter((key)=>bKeys.indexOf(key) !== -1);\n        const newObj = {\n            ...a,\n            ...b\n        };\n        for (const key of sharedKeys){\n            const sharedValue = mergeValues(a[key], b[key]);\n            if (!sharedValue.valid) {\n                return {\n                    valid: false\n                };\n            }\n            newObj[key] = sharedValue.data;\n        }\n        return {\n            valid: true,\n            data: newObj\n        };\n    } else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {\n        if (a.length !== b.length) {\n            return {\n                valid: false\n            };\n        }\n        const newArray = [];\n        for(let index = 0; index < a.length; index++){\n            const itemA = a[index];\n            const itemB = b[index];\n            const sharedValue = mergeValues(itemA, itemB);\n            if (!sharedValue.valid) {\n                return {\n                    valid: false\n                };\n            }\n            newArray.push(sharedValue.data);\n        }\n        return {\n            valid: true,\n            data: newArray\n        };\n    } else if (aType === ZodParsedType.date && bType === ZodParsedType.date && +a === +b) {\n        return {\n            valid: true,\n            data: a\n        };\n    } else {\n        return {\n            valid: false\n        };\n    }\n}\nclass ZodIntersection extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const handleParsed = (parsedLeft, parsedRight)=>{\n            if (isAborted(parsedLeft) || isAborted(parsedRight)) {\n                return INVALID;\n            }\n            const merged = mergeValues(parsedLeft.value, parsedRight.value);\n            if (!merged.valid) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.invalid_intersection_types\n                });\n                return INVALID;\n            }\n            if (isDirty(parsedLeft) || isDirty(parsedRight)) {\n                status.dirty();\n            }\n            return {\n                status: status.value,\n                value: merged.data\n            };\n        };\n        if (ctx.common.async) {\n            return Promise.all([\n                this._def.left._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                }),\n                this._def.right._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                })\n            ]).then(([left, right])=>handleParsed(left, right));\n        } else {\n            return handleParsed(this._def.left._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx\n            }), this._def.right._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx\n            }));\n        }\n    }\n}\nZodIntersection.create = (left, right, params)=>{\n    return new ZodIntersection({\n        left: left,\n        right: right,\n        typeName: ZodFirstPartyTypeKind.ZodIntersection,\n        ...processCreateParams(params)\n    });\n};\nclass ZodTuple extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        if (ctx.data.length < this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_small,\n                minimum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\"\n            });\n            return INVALID;\n        }\n        const rest = this._def.rest;\n        if (!rest && ctx.data.length > this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_big,\n                maximum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\"\n            });\n            status.dirty();\n        }\n        const items = [\n            ...ctx.data\n        ].map((item, itemIndex)=>{\n            const schema = this._def.items[itemIndex] || this._def.rest;\n            if (!schema) return null;\n            return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));\n        }).filter((x)=>!!x); // filter nulls\n        if (ctx.common.async) {\n            return Promise.all(items).then((results)=>{\n                return ParseStatus.mergeArray(status, results);\n            });\n        } else {\n            return ParseStatus.mergeArray(status, items);\n        }\n    }\n    get items() {\n        return this._def.items;\n    }\n    rest(rest) {\n        return new ZodTuple({\n            ...this._def,\n            rest\n        });\n    }\n}\nZodTuple.create = (schemas, params)=>{\n    if (!Array.isArray(schemas)) {\n        throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");\n    }\n    return new ZodTuple({\n        items: schemas,\n        typeName: ZodFirstPartyTypeKind.ZodTuple,\n        rest: null,\n        ...processCreateParams(params)\n    });\n};\nclass ZodRecord extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const pairs = [];\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        for(const key in ctx.data){\n            pairs.push({\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),\n                value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key))\n            });\n        }\n        if (ctx.common.async) {\n            return ParseStatus.mergeObjectAsync(status, pairs);\n        } else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get element() {\n        return this._def.valueType;\n    }\n    static create(first, second, third) {\n        if (second instanceof ZodType) {\n            return new ZodRecord({\n                keyType: first,\n                valueType: second,\n                typeName: ZodFirstPartyTypeKind.ZodRecord,\n                ...processCreateParams(third)\n            });\n        }\n        return new ZodRecord({\n            keyType: ZodString.create(),\n            valueType: first,\n            typeName: ZodFirstPartyTypeKind.ZodRecord,\n            ...processCreateParams(second)\n        });\n    }\n}\nclass ZodMap extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.map) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.map,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        const pairs = [\n            ...ctx.data.entries()\n        ].map(([key, value], index)=>{\n            return {\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [\n                    index,\n                    \"key\"\n                ])),\n                value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [\n                    index,\n                    \"value\"\n                ]))\n            };\n        });\n        if (ctx.common.async) {\n            const finalMap = new Map();\n            return Promise.resolve().then(async ()=>{\n                for (const pair of pairs){\n                    const key = await pair.key;\n                    const value = await pair.value;\n                    if (key.status === \"aborted\" || value.status === \"aborted\") {\n                        return INVALID;\n                    }\n                    if (key.status === \"dirty\" || value.status === \"dirty\") {\n                        status.dirty();\n                    }\n                    finalMap.set(key.value, value.value);\n                }\n                return {\n                    status: status.value,\n                    value: finalMap\n                };\n            });\n        } else {\n            const finalMap = new Map();\n            for (const pair of pairs){\n                const key = pair.key;\n                const value = pair.value;\n                if (key.status === \"aborted\" || value.status === \"aborted\") {\n                    return INVALID;\n                }\n                if (key.status === \"dirty\" || value.status === \"dirty\") {\n                    status.dirty();\n                }\n                finalMap.set(key.value, value.value);\n            }\n            return {\n                status: status.value,\n                value: finalMap\n            };\n        }\n    }\n}\nZodMap.create = (keyType, valueType, params)=>{\n    return new ZodMap({\n        valueType,\n        keyType,\n        typeName: ZodFirstPartyTypeKind.ZodMap,\n        ...processCreateParams(params)\n    });\n};\nclass ZodSet extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.set) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.set,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const def = this._def;\n        if (def.minSize !== null) {\n            if (ctx.data.size < def.minSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minSize.message\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxSize !== null) {\n            if (ctx.data.size > def.maxSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxSize.message\n                });\n                status.dirty();\n            }\n        }\n        const valueType = this._def.valueType;\n        function finalizeSet(elements) {\n            const parsedSet = new Set();\n            for (const element of elements){\n                if (element.status === \"aborted\") return INVALID;\n                if (element.status === \"dirty\") status.dirty();\n                parsedSet.add(element.value);\n            }\n            return {\n                status: status.value,\n                value: parsedSet\n            };\n        }\n        const elements = [\n            ...ctx.data.values()\n        ].map((item, i)=>valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));\n        if (ctx.common.async) {\n            return Promise.all(elements).then((elements)=>finalizeSet(elements));\n        } else {\n            return finalizeSet(elements);\n        }\n    }\n    min(minSize, message) {\n        return new ZodSet({\n            ...this._def,\n            minSize: {\n                value: minSize,\n                message: errorUtil.toString(message)\n            }\n        });\n    }\n    max(maxSize, message) {\n        return new ZodSet({\n            ...this._def,\n            maxSize: {\n                value: maxSize,\n                message: errorUtil.toString(message)\n            }\n        });\n    }\n    size(size, message) {\n        return this.min(size, message).max(size, message);\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodSet.create = (valueType, params)=>{\n    return new ZodSet({\n        valueType,\n        minSize: null,\n        maxSize: null,\n        typeName: ZodFirstPartyTypeKind.ZodSet,\n        ...processCreateParams(params)\n    });\n};\nclass ZodFunction extends ZodType {\n    constructor(){\n        super(...arguments);\n        this.validate = this.implement;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.function) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.function,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        function makeArgsIssue(args, error) {\n            return makeIssue({\n                data: args,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap\n                ].filter((x)=>!!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_arguments,\n                    argumentsError: error\n                }\n            });\n        }\n        function makeReturnsIssue(returns, error) {\n            return makeIssue({\n                data: returns,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap\n                ].filter((x)=>!!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_return_type,\n                    returnTypeError: error\n                }\n            });\n        }\n        const params = {\n            errorMap: ctx.common.contextualErrorMap\n        };\n        const fn = ctx.data;\n        if (this._def.returns instanceof ZodPromise) {\n            // Would love a way to avoid disabling this rule, but we need\n            // an alias (using an arrow function was what caused 2651).\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            const me = this;\n            return OK(async function(...args) {\n                const error = new ZodError([]);\n                const parsedArgs = await me._def.args.parseAsync(args, params).catch((e)=>{\n                    error.addIssue(makeArgsIssue(args, e));\n                    throw error;\n                });\n                const result = await Reflect.apply(fn, this, parsedArgs);\n                const parsedReturns = await me._def.returns._def.type.parseAsync(result, params).catch((e)=>{\n                    error.addIssue(makeReturnsIssue(result, e));\n                    throw error;\n                });\n                return parsedReturns;\n            });\n        } else {\n            // Would love a way to avoid disabling this rule, but we need\n            // an alias (using an arrow function was what caused 2651).\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            const me = this;\n            return OK(function(...args) {\n                const parsedArgs = me._def.args.safeParse(args, params);\n                if (!parsedArgs.success) {\n                    throw new ZodError([\n                        makeArgsIssue(args, parsedArgs.error)\n                    ]);\n                }\n                const result = Reflect.apply(fn, this, parsedArgs.data);\n                const parsedReturns = me._def.returns.safeParse(result, params);\n                if (!parsedReturns.success) {\n                    throw new ZodError([\n                        makeReturnsIssue(result, parsedReturns.error)\n                    ]);\n                }\n                return parsedReturns.data;\n            });\n        }\n    }\n    parameters() {\n        return this._def.args;\n    }\n    returnType() {\n        return this._def.returns;\n    }\n    args(...items) {\n        return new ZodFunction({\n            ...this._def,\n            args: ZodTuple.create(items).rest(ZodUnknown.create())\n        });\n    }\n    returns(returnType) {\n        return new ZodFunction({\n            ...this._def,\n            returns: returnType\n        });\n    }\n    implement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    strictImplement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    static create(args, returns, params) {\n        return new ZodFunction({\n            args: args ? args : ZodTuple.create([]).rest(ZodUnknown.create()),\n            returns: returns || ZodUnknown.create(),\n            typeName: ZodFirstPartyTypeKind.ZodFunction,\n            ...processCreateParams(params)\n        });\n    }\n}\nclass ZodLazy extends ZodType {\n    get schema() {\n        return this._def.getter();\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const lazySchema = this._def.getter();\n        return lazySchema._parse({\n            data: ctx.data,\n            path: ctx.path,\n            parent: ctx\n        });\n    }\n}\nZodLazy.create = (getter, params)=>{\n    return new ZodLazy({\n        getter: getter,\n        typeName: ZodFirstPartyTypeKind.ZodLazy,\n        ...processCreateParams(params)\n    });\n};\nclass ZodLiteral extends ZodType {\n    _parse(input) {\n        if (input.data !== this._def.value) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_literal,\n                expected: this._def.value\n            });\n            return INVALID;\n        }\n        return {\n            status: \"valid\",\n            value: input.data\n        };\n    }\n    get value() {\n        return this._def.value;\n    }\n}\nZodLiteral.create = (value, params)=>{\n    return new ZodLiteral({\n        value: value,\n        typeName: ZodFirstPartyTypeKind.ZodLiteral,\n        ...processCreateParams(params)\n    });\n};\nfunction createZodEnum(values, params) {\n    return new ZodEnum({\n        values,\n        typeName: ZodFirstPartyTypeKind.ZodEnum,\n        ...processCreateParams(params)\n    });\n}\nclass ZodEnum extends ZodType {\n    _parse(input) {\n        if (typeof input.data !== \"string\") {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type\n            });\n            return INVALID;\n        }\n        if (this._def.values.indexOf(input.data) === -1) {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get options() {\n        return this._def.values;\n    }\n    get enum() {\n        const enumValues = {};\n        for (const val of this._def.values){\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Values() {\n        const enumValues = {};\n        for (const val of this._def.values){\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Enum() {\n        const enumValues = {};\n        for (const val of this._def.values){\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    extract(values) {\n        return ZodEnum.create(values);\n    }\n    exclude(values) {\n        return ZodEnum.create(this.options.filter((opt)=>!values.includes(opt)));\n    }\n}\nZodEnum.create = createZodEnum;\nclass ZodNativeEnum extends ZodType {\n    _parse(input) {\n        const nativeEnumValues = util.getValidEnumValues(this._def.values);\n        const ctx = this._getOrReturnCtx(input);\n        if (ctx.parsedType !== ZodParsedType.string && ctx.parsedType !== ZodParsedType.number) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type\n            });\n            return INVALID;\n        }\n        if (nativeEnumValues.indexOf(input.data) === -1) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get enum() {\n        return this._def.values;\n    }\n}\nZodNativeEnum.create = (values, params)=>{\n    return new ZodNativeEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodNativeEnum,\n        ...processCreateParams(params)\n    });\n};\nclass ZodPromise extends ZodType {\n    unwrap() {\n        return this._def.type;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.promise && ctx.common.async === false) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.promise,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        const promisified = ctx.parsedType === ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);\n        return OK(promisified.then((data)=>{\n            return this._def.type.parseAsync(data, {\n                path: ctx.path,\n                errorMap: ctx.common.contextualErrorMap\n            });\n        }));\n    }\n}\nZodPromise.create = (schema, params)=>{\n    return new ZodPromise({\n        type: schema,\n        typeName: ZodFirstPartyTypeKind.ZodPromise,\n        ...processCreateParams(params)\n    });\n};\nclass ZodEffects extends ZodType {\n    innerType() {\n        return this._def.schema;\n    }\n    sourceType() {\n        return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects ? this._def.schema.sourceType() : this._def.schema;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const effect = this._def.effect || null;\n        const checkCtx = {\n            addIssue: (arg)=>{\n                addIssueToContext(ctx, arg);\n                if (arg.fatal) {\n                    status.abort();\n                } else {\n                    status.dirty();\n                }\n            },\n            get path () {\n                return ctx.path;\n            }\n        };\n        checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);\n        if (effect.type === \"preprocess\") {\n            const processed = effect.transform(ctx.data, checkCtx);\n            if (ctx.common.issues.length) {\n                return {\n                    status: \"dirty\",\n                    value: ctx.data\n                };\n            }\n            if (ctx.common.async) {\n                return Promise.resolve(processed).then((processed)=>{\n                    return this._def.schema._parseAsync({\n                        data: processed,\n                        path: ctx.path,\n                        parent: ctx\n                    });\n                });\n            } else {\n                return this._def.schema._parseSync({\n                    data: processed,\n                    path: ctx.path,\n                    parent: ctx\n                });\n            }\n        }\n        if (effect.type === \"refinement\") {\n            const executeRefinement = (acc)=>{\n                const result = effect.refinement(acc, checkCtx);\n                if (ctx.common.async) {\n                    return Promise.resolve(result);\n                }\n                if (result instanceof Promise) {\n                    throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");\n                }\n                return acc;\n            };\n            if (ctx.common.async === false) {\n                const inner = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                });\n                if (inner.status === \"aborted\") return INVALID;\n                if (inner.status === \"dirty\") status.dirty();\n                // return value is ignored\n                executeRefinement(inner.value);\n                return {\n                    status: status.value,\n                    value: inner.value\n                };\n            } else {\n                return this._def.schema._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                }).then((inner)=>{\n                    if (inner.status === \"aborted\") return INVALID;\n                    if (inner.status === \"dirty\") status.dirty();\n                    return executeRefinement(inner.value).then(()=>{\n                        return {\n                            status: status.value,\n                            value: inner.value\n                        };\n                    });\n                });\n            }\n        }\n        if (effect.type === \"transform\") {\n            if (ctx.common.async === false) {\n                const base = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                });\n                if (!isValid(base)) return base;\n                const result = effect.transform(base.value, checkCtx);\n                if (result instanceof Promise) {\n                    throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);\n                }\n                return {\n                    status: status.value,\n                    value: result\n                };\n            } else {\n                return this._def.schema._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                }).then((base)=>{\n                    if (!isValid(base)) return base;\n                    return Promise.resolve(effect.transform(base.value, checkCtx)).then((result)=>({\n                            status: status.value,\n                            value: result\n                        }));\n                });\n            }\n        }\n        util.assertNever(effect);\n    }\n}\nZodEffects.create = (schema, effect, params)=>{\n    return new ZodEffects({\n        schema,\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        effect,\n        ...processCreateParams(params)\n    });\n};\nZodEffects.createWithPreprocess = (preprocess, schema, params)=>{\n    return new ZodEffects({\n        schema,\n        effect: {\n            type: \"preprocess\",\n            transform: preprocess\n        },\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        ...processCreateParams(params)\n    });\n};\nclass ZodOptional extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.undefined) {\n            return OK(undefined);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodOptional.create = (type, params)=>{\n    return new ZodOptional({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodOptional,\n        ...processCreateParams(params)\n    });\n};\nclass ZodNullable extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.null) {\n            return OK(null);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodNullable.create = (type, params)=>{\n    return new ZodNullable({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodNullable,\n        ...processCreateParams(params)\n    });\n};\nclass ZodDefault extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        let data = ctx.data;\n        if (ctx.parsedType === ZodParsedType.undefined) {\n            data = this._def.defaultValue();\n        }\n        return this._def.innerType._parse({\n            data,\n            path: ctx.path,\n            parent: ctx\n        });\n    }\n    removeDefault() {\n        return this._def.innerType;\n    }\n}\nZodDefault.create = (type, params)=>{\n    return new ZodDefault({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodDefault,\n        defaultValue: typeof params.default === \"function\" ? params.default : ()=>params.default,\n        ...processCreateParams(params)\n    });\n};\nclass ZodCatch extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        // newCtx is used to not collect issues from inner types in ctx\n        const newCtx = {\n            ...ctx,\n            common: {\n                ...ctx.common,\n                issues: []\n            }\n        };\n        const result = this._def.innerType._parse({\n            data: newCtx.data,\n            path: newCtx.path,\n            parent: {\n                ...newCtx\n            }\n        });\n        if (isAsync(result)) {\n            return result.then((result)=>{\n                return {\n                    status: \"valid\",\n                    value: result.status === \"valid\" ? result.value : this._def.catchValue({\n                        get error () {\n                            return new ZodError(newCtx.common.issues);\n                        },\n                        input: newCtx.data\n                    })\n                };\n            });\n        } else {\n            return {\n                status: \"valid\",\n                value: result.status === \"valid\" ? result.value : this._def.catchValue({\n                    get error () {\n                        return new ZodError(newCtx.common.issues);\n                    },\n                    input: newCtx.data\n                })\n            };\n        }\n    }\n    removeCatch() {\n        return this._def.innerType;\n    }\n}\nZodCatch.create = (type, params)=>{\n    return new ZodCatch({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodCatch,\n        catchValue: typeof params.catch === \"function\" ? params.catch : ()=>params.catch,\n        ...processCreateParams(params)\n    });\n};\nclass ZodNaN extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.nan) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.nan,\n                received: ctx.parsedType\n            });\n            return INVALID;\n        }\n        return {\n            status: \"valid\",\n            value: input.data\n        };\n    }\n}\nZodNaN.create = (params)=>{\n    return new ZodNaN({\n        typeName: ZodFirstPartyTypeKind.ZodNaN,\n        ...processCreateParams(params)\n    });\n};\nconst BRAND = Symbol(\"zod_brand\");\nclass ZodBranded extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const data = ctx.data;\n        return this._def.type._parse({\n            data,\n            path: ctx.path,\n            parent: ctx\n        });\n    }\n    unwrap() {\n        return this._def.type;\n    }\n}\nclass ZodPipeline extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.common.async) {\n            const handleAsync = async ()=>{\n                const inResult = await this._def.in._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx\n                });\n                if (inResult.status === \"aborted\") return INVALID;\n                if (inResult.status === \"dirty\") {\n                    status.dirty();\n                    return DIRTY(inResult.value);\n                } else {\n                    return this._def.out._parseAsync({\n                        data: inResult.value,\n                        path: ctx.path,\n                        parent: ctx\n                    });\n                }\n            };\n            return handleAsync();\n        } else {\n            const inResult = this._def.in._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx\n            });\n            if (inResult.status === \"aborted\") return INVALID;\n            if (inResult.status === \"dirty\") {\n                status.dirty();\n                return {\n                    status: \"dirty\",\n                    value: inResult.value\n                };\n            } else {\n                return this._def.out._parseSync({\n                    data: inResult.value,\n                    path: ctx.path,\n                    parent: ctx\n                });\n            }\n        }\n    }\n    static create(a, b) {\n        return new ZodPipeline({\n            in: a,\n            out: b,\n            typeName: ZodFirstPartyTypeKind.ZodPipeline\n        });\n    }\n}\nclass ZodReadonly extends ZodType {\n    _parse(input) {\n        const result = this._def.innerType._parse(input);\n        if (isValid(result)) {\n            result.value = Object.freeze(result.value);\n        }\n        return result;\n    }\n}\nZodReadonly.create = (type, params)=>{\n    return new ZodReadonly({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodReadonly,\n        ...processCreateParams(params)\n    });\n};\nconst custom = (check, params = {}, /**\n * @deprecated\n *\n * Pass `fatal` into the params object instead:\n *\n * ```ts\n * z.string().custom((val) => val.length > 5, { fatal: false })\n * ```\n *\n */ fatal)=>{\n    if (check) return ZodAny.create().superRefine((data, ctx)=>{\n        var _a, _b;\n        if (!check(data)) {\n            const p = typeof params === \"function\" ? params(data) : typeof params === \"string\" ? {\n                message: params\n            } : params;\n            const _fatal = (_b = (_a = p.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;\n            const p2 = typeof p === \"string\" ? {\n                message: p\n            } : p;\n            ctx.addIssue({\n                code: \"custom\",\n                ...p2,\n                fatal: _fatal\n            });\n        }\n    });\n    return ZodAny.create();\n};\nconst late = {\n    object: ZodObject.lazycreate\n};\nvar ZodFirstPartyTypeKind;\n(function(ZodFirstPartyTypeKind) {\n    ZodFirstPartyTypeKind[\"ZodString\"] = \"ZodString\";\n    ZodFirstPartyTypeKind[\"ZodNumber\"] = \"ZodNumber\";\n    ZodFirstPartyTypeKind[\"ZodNaN\"] = \"ZodNaN\";\n    ZodFirstPartyTypeKind[\"ZodBigInt\"] = \"ZodBigInt\";\n    ZodFirstPartyTypeKind[\"ZodBoolean\"] = \"ZodBoolean\";\n    ZodFirstPartyTypeKind[\"ZodDate\"] = \"ZodDate\";\n    ZodFirstPartyTypeKind[\"ZodSymbol\"] = \"ZodSymbol\";\n    ZodFirstPartyTypeKind[\"ZodUndefined\"] = \"ZodUndefined\";\n    ZodFirstPartyTypeKind[\"ZodNull\"] = \"ZodNull\";\n    ZodFirstPartyTypeKind[\"ZodAny\"] = \"ZodAny\";\n    ZodFirstPartyTypeKind[\"ZodUnknown\"] = \"ZodUnknown\";\n    ZodFirstPartyTypeKind[\"ZodNever\"] = \"ZodNever\";\n    ZodFirstPartyTypeKind[\"ZodVoid\"] = \"ZodVoid\";\n    ZodFirstPartyTypeKind[\"ZodArray\"] = \"ZodArray\";\n    ZodFirstPartyTypeKind[\"ZodObject\"] = \"ZodObject\";\n    ZodFirstPartyTypeKind[\"ZodUnion\"] = \"ZodUnion\";\n    ZodFirstPartyTypeKind[\"ZodDiscriminatedUnion\"] = \"ZodDiscriminatedUnion\";\n    ZodFirstPartyTypeKind[\"ZodIntersection\"] = \"ZodIntersection\";\n    ZodFirstPartyTypeKind[\"ZodTuple\"] = \"ZodTuple\";\n    ZodFirstPartyTypeKind[\"ZodRecord\"] = \"ZodRecord\";\n    ZodFirstPartyTypeKind[\"ZodMap\"] = \"ZodMap\";\n    ZodFirstPartyTypeKind[\"ZodSet\"] = \"ZodSet\";\n    ZodFirstPartyTypeKind[\"ZodFunction\"] = \"ZodFunction\";\n    ZodFirstPartyTypeKind[\"ZodLazy\"] = \"ZodLazy\";\n    ZodFirstPartyTypeKind[\"ZodLiteral\"] = \"ZodLiteral\";\n    ZodFirstPartyTypeKind[\"ZodEnum\"] = \"ZodEnum\";\n    ZodFirstPartyTypeKind[\"ZodEffects\"] = \"ZodEffects\";\n    ZodFirstPartyTypeKind[\"ZodNativeEnum\"] = \"ZodNativeEnum\";\n    ZodFirstPartyTypeKind[\"ZodOptional\"] = \"ZodOptional\";\n    ZodFirstPartyTypeKind[\"ZodNullable\"] = \"ZodNullable\";\n    ZodFirstPartyTypeKind[\"ZodDefault\"] = \"ZodDefault\";\n    ZodFirstPartyTypeKind[\"ZodCatch\"] = \"ZodCatch\";\n    ZodFirstPartyTypeKind[\"ZodPromise\"] = \"ZodPromise\";\n    ZodFirstPartyTypeKind[\"ZodBranded\"] = \"ZodBranded\";\n    ZodFirstPartyTypeKind[\"ZodPipeline\"] = \"ZodPipeline\";\n    ZodFirstPartyTypeKind[\"ZodReadonly\"] = \"ZodReadonly\";\n})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));\nconst instanceOfType = (// const instanceOfType = <T extends new (...args: any[]) => any>(\ncls, params = {\n    message: `Input not instance of ${cls.name}`\n})=>custom((data)=>data instanceof cls, params);\nconst stringType = ZodString.create;\nconst numberType = ZodNumber.create;\nconst nanType = ZodNaN.create;\nconst bigIntType = ZodBigInt.create;\nconst booleanType = ZodBoolean.create;\nconst dateType = ZodDate.create;\nconst symbolType = ZodSymbol.create;\nconst undefinedType = ZodUndefined.create;\nconst nullType = ZodNull.create;\nconst anyType = ZodAny.create;\nconst unknownType = ZodUnknown.create;\nconst neverType = ZodNever.create;\nconst voidType = ZodVoid.create;\nconst arrayType = ZodArray.create;\nconst objectType = ZodObject.create;\nconst strictObjectType = ZodObject.strictCreate;\nconst unionType = ZodUnion.create;\nconst discriminatedUnionType = ZodDiscriminatedUnion.create;\nconst intersectionType = ZodIntersection.create;\nconst tupleType = ZodTuple.create;\nconst recordType = ZodRecord.create;\nconst mapType = ZodMap.create;\nconst setType = ZodSet.create;\nconst functionType = ZodFunction.create;\nconst lazyType = ZodLazy.create;\nconst literalType = ZodLiteral.create;\nconst enumType = ZodEnum.create;\nconst nativeEnumType = ZodNativeEnum.create;\nconst promiseType = ZodPromise.create;\nconst effectsType = ZodEffects.create;\nconst optionalType = ZodOptional.create;\nconst nullableType = ZodNullable.create;\nconst preprocessType = ZodEffects.createWithPreprocess;\nconst pipelineType = ZodPipeline.create;\nconst ostring = ()=>stringType().optional();\nconst onumber = ()=>numberType().optional();\nconst oboolean = ()=>booleanType().optional();\nconst coerce = {\n    string: (arg)=>ZodString.create({\n            ...arg,\n            coerce: true\n        }),\n    number: (arg)=>ZodNumber.create({\n            ...arg,\n            coerce: true\n        }),\n    boolean: (arg)=>ZodBoolean.create({\n            ...arg,\n            coerce: true\n        }),\n    bigint: (arg)=>ZodBigInt.create({\n            ...arg,\n            coerce: true\n        }),\n    date: (arg)=>ZodDate.create({\n            ...arg,\n            coerce: true\n        })\n};\nconst NEVER = INVALID;\nvar z = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    defaultErrorMap: errorMap,\n    setErrorMap: setErrorMap,\n    getErrorMap: getErrorMap,\n    makeIssue: makeIssue,\n    EMPTY_PATH: EMPTY_PATH,\n    addIssueToContext: addIssueToContext,\n    ParseStatus: ParseStatus,\n    INVALID: INVALID,\n    DIRTY: DIRTY,\n    OK: OK,\n    isAborted: isAborted,\n    isDirty: isDirty,\n    isValid: isValid,\n    isAsync: isAsync,\n    get util () {\n        return util;\n    },\n    get objectUtil () {\n        return objectUtil;\n    },\n    ZodParsedType: ZodParsedType,\n    getParsedType: getParsedType,\n    ZodType: ZodType,\n    ZodString: ZodString,\n    ZodNumber: ZodNumber,\n    ZodBigInt: ZodBigInt,\n    ZodBoolean: ZodBoolean,\n    ZodDate: ZodDate,\n    ZodSymbol: ZodSymbol,\n    ZodUndefined: ZodUndefined,\n    ZodNull: ZodNull,\n    ZodAny: ZodAny,\n    ZodUnknown: ZodUnknown,\n    ZodNever: ZodNever,\n    ZodVoid: ZodVoid,\n    ZodArray: ZodArray,\n    ZodObject: ZodObject,\n    ZodUnion: ZodUnion,\n    ZodDiscriminatedUnion: ZodDiscriminatedUnion,\n    ZodIntersection: ZodIntersection,\n    ZodTuple: ZodTuple,\n    ZodRecord: ZodRecord,\n    ZodMap: ZodMap,\n    ZodSet: ZodSet,\n    ZodFunction: ZodFunction,\n    ZodLazy: ZodLazy,\n    ZodLiteral: ZodLiteral,\n    ZodEnum: ZodEnum,\n    ZodNativeEnum: ZodNativeEnum,\n    ZodPromise: ZodPromise,\n    ZodEffects: ZodEffects,\n    ZodTransformer: ZodEffects,\n    ZodOptional: ZodOptional,\n    ZodNullable: ZodNullable,\n    ZodDefault: ZodDefault,\n    ZodCatch: ZodCatch,\n    ZodNaN: ZodNaN,\n    BRAND: BRAND,\n    ZodBranded: ZodBranded,\n    ZodPipeline: ZodPipeline,\n    ZodReadonly: ZodReadonly,\n    custom: custom,\n    Schema: ZodType,\n    ZodSchema: ZodType,\n    late: late,\n    get ZodFirstPartyTypeKind () {\n        return ZodFirstPartyTypeKind;\n    },\n    coerce: coerce,\n    any: anyType,\n    array: arrayType,\n    bigint: bigIntType,\n    boolean: booleanType,\n    date: dateType,\n    discriminatedUnion: discriminatedUnionType,\n    effect: effectsType,\n    \"enum\": enumType,\n    \"function\": functionType,\n    \"instanceof\": instanceOfType,\n    intersection: intersectionType,\n    lazy: lazyType,\n    literal: literalType,\n    map: mapType,\n    nan: nanType,\n    nativeEnum: nativeEnumType,\n    never: neverType,\n    \"null\": nullType,\n    nullable: nullableType,\n    number: numberType,\n    object: objectType,\n    oboolean: oboolean,\n    onumber: onumber,\n    optional: optionalType,\n    ostring: ostring,\n    pipeline: pipelineType,\n    preprocess: preprocessType,\n    promise: promiseType,\n    record: recordType,\n    set: setType,\n    strictObject: strictObjectType,\n    string: stringType,\n    symbol: symbolType,\n    transformer: effectsType,\n    tuple: tupleType,\n    \"undefined\": undefinedType,\n    union: unionType,\n    unknown: unknownType,\n    \"void\": voidType,\n    NEVER: NEVER,\n    ZodIssueCode: ZodIssueCode,\n    quotelessJson: quotelessJson,\n    ZodError: ZodError\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zod/lib/index.mjs\n");

/***/ })

};
;