trigger:
  batch: true
  branches:
    include:
      - master
  paths:
    include:
      - 'Met.ServiceApps/src/OnePoint/ClientApp/*'

resources:
  repositories:
    - repository: templates
      type: git
      name: ServiceApps/PipelineTemplates
      ref: refs/tags/1.5.7
pool:
  vmImage: 'ubuntu-latest'

stages:
  - template: frontend_container_deploy_qa.yml@templates
    parameters:
      containerName: onepoint-ui
      projectPath: Met.ServiceApps/src/OnePoint/ClientApp
      tag: '$(Build.BuildId)'
      scanDirectory: /Met.ServiceApps/src/OnePoint/ClientApp
