import {
  BREAKPOINTS,
  ChevronDownIcon,
  CloseIcon,
  SearchIcon
} from '@/design/atoms';
import {
  Button,
  Select,
  SelectDefaultOption,
  SelectItem,
  Tooltip
} from '@/design/molecules';
import { statesData } from '@/features/customer';
import { AccountSearchSchemaType } from '@/features/search';
import {
  Box,
  IconButton,
  InputAdornment,
  TextField,
  Theme,
  useMediaQuery
} from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useCallback } from 'react';
import {
  Control,
  UseFormRegister,
  UseFormResetField,
  useWatch
} from 'react-hook-form';

interface Props {
  isFetching: boolean;
  allowGuest?: boolean;
  onSearch: () => void;
  handleContinueAsGuestClick: () => void;
  onStateChange: (value: string) => void;
  register: UseFormRegister<AccountSearchSchemaType>;
  resetField: UseFormResetField<AccountSearchSchemaType>;
  control: Control<AccountSearchSchemaType>;
}

const IconComponent = (props: { className: string }) => (
  <ChevronDownIcon
    {...props}
    viewBox="0 0 16 10"
    sx={{
      width: '16px',
      height: '10px'
    }}
  />
);

export const AccountSearch = (props: Props) => {
  const { t } = useTranslation();
  const { control, resetField } = props;
  const accountSearchText = useWatch({
    control,
    name: 'accountSearchText',
    defaultValue: ''
  });

  const isDeskTopScreen = useMediaQuery((theme: Theme) =>
    theme.breakpoints.up(BREAKPOINTS.values.xl)
  );
  const handleSearchIcon = () => {
    props.onSearch();
  };
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      props.onSearch();
    }
  };

  const handleClose = useCallback(() => {
    resetField('accountSearchText');
  }, [resetField]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-end',
        alignSelf: 'stretch'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          width: '100%',
          gap: '16px',
          '& .MuiFormControl-root': {
            marginTop: { xs: '12px', sm: '16px' }
          }
        }}
      >
        <TextField
          variant="outlined"
          placeholder={t(
            'features.customer.search.accountSearchInputPlaceHolder'
          )}
          aria-label={t(
            'features.customer.search.accountSearchInputPlaceHolder'
          )}
          data-testid="accountSearchInput-testId"
          {...props.register('accountSearchText')}
          onKeyDown={handleKeyDown}
          sx={{
            flex: 3,
            backgroundColor: 'common.white',
            boxShadow: '0px 4px 8px 0px rgba(0, 0, 0, 0.10)',
            height: { xs: '64px' },
            color: 'neutral.400',
            fontWeight: 400,
            '& .MuiInputBase-input': {
              fontSize: { xs: '14px', sm: '18px' }
            },
            '& .MuiInputBase-root': {
              px: { xs: '8px', sm: '12px', md: '14px' },
              height: { xs: '66px' }
            },
            '& .MuiIconButton-root': {
              px: { xs: '0px', md: '8px' }
            }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon
                  onClick={handleSearchIcon}
                  data-testid="searchIcon-testId"
                  sx={{
                    width: { xs: '16px', sm: '24px' },
                    height: { xs: '19px', sm: '24px' }
                  }}
                />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <Box
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    display: 'flex'
                  }}
                >
                  {!props.isFetching && accountSearchText.length > 0 ? (
                    <>
                      <Tooltip placement="top" title={t('common.clear')}>
                        <IconButton
                          data-testid="closeIconBtn-testId"
                          onClick={handleClose}
                        >
                          <CloseIcon
                            style={{
                              width: '24px',
                              height: '24px'
                            }}
                            viewBox="0 0 24 24"
                            data-testid="closeButtonSearch-testId"
                          />
                        </IconButton>
                      </Tooltip>
                    </>
                  ) : null}
                </Box>
              </InputAdornment>
            )
          }}
        />
        <Box position="relative" sx={{ flex: 1 }}>
          <Select
            name="stateOrProvince"
            data-testid="select-state-testId"
            error={false}
            items={[
              {
                id: SelectDefaultOption.All,
                value: t('common.all')
              } as SelectItem
            ].concat(
              statesData
                .flatMap((city) => city.states)
                .map((state) => ({
                  id: state.stateId,
                  value: state.stateName
                }))
            )}
            labelText={t(`features.customer.profileForm.state`)}
            control={control}
            onSelectChange={props.onStateChange}
            iconComponent={IconComponent}
            sx={{
              width: '100%',
              backgroundColor: 'common.white',
              boxShadow: '0px 4px 8px 0px rgba(0, 0, 0, 0.10)',
              height: { xs: '64px' },
              borderRadius: '4px',
              border: '1px solid',
              borderColor: 'neutral.300',
              marginBottom: 'unset',
              '.MuiSvgIcon-root': {
                right: '16px',
                top: 'unset'
              },
              '.MuiSelect-select:focus': {
                backgroundColor: 'unset'
              },
              '.MuiInputBase-root': {
                height: '100%',
                color: 'neutral.800',
                bgcolor: 'common.white',
                ':before': {
                  borderBottomColor: 'neutral.300'
                },
                ':hover:not(.Mui-disabled, .Mui-error)': {
                  bgcolor: 'neutral.100'
                },
                ':hover:not(.Mui-disabled, .Mui-error):before': {
                  borderBottomColor: 'neutral.300'
                }
              },
              '.MuiInputBase-root:focus': {
                backgroundColor: 'unset'
              },
              '.MuiFormLabel-root': {
                top: '10%',
                color: '#9393a3',
                '.MuiTypography-root': {
                  fontSize: { xs: '14px', sm: '18px' }
                }
              }
            }}
          />
        </Box>
      </Box>

      {props.allowGuest && (
        <Button
          data-testid="accountSearch-btnContinueAsGuest-testid"
          size="medium"
          variant="secondary"
          onClick={props.handleContinueAsGuestClick}
          sx={{ my: '16px' }}
          fullWidth={!isDeskTopScreen}
        >
          {t('features.customer.search.continueAsGuest')}
        </Button>
      )}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          '& .MuiFormControl-root': {
            marginTop: { xs: '12px', sm: '16px' }
          }
        }}
      ></Box>
    </Box>
  );
};
