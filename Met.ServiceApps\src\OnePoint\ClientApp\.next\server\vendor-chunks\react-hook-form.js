"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || data instanceof FileList)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (obj, path, defaultValue)=>{\n    if (!path || !isObject(obj)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], obj);\n    return isUndefined(result) || result === obj ? isUndefined(obj[path]) ? defaultValue : obj[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    _props.current = props;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n            next: _props.current.next\n        });\n        return ()=>{\n            subscription && subscription.unsubscribe();\n        };\n    }, [\n        props.disabled\n    ]);\n}\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _mounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        next: (value1)=>_mounted.current && shouldSubscribeByName(_name.current, value1.name, exact) && shouldRenderFormState(value1, _localProxyFormState.current, control._updateFormState) && updateFormState({\n                ...control._formState,\n                ...value1\n            }),\n        subject: control._subjects.state\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _mounted.current = true;\n        _localProxyFormState.current.isValid && control._updateValid(true);\n        return ()=>{\n            _mounted.current = false;\n        };\n    }, [\n        control\n    ]);\n    return getProxyFormState(formState, control, _localProxyFormState.current, false);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        subject: control._subjects.values,\n        next: (formState)=>{\n            if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n                updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n            }\n        }\n    });\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, defaultValue));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nfunction set(object, path, value1) {\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n    return object;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name\n    });\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1\n    }));\n    _registerProps.current = control.register(name, props.rules);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (get(control._fields, name)) {\n            control._updateDisabledField({\n                disabled,\n                fields: control._fields,\n                name,\n                value: get(control._fields, name)._f.value\n            });\n        }\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return {\n        field: {\n            name,\n            value: value1,\n            ...isBoolean(disabled) || isBoolean(formState.disabled) ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n                    target: {\n                        value: getEventValue(event),\n                        name: name\n                    },\n                    type: EVENTS.CHANGE\n                }), [\n                name\n            ]),\n            onBlur: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n                    target: {\n                        value: get(control._formValues, name),\n                        name: name\n                    },\n                    type: EVENTS.BLUR\n                }), [\n                name,\n                control\n            ]),\n            ref: (elm)=>{\n                const field = get(control._fields, name);\n                if (field && elm) {\n                    field._f.ref = {\n                        focus: ()=>elm.focus(),\n                        select: ()=>elm.select(),\n                        setCustomValidity: (message)=>elm.setCustomValidity(message),\n                        reportValidity: ()=>elm.reportValidity()\n                    };\n                }\n            }\n        },\n        formState,\n        fieldState: Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        })\n    };\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            for (const name of control._names.mount){\n                formData.append(name, get(data, name));\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(action, {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    break;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    break;\n                }\n            } else if (isObject(currentField)) {\n                iterateFieldsByAction(currentField, action);\n            }\n        }\n    }\n};\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = compact(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = (value1)=>isString(value1);\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRegex = (value1)=>value1 instanceof RegExp;\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, disabled } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabled) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nfunction append(data, value1) {\n    return [\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\n}\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nfunction prepend(data, value1) {\n    return [\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\n}\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    data[indexA] = [\n        data[indexB],\n        data[indexB] = data[indexA]\n    ][0];\n};\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    props.rules && control.register(name, props.rules);\n    useSubscribe({\n        next: ({ values, name: fieldArrayName })=>{\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n        subject: control._subjects.array\n    });\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._updateFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append$1 = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = append(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = append(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, append, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend$1 = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prepend(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prepend(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, prepend, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n            if (control._options.resolver) {\n                control._executeSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    validateField(field, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.values.next({\n            name,\n            values: {\n                ...control._formValues\n            }\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._updateValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._updateFieldArray(name);\n        return ()=>{\n            (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\nfunction createSubject() {\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n}\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (_f.refs ? _f.refs.every((ref)=>ref.disabled) : ref.disabled) {\n        return;\n    }\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}, flushRootRender) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        errors: {},\n        disabled: false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    const _subjects = {\n        values: createSubject(),\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldCaptureDirtyFields = props.resetOptions && props.resetOptions.keepDirtyValues;\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _updateValid = async (shouldUpdateValid)=>{\n        if (_proxyFormState.isValid || shouldUpdateValid) {\n            const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (value1)=>_proxyFormState.isValidating && _subjects.state.next({\n            isValidating: value1\n        });\n    const _updateFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _updateValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!isBlurEvent || shouldDirty) {\n            if (_proxyFormState.isDirty) {\n                isPreviousDirty = _formState.isDirty;\n                _formState.isDirty = output.isDirty = _getDirty();\n                shouldUpdateField = isPreviousDirty !== output.isDirty;\n            }\n            const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n            isPreviousDirty = get(_formState.dirtyFields, name);\n            isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n            output.dirtyFields = _formState.dirtyFields;\n            shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n        }\n        if (isBlurEvent) {\n            const isPreviousFieldTouched = get(_formState.touchedFields, name);\n            if (!isPreviousFieldTouched) {\n                set(_formState.touchedFields, name, isBlurEvent);\n                output.touchedFields = _formState.touchedFields;\n                shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n            }\n        }\n        shouldUpdateField && shouldRender && _subjects.state.next(output);\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (props.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(props.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n        _updateIsValidating(false);\n    };\n    const _executeSchema = async (name)=>_options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _executeSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const fieldError = await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                fieldValue && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>(name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, props.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.length > 1 ? fieldReference.refs.forEach((checkboxRef)=>(!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find((data)=>data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.values.next({\n                            name,\n                            values: {\n                                ..._formValues\n                            }\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || !isPrimitive(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: {\n                    ..._formValues\n                }\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.values.next({\n            name,\n            values: {\n                ..._formValues\n            }\n        });\n        !_state.mount && flushRootRender();\n    };\n    const onChange = async (event)=>{\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const getCurrentFieldValue = ()=>target.type ? getFieldValue(field._f) : getEventValue(event);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || fieldValue === get(_formValues, name, fieldValue);\n        };\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = getCurrentFieldValue();\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.values.next({\n                name,\n                type: event.type,\n                values: {\n                    ..._formValues\n                }\n            });\n            if (shouldSkipValidation) {\n                _proxyFormState.isValid && _updateValid();\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            _updateIsValidating(true);\n            if (_options.resolver) {\n                const { errors } = await _executeSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                error = (await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        _updateIsValidating(true);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _updateValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors,\n            isValidating: false\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._defaultValues,\n            ..._state.mount ? _formValues : {}\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name),\n            error: get((formState || _formState).errors, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        set(_formState.errors, name, {\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.values.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.values.next({\n            values: {\n                ..._formValues\n            }\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _updateValid();\n    };\n    const _updateDisabledField = ({ disabled, name, field, fields, value: value1 })=>{\n        if (isBoolean(disabled)) {\n            const inputValue = disabled ? undefined : isUndefined(value1) ? getFieldValue(field ? field._f : get(fields, name)._f) : value1;\n            set(_formValues, name, inputValue);\n            updateTouchAndDirty(name, inputValue, false, false, true);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _updateDisabledField({\n                field,\n                disabled: options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref)=>{\n                ref.disabled = disabled;\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _executeSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                await onValid(fieldValues, e);\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors),\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, get(_defaultValues, name));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, options.defaultValue);\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, get(_defaultValues, name)) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _updateValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const values = formValues && !isEmptyObject(formValues) ? cloneUpdatedValues : _defaultValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues || shouldCaptureDirtyFields) {\n                for (const fieldName of _names.mount){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                _fields = {};\n            }\n            _formValues = props.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.values.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        !_state.mount && flushRootRender();\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid;\n        _state.watch = !!props.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: keepStateOptions.keepDirtyValues ? _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && fieldRef.select();\n            }\n        }\n    };\n    const _updateFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    return {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _executeSchema,\n            _getWatch,\n            _getDirty,\n            _updateValid,\n            _removeUnmounted,\n            _updateFieldArray,\n            _updateDisabledField,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _updateFormState,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            set _formState (value){\n                _formState = value;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        errors: {},\n        disabled: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...createFormControl(props, ()=>updateFormState((formState)=>({\n                        ...formState\n                    }))),\n            formState\n        };\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useSubscribe({\n        subject: control._subjects.state,\n        next: (value1)=>{\n            if (shouldRenderFormState(value1, control._proxyFormState, control._updateFormState, true)) {\n                updateFormState({\n                    ...control._formState\n                });\n            }\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        props.values,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._updateValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;