import {
  AccountSearchResultHeader,
  ParentAccount,
  Site,
  SiteItem
} from '@/features/account';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';

const mockParentAccount: ParentAccount = {
  id: '1',
  accountNumber: '12345',
  accountDescription: 'Example Account',
  customerClass: 'A',
  ascFlatRate: 'FlatRate',
  accountEstablishedDate: { seconds: 0, nanos: 0 },
  accountTerminationDate: { seconds: 0, nanos: 0 },
  heldBillExpirationDate: { seconds: 0, nanos: 0 },
  holdBillIndicator: true,
  npaNumber: 'NPA123',
  sourceCode: 'SRC',
  autopayFlag: true,
  ripperParent: 'Parent',
  allowDropShip: true,
  paymentMethod: 'CreditCard',
  costCenter: 'CostCenter1',
  priceTolerance: 'Tolerance1',
  updatedAt: { seconds: 0, nanos: 0 }
};

const mockSites: Site[] = Array.from({ length: 3 }, (_, index) => ({
  id: `site${index + 1}`,
  carrierAccountNumber: `Carrier${index + 1}`,
  deliveryTerms: 'Terms',
  priceSchedule: 'Schedule',
  reasonCode: 'Reason',
  priorSiteNumber: `Prior${index + 1}`,
  priorAccountNumber: `PriorAcc${index + 1}`,
  salesHoldCode: 'Hold',
  gtmScreening: 'Screening',
  paymentTerms: 'Terms',
  freightForwarder: 'Forwarder',
  applyFreight: true,
  siteNumber: `Site${index + 1}`,
  siteName: `SiteName${index + 1}`,
  country: 'Country',
  addressLine1: 'Address1',
  addressLine2: 'Address2',
  city: 'City',
  county: 'County',
  state: 'State',
  province: 'Province',
  postalCode: 'PostalCode',
  shortDescription: 'ShortDesc',
  originalAccountSite: 'OriginalSite',
  accountAddressSet: 'AddressSet',
  effectiveFromDate: { seconds: 0, nanos: 0 },
  effectiveEndDate: { seconds: 0, nanos: 0 },
  eoriNumber: 'EORI',
  mailstop: 'Mailstop',
  locationId: 'LocationID',
  comments: 'Comments',
  description: 'Description',
  primary: true,
  addressLine3: 'Address3',
  addressLine4: 'Address4',
  countryCode: 'CountryCode',
  identifyingAddress: true,
  taxGeoCode: 'TaxGeo',
  taxInsideCityLimits: true,
  language: 'Language',
  updatedAt: { seconds: 0, nanos: 0 },
  accountType: 'Type',
  midParent: 'MidParent',
  salesRegion: 'Region',
  salesDivision: 'Division',
  incoterms: 'Incoterms',
  keyAccountId: 'KeyAccount',
  rebateProgram: 'Rebate',
  websiteInclusionOverrideFlag: 'Override',
  serviceProgram: 'Service',
  serviceManager: 'Manager',
  allowBackorders: true,
  allowPartialLineShipments: true,
  allowPartialOrderShipments: true,
  replacementAllowed: 'Allowed',
  allowSpecialPaymentTerms: true,
  freightGroup: 'FreightGroup',
  warehouse: 'Warehouse',
  shipmentPriority: 'Priority',
  reviewWarrantyClaims: true,
  affiliationLink: 'Link',
  anonymousCustomer: true,
  internalNote: 'InternalNote',
  deliveryNote: 'DeliveryNote',
  externalNote: 'ExternalNote',
  typeOfBusiness: 'BusinessType',
  financialDimensionsDg0Override: 'Override0',
  financialDimensionsDg1Override: 'Override1',
  financialDimensionsDg2Override: 'Override2',
  financialDimensionsDg3Override: 'Override3',
  financialDimensionsDg4Override: 'Override4',
  financialDimensionsDg5Override: 'Override5',
  customerCategoryCode: 'CategoryCode',
  ediLocationCode: 'EDILocation',
  keyAccountFlag: 'KeyFlag',
  statusPermissions: 'Permissions',
  customerSourceReference: 'SourceRef',
  tradingPartnerId: 'PartnerID',
  historicalAddressLine1: 'HistAddress1',
  historicalAddressLine2: 'HistAddress2',
  historicalAddressLine3: 'HistAddress3',
  historicalAddressLine4: 'HistAddress4',
  historicalAddressEffectiveEndDate: { seconds: 0, nanos: 0 },
  priceScheduleDescription: 'PriceDesc',
  siteUses: [],
  siteProfiles: [],
  siteResponsibilities: [],
  accountProfiles: [],
  accountResponsibilities: [],
  contacts: [],
  contactPoints: []
}));

export const mockSiteItems: SiteItem[] = mockSites.map((site) => ({
  ...site,
  parentAccount: mockParentAccount
}));

describe('CustomerSearchResultHeader component tests', () => {
  it('Renders successfully', () => {
    render(<AccountSearchResultHeader sites={mockSiteItems} />);

    const count = screen.getByTestId('loadingTextNumber-testId');
    const label = screen.getByTestId('loadingText-testId');

    expect(count).toHaveTextContent('3');
    expect(label).toHaveTextContent('records found');
  });
});
