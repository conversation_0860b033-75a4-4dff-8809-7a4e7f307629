"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/immer";
exports.ids = ["vendor-chunks/immer"];
exports.modules = {

/***/ "(ssr)/./node_modules/immer/dist/immer.mjs":
/*!*******************************************!*\
  !*** ./node_modules/immer/dist/immer.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Immer: () => (/* binding */ Immer2),\n/* harmony export */   applyPatches: () => (/* binding */ applyPatches),\n/* harmony export */   castDraft: () => (/* binding */ castDraft),\n/* harmony export */   castImmutable: () => (/* binding */ castImmutable),\n/* harmony export */   createDraft: () => (/* binding */ createDraft),\n/* harmony export */   current: () => (/* binding */ current),\n/* harmony export */   enableMapSet: () => (/* binding */ enableMapSet),\n/* harmony export */   enablePatches: () => (/* binding */ enablePatches),\n/* harmony export */   finishDraft: () => (/* binding */ finishDraft),\n/* harmony export */   freeze: () => (/* binding */ freeze),\n/* harmony export */   immerable: () => (/* binding */ DRAFTABLE),\n/* harmony export */   isDraft: () => (/* binding */ isDraft),\n/* harmony export */   isDraftable: () => (/* binding */ isDraftable),\n/* harmony export */   nothing: () => (/* binding */ NOTHING),\n/* harmony export */   original: () => (/* binding */ original),\n/* harmony export */   produce: () => (/* binding */ produce),\n/* harmony export */   produceWithPatches: () => (/* binding */ produceWithPatches),\n/* harmony export */   setAutoFreeze: () => (/* binding */ setAutoFreeze),\n/* harmony export */   setUseStrictShallowCopy: () => (/* binding */ setUseStrictShallowCopy)\n/* harmony export */ });\n// src/utils/env.ts\nvar NOTHING = Symbol.for(\"immer-nothing\");\nvar DRAFTABLE = Symbol.for(\"immer-draftable\");\nvar DRAFT_STATE = Symbol.for(\"immer-state\");\n// src/utils/errors.ts\nvar errors =  true ? [\n    // All error codes, starting by 0:\n    function(plugin) {\n        return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`;\n    },\n    function(thing) {\n        return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;\n    },\n    \"This object has been frozen and should not be mutated\",\n    function(data) {\n        return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + data;\n    },\n    \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n    \"Immer forbids circular references\",\n    \"The first or second argument to `produce` must be a function\",\n    \"The third argument to `produce` must be a function or undefined\",\n    \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n    \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n    function(thing) {\n        return `'current' expects a draft, got: ${thing}`;\n    },\n    \"Object.defineProperty() cannot be used on an Immer draft\",\n    \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n    \"Immer only supports deleting array indices\",\n    \"Immer only supports setting array indices and the 'length' property\",\n    function(thing) {\n        return `'original' expects a draft, got: ${thing}`;\n    }\n] : 0;\nfunction die(error, ...args) {\n    if (true) {\n        const e = errors[error];\n        const msg = typeof e === \"function\" ? e.apply(null, args) : e;\n        throw new Error(`[Immer] ${msg}`);\n    }\n    throw new Error(`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`);\n}\n// src/utils/common.ts\nvar getPrototypeOf = Object.getPrototypeOf;\nfunction isDraft(value) {\n    return !!value && !!value[DRAFT_STATE];\n}\nfunction isDraftable(value) {\n    if (!value) return false;\n    return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);\n}\nvar objectCtorString = Object.prototype.constructor.toString();\nfunction isPlainObject(value) {\n    if (!value || typeof value !== \"object\") return false;\n    const proto = getPrototypeOf(value);\n    if (proto === null) {\n        return true;\n    }\n    const Ctor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n    if (Ctor === Object) return true;\n    return typeof Ctor == \"function\" && Function.toString.call(Ctor) === objectCtorString;\n}\nfunction original(value) {\n    if (!isDraft(value)) die(15, value);\n    return value[DRAFT_STATE].base_;\n}\nfunction each(obj, iter) {\n    if (getArchtype(obj) === 0 /* Object */ ) {\n        Object.entries(obj).forEach(([key, value])=>{\n            iter(key, value, obj);\n        });\n    } else {\n        obj.forEach((entry, index)=>iter(index, entry, obj));\n    }\n}\nfunction getArchtype(thing) {\n    const state = thing[DRAFT_STATE];\n    return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */  : isMap(thing) ? 2 /* Map */  : isSet(thing) ? 3 /* Set */  : 0 /* Object */ ;\n}\nfunction has(thing, prop) {\n    return getArchtype(thing) === 2 /* Map */  ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);\n}\nfunction get(thing, prop) {\n    return getArchtype(thing) === 2 /* Map */  ? thing.get(prop) : thing[prop];\n}\nfunction set(thing, propOrOldValue, value) {\n    const t = getArchtype(thing);\n    if (t === 2 /* Map */ ) thing.set(propOrOldValue, value);\n    else if (t === 3 /* Set */ ) {\n        thing.add(value);\n    } else thing[propOrOldValue] = value;\n}\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || 1 / x === 1 / y;\n    } else {\n        return x !== x && y !== y;\n    }\n}\nfunction isMap(target) {\n    return target instanceof Map;\n}\nfunction isSet(target) {\n    return target instanceof Set;\n}\nfunction latest(state) {\n    return state.copy_ || state.base_;\n}\nfunction shallowCopy(base, strict) {\n    if (isMap(base)) {\n        return new Map(base);\n    }\n    if (isSet(base)) {\n        return new Set(base);\n    }\n    if (Array.isArray(base)) return Array.prototype.slice.call(base);\n    if (!strict && isPlainObject(base)) {\n        if (!getPrototypeOf(base)) {\n            const obj = /* @__PURE__ */ Object.create(null);\n            return Object.assign(obj, base);\n        }\n        return {\n            ...base\n        };\n    }\n    const descriptors = Object.getOwnPropertyDescriptors(base);\n    delete descriptors[DRAFT_STATE];\n    let keys = Reflect.ownKeys(descriptors);\n    for(let i = 0; i < keys.length; i++){\n        const key = keys[i];\n        const desc = descriptors[key];\n        if (desc.writable === false) {\n            desc.writable = true;\n            desc.configurable = true;\n        }\n        if (desc.get || desc.set) descriptors[key] = {\n            configurable: true,\n            writable: true,\n            // could live with !!desc.set as well here...\n            enumerable: desc.enumerable,\n            value: base[key]\n        };\n    }\n    return Object.create(getPrototypeOf(base), descriptors);\n}\nfunction freeze(obj, deep = false) {\n    if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj;\n    if (getArchtype(obj) > 1) {\n        obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;\n    }\n    Object.freeze(obj);\n    if (deep) each(obj, (_key, value)=>freeze(value, true), true);\n    return obj;\n}\nfunction dontMutateFrozenCollections() {\n    die(2);\n}\nfunction isFrozen(obj) {\n    return Object.isFrozen(obj);\n}\n// src/utils/plugins.ts\nvar plugins = {};\nfunction getPlugin(pluginKey) {\n    const plugin = plugins[pluginKey];\n    if (!plugin) {\n        die(0, pluginKey);\n    }\n    return plugin;\n}\nfunction loadPlugin(pluginKey, implementation) {\n    if (!plugins[pluginKey]) plugins[pluginKey] = implementation;\n}\n// src/core/scope.ts\nvar currentScope;\nfunction getCurrentScope() {\n    return currentScope;\n}\nfunction createScope(parent_, immer_) {\n    return {\n        drafts_: [],\n        parent_,\n        immer_,\n        // Whenever the modified draft contains a draft from another scope, we\n        // need to prevent auto-freezing so the unowned draft can be finalized.\n        canAutoFreeze_: true,\n        unfinalizedDrafts_: 0\n    };\n}\nfunction usePatchesInScope(scope, patchListener) {\n    if (patchListener) {\n        getPlugin(\"Patches\");\n        scope.patches_ = [];\n        scope.inversePatches_ = [];\n        scope.patchListener_ = patchListener;\n    }\n}\nfunction revokeScope(scope) {\n    leaveScope(scope);\n    scope.drafts_.forEach(revokeDraft);\n    scope.drafts_ = null;\n}\nfunction leaveScope(scope) {\n    if (scope === currentScope) {\n        currentScope = scope.parent_;\n    }\n}\nfunction enterScope(immer2) {\n    return currentScope = createScope(currentScope, immer2);\n}\nfunction revokeDraft(draft) {\n    const state = draft[DRAFT_STATE];\n    if (state.type_ === 0 /* Object */  || state.type_ === 1 /* Array */ ) state.revoke_();\n    else state.revoked_ = true;\n}\n// src/core/finalize.ts\nfunction processResult(result, scope) {\n    scope.unfinalizedDrafts_ = scope.drafts_.length;\n    const baseDraft = scope.drafts_[0];\n    const isReplaced = result !== void 0 && result !== baseDraft;\n    if (isReplaced) {\n        if (baseDraft[DRAFT_STATE].modified_) {\n            revokeScope(scope);\n            die(4);\n        }\n        if (isDraftable(result)) {\n            result = finalize(scope, result);\n            if (!scope.parent_) maybeFreeze(scope, result);\n        }\n        if (scope.patches_) {\n            getPlugin(\"Patches\").generateReplacementPatches_(baseDraft[DRAFT_STATE].base_, result, scope.patches_, scope.inversePatches_);\n        }\n    } else {\n        result = finalize(scope, baseDraft, []);\n    }\n    revokeScope(scope);\n    if (scope.patches_) {\n        scope.patchListener_(scope.patches_, scope.inversePatches_);\n    }\n    return result !== NOTHING ? result : void 0;\n}\nfunction finalize(rootScope, value, path) {\n    if (isFrozen(value)) return value;\n    const state = value[DRAFT_STATE];\n    if (!state) {\n        each(value, (key, childValue)=>finalizeProperty(rootScope, state, value, key, childValue, path), true);\n        return value;\n    }\n    if (state.scope_ !== rootScope) return value;\n    if (!state.modified_) {\n        maybeFreeze(rootScope, state.base_, true);\n        return state.base_;\n    }\n    if (!state.finalized_) {\n        state.finalized_ = true;\n        state.scope_.unfinalizedDrafts_--;\n        const result = state.copy_;\n        let resultEach = result;\n        let isSet2 = false;\n        if (state.type_ === 3 /* Set */ ) {\n            resultEach = new Set(result);\n            result.clear();\n            isSet2 = true;\n        }\n        each(resultEach, (key, childValue)=>finalizeProperty(rootScope, state, result, key, childValue, path, isSet2));\n        maybeFreeze(rootScope, result, false);\n        if (path && rootScope.patches_) {\n            getPlugin(\"Patches\").generatePatches_(state, path, rootScope.patches_, rootScope.inversePatches_);\n        }\n    }\n    return state.copy_;\n}\nfunction finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {\n    if ( true && childValue === targetObject) die(5);\n    if (isDraft(childValue)) {\n        const path = rootPath && parentState && parentState.type_ !== 3 /* Set */  && // Set objects are atomic since they have no keys.\n        !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;\n        const res = finalize(rootScope, childValue, path);\n        set(targetObject, prop, res);\n        if (isDraft(res)) {\n            rootScope.canAutoFreeze_ = false;\n        } else return;\n    } else if (targetIsSet) {\n        targetObject.add(childValue);\n    }\n    if (isDraftable(childValue) && !isFrozen(childValue)) {\n        if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n            return;\n        }\n        finalize(rootScope, childValue);\n        if (!parentState || !parentState.scope_.parent_) maybeFreeze(rootScope, childValue);\n    }\n}\nfunction maybeFreeze(scope, value, deep = false) {\n    if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n        freeze(value, deep);\n    }\n}\n// src/core/proxy.ts\nfunction createProxyProxy(base, parent) {\n    const isArray = Array.isArray(base);\n    const state = {\n        type_: isArray ? 1 /* Array */  : 0 /* Object */ ,\n        // Track which produce call this is associated with.\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        // True for both shallow and deep changes.\n        modified_: false,\n        // Used during finalization.\n        finalized_: false,\n        // Track which properties have been assigned (true) or deleted (false).\n        assigned_: {},\n        // The parent draft state.\n        parent_: parent,\n        // The base state.\n        base_: base,\n        // The base proxy.\n        draft_: null,\n        // set below\n        // The base copy with any updated values.\n        copy_: null,\n        // Called by the `produce` function.\n        revoke_: null,\n        isManual_: false\n    };\n    let target = state;\n    let traps = objectTraps;\n    if (isArray) {\n        target = [\n            state\n        ];\n        traps = arrayTraps;\n    }\n    const { revoke, proxy } = Proxy.revocable(target, traps);\n    state.draft_ = proxy;\n    state.revoke_ = revoke;\n    return proxy;\n}\nvar objectTraps = {\n    get (state, prop) {\n        if (prop === DRAFT_STATE) return state;\n        const source = latest(state);\n        if (!has(source, prop)) {\n            return readPropFromProto(state, source, prop);\n        }\n        const value = source[prop];\n        if (state.finalized_ || !isDraftable(value)) {\n            return value;\n        }\n        if (value === peek(state.base_, prop)) {\n            prepareCopy(state);\n            return state.copy_[prop] = createProxy(value, state);\n        }\n        return value;\n    },\n    has (state, prop) {\n        return prop in latest(state);\n    },\n    ownKeys (state) {\n        return Reflect.ownKeys(latest(state));\n    },\n    set (state, prop, value) {\n        const desc = getDescriptorFromProto(latest(state), prop);\n        if (desc?.set) {\n            desc.set.call(state.draft_, value);\n            return true;\n        }\n        if (!state.modified_) {\n            const current2 = peek(latest(state), prop);\n            const currentState = current2?.[DRAFT_STATE];\n            if (currentState && currentState.base_ === value) {\n                state.copy_[prop] = value;\n                state.assigned_[prop] = false;\n                return true;\n            }\n            if (is(value, current2) && (value !== void 0 || has(state.base_, prop))) return true;\n            prepareCopy(state);\n            markChanged(state);\n        }\n        if (state.copy_[prop] === value && // special case: handle new props with value 'undefined'\n        (value !== void 0 || prop in state.copy_) || // special case: NaN\n        Number.isNaN(value) && Number.isNaN(state.copy_[prop])) return true;\n        state.copy_[prop] = value;\n        state.assigned_[prop] = true;\n        return true;\n    },\n    deleteProperty (state, prop) {\n        if (peek(state.base_, prop) !== void 0 || prop in state.base_) {\n            state.assigned_[prop] = false;\n            prepareCopy(state);\n            markChanged(state);\n        } else {\n            delete state.assigned_[prop];\n        }\n        if (state.copy_) {\n            delete state.copy_[prop];\n        }\n        return true;\n    },\n    // Note: We never coerce `desc.value` into an Immer draft, because we can't make\n    // the same guarantee in ES5 mode.\n    getOwnPropertyDescriptor (state, prop) {\n        const owner = latest(state);\n        const desc = Reflect.getOwnPropertyDescriptor(owner, prop);\n        if (!desc) return desc;\n        return {\n            writable: true,\n            configurable: state.type_ !== 1 /* Array */  || prop !== \"length\",\n            enumerable: desc.enumerable,\n            value: owner[prop]\n        };\n    },\n    defineProperty () {\n        die(11);\n    },\n    getPrototypeOf (state) {\n        return getPrototypeOf(state.base_);\n    },\n    setPrototypeOf () {\n        die(12);\n    }\n};\nvar arrayTraps = {};\neach(objectTraps, (key, fn)=>{\n    arrayTraps[key] = function() {\n        arguments[0] = arguments[0][0];\n        return fn.apply(this, arguments);\n    };\n});\narrayTraps.deleteProperty = function(state, prop) {\n    if ( true && isNaN(parseInt(prop))) die(13);\n    return arrayTraps.set.call(this, state, prop, void 0);\n};\narrayTraps.set = function(state, prop, value) {\n    if ( true && prop !== \"length\" && isNaN(parseInt(prop))) die(14);\n    return objectTraps.set.call(this, state[0], prop, value, state[0]);\n};\nfunction peek(draft, prop) {\n    const state = draft[DRAFT_STATE];\n    const source = state ? latest(state) : draft;\n    return source[prop];\n}\nfunction readPropFromProto(state, source, prop) {\n    const desc = getDescriptorFromProto(source, prop);\n    return desc ? `value` in desc ? desc.value : (// This is a very special case, if the prop is a getter defined by the\n    // prototype, we should invoke it with the draft as context!\n    desc.get?.call(state.draft_)) : void 0;\n}\nfunction getDescriptorFromProto(source, prop) {\n    if (!(prop in source)) return void 0;\n    let proto = getPrototypeOf(source);\n    while(proto){\n        const desc = Object.getOwnPropertyDescriptor(proto, prop);\n        if (desc) return desc;\n        proto = getPrototypeOf(proto);\n    }\n    return void 0;\n}\nfunction markChanged(state) {\n    if (!state.modified_) {\n        state.modified_ = true;\n        if (state.parent_) {\n            markChanged(state.parent_);\n        }\n    }\n}\nfunction prepareCopy(state) {\n    if (!state.copy_) {\n        state.copy_ = shallowCopy(state.base_, state.scope_.immer_.useStrictShallowCopy_);\n    }\n}\n// src/core/immerClass.ts\nvar Immer2 = class {\n    constructor(config){\n        this.autoFreeze_ = true;\n        this.useStrictShallowCopy_ = false;\n        /**\n     * The `produce` function takes a value and a \"recipe function\" (whose\n     * return value often depends on the base state). The recipe function is\n     * free to mutate its first argument however it wants. All mutations are\n     * only ever applied to a __copy__ of the base state.\n     *\n     * Pass only a function to create a \"curried producer\" which relieves you\n     * from passing the recipe function every time.\n     *\n     * Only plain objects and arrays are made mutable. All other objects are\n     * considered uncopyable.\n     *\n     * Note: This function is __bound__ to its `Immer` instance.\n     *\n     * @param {any} base - the initial state\n     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n     * @param {Function} patchListener - optional function that will be called with all the patches produced here\n     * @returns {any} a new state, or the initial state if nothing was modified\n     */ this.produce = (base, recipe, patchListener)=>{\n            if (typeof base === \"function\" && typeof recipe !== \"function\") {\n                const defaultBase = recipe;\n                recipe = base;\n                const self = this;\n                return function curriedProduce(base2 = defaultBase, ...args) {\n                    return self.produce(base2, (draft)=>recipe.call(this, draft, ...args));\n                };\n            }\n            if (typeof recipe !== \"function\") die(6);\n            if (patchListener !== void 0 && typeof patchListener !== \"function\") die(7);\n            let result;\n            if (isDraftable(base)) {\n                const scope = enterScope(this);\n                const proxy = createProxy(base, void 0);\n                let hasError = true;\n                try {\n                    result = recipe(proxy);\n                    hasError = false;\n                } finally{\n                    if (hasError) revokeScope(scope);\n                    else leaveScope(scope);\n                }\n                usePatchesInScope(scope, patchListener);\n                return processResult(result, scope);\n            } else if (!base || typeof base !== \"object\") {\n                result = recipe(base);\n                if (result === void 0) result = base;\n                if (result === NOTHING) result = void 0;\n                if (this.autoFreeze_) freeze(result, true);\n                if (patchListener) {\n                    const p = [];\n                    const ip = [];\n                    getPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip);\n                    patchListener(p, ip);\n                }\n                return result;\n            } else die(1, base);\n        };\n        this.produceWithPatches = (base, recipe)=>{\n            if (typeof base === \"function\") {\n                return (state, ...args)=>this.produceWithPatches(state, (draft)=>base(draft, ...args));\n            }\n            let patches, inversePatches;\n            const result = this.produce(base, recipe, (p, ip)=>{\n                patches = p;\n                inversePatches = ip;\n            });\n            return [\n                result,\n                patches,\n                inversePatches\n            ];\n        };\n        if (typeof config?.autoFreeze === \"boolean\") this.setAutoFreeze(config.autoFreeze);\n        if (typeof config?.useStrictShallowCopy === \"boolean\") this.setUseStrictShallowCopy(config.useStrictShallowCopy);\n    }\n    createDraft(base) {\n        if (!isDraftable(base)) die(8);\n        if (isDraft(base)) base = current(base);\n        const scope = enterScope(this);\n        const proxy = createProxy(base, void 0);\n        proxy[DRAFT_STATE].isManual_ = true;\n        leaveScope(scope);\n        return proxy;\n    }\n    finishDraft(draft, patchListener) {\n        const state = draft && draft[DRAFT_STATE];\n        if (!state || !state.isManual_) die(9);\n        const { scope_: scope } = state;\n        usePatchesInScope(scope, patchListener);\n        return processResult(void 0, scope);\n    }\n    /**\n   * Pass true to automatically freeze all copies created by Immer.\n   *\n   * By default, auto-freezing is enabled.\n   */ setAutoFreeze(value) {\n        this.autoFreeze_ = value;\n    }\n    /**\n   * Pass true to enable strict shallow copy.\n   *\n   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n   */ setUseStrictShallowCopy(value) {\n        this.useStrictShallowCopy_ = value;\n    }\n    applyPatches(base, patches) {\n        let i;\n        for(i = patches.length - 1; i >= 0; i--){\n            const patch = patches[i];\n            if (patch.path.length === 0 && patch.op === \"replace\") {\n                base = patch.value;\n                break;\n            }\n        }\n        if (i > -1) {\n            patches = patches.slice(i + 1);\n        }\n        const applyPatchesImpl = getPlugin(\"Patches\").applyPatches_;\n        if (isDraft(base)) {\n            return applyPatchesImpl(base, patches);\n        }\n        return this.produce(base, (draft)=>applyPatchesImpl(draft, patches));\n    }\n};\nfunction createProxy(value, parent) {\n    const draft = isMap(value) ? getPlugin(\"MapSet\").proxyMap_(value, parent) : isSet(value) ? getPlugin(\"MapSet\").proxySet_(value, parent) : createProxyProxy(value, parent);\n    const scope = parent ? parent.scope_ : getCurrentScope();\n    scope.drafts_.push(draft);\n    return draft;\n}\n// src/core/current.ts\nfunction current(value) {\n    if (!isDraft(value)) die(10, value);\n    return currentImpl(value);\n}\nfunction currentImpl(value) {\n    if (!isDraftable(value) || isFrozen(value)) return value;\n    const state = value[DRAFT_STATE];\n    let copy;\n    if (state) {\n        if (!state.modified_) return state.base_;\n        state.finalized_ = true;\n        copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);\n    } else {\n        copy = shallowCopy(value, true);\n    }\n    each(copy, (key, childValue)=>{\n        set(copy, key, currentImpl(childValue));\n    });\n    if (state) {\n        state.finalized_ = false;\n    }\n    return copy;\n}\n// src/plugins/patches.ts\nfunction enablePatches() {\n    const errorOffset = 16;\n    if (true) {\n        errors.push('Sets cannot have \"replace\" patches.', function(op) {\n            return \"Unsupported patch operation: \" + op;\n        }, function(path) {\n            return \"Cannot apply patch, path doesn't resolve: \" + path;\n        }, \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\");\n    }\n    const REPLACE = \"replace\";\n    const ADD = \"add\";\n    const REMOVE = \"remove\";\n    function generatePatches_(state, basePath, patches, inversePatches) {\n        switch(state.type_){\n            case 0 /* Object */ :\n            case 2 /* Map */ :\n                return generatePatchesFromAssigned(state, basePath, patches, inversePatches);\n            case 1 /* Array */ :\n                return generateArrayPatches(state, basePath, patches, inversePatches);\n            case 3 /* Set */ :\n                return generateSetPatches(state, basePath, patches, inversePatches);\n        }\n    }\n    function generateArrayPatches(state, basePath, patches, inversePatches) {\n        let { base_, assigned_ } = state;\n        let copy_ = state.copy_;\n        if (copy_.length < base_.length) {\n            ;\n            [base_, copy_] = [\n                copy_,\n                base_\n            ];\n            [patches, inversePatches] = [\n                inversePatches,\n                patches\n            ];\n        }\n        for(let i = 0; i < base_.length; i++){\n            if (assigned_[i] && copy_[i] !== base_[i]) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: REPLACE,\n                    path,\n                    // Need to maybe clone it, as it can in fact be the original value\n                    // due to the base/copy inversion at the start of this function\n                    value: clonePatchValueIfNeeded(copy_[i])\n                });\n                inversePatches.push({\n                    op: REPLACE,\n                    path,\n                    value: clonePatchValueIfNeeded(base_[i])\n                });\n            }\n        }\n        for(let i = base_.length; i < copy_.length; i++){\n            const path = basePath.concat([\n                i\n            ]);\n            patches.push({\n                op: ADD,\n                path,\n                // Need to maybe clone it, as it can in fact be the original value\n                // due to the base/copy inversion at the start of this function\n                value: clonePatchValueIfNeeded(copy_[i])\n            });\n        }\n        for(let i = copy_.length - 1; base_.length <= i; --i){\n            const path = basePath.concat([\n                i\n            ]);\n            inversePatches.push({\n                op: REMOVE,\n                path\n            });\n        }\n    }\n    function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {\n        const { base_, copy_ } = state;\n        each(state.assigned_, (key, assignedValue)=>{\n            const origValue = get(base_, key);\n            const value = get(copy_, key);\n            const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;\n            if (origValue === value && op === REPLACE) return;\n            const path = basePath.concat(key);\n            patches.push(op === REMOVE ? {\n                op,\n                path\n            } : {\n                op,\n                path,\n                value\n            });\n            inversePatches.push(op === ADD ? {\n                op: REMOVE,\n                path\n            } : op === REMOVE ? {\n                op: ADD,\n                path,\n                value: clonePatchValueIfNeeded(origValue)\n            } : {\n                op: REPLACE,\n                path,\n                value: clonePatchValueIfNeeded(origValue)\n            });\n        });\n    }\n    function generateSetPatches(state, basePath, patches, inversePatches) {\n        let { base_, copy_ } = state;\n        let i = 0;\n        base_.forEach((value)=>{\n            if (!copy_.has(value)) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: REMOVE,\n                    path,\n                    value\n                });\n                inversePatches.unshift({\n                    op: ADD,\n                    path,\n                    value\n                });\n            }\n            i++;\n        });\n        i = 0;\n        copy_.forEach((value)=>{\n            if (!base_.has(value)) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: ADD,\n                    path,\n                    value\n                });\n                inversePatches.unshift({\n                    op: REMOVE,\n                    path,\n                    value\n                });\n            }\n            i++;\n        });\n    }\n    function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {\n        patches.push({\n            op: REPLACE,\n            path: [],\n            value: replacement === NOTHING ? void 0 : replacement\n        });\n        inversePatches.push({\n            op: REPLACE,\n            path: [],\n            value: baseValue\n        });\n    }\n    function applyPatches_(draft, patches) {\n        patches.forEach((patch)=>{\n            const { path, op } = patch;\n            let base = draft;\n            for(let i = 0; i < path.length - 1; i++){\n                const parentType = getArchtype(base);\n                let p = path[i];\n                if (typeof p !== \"string\" && typeof p !== \"number\") {\n                    p = \"\" + p;\n                }\n                if ((parentType === 0 /* Object */  || parentType === 1 /* Array */ ) && (p === \"__proto__\" || p === \"constructor\")) die(errorOffset + 3);\n                if (typeof base === \"function\" && p === \"prototype\") die(errorOffset + 3);\n                base = get(base, p);\n                if (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"));\n            }\n            const type = getArchtype(base);\n            const value = deepClonePatchValue(patch.value);\n            const key = path[path.length - 1];\n            switch(op){\n                case REPLACE:\n                    switch(type){\n                        case 2 /* Map */ :\n                            return base.set(key, value);\n                        case 3 /* Set */ :\n                            die(errorOffset);\n                        default:\n                            return base[key] = value;\n                    }\n                case ADD:\n                    switch(type){\n                        case 1 /* Array */ :\n                            return key === \"-\" ? base.push(value) : base.splice(key, 0, value);\n                        case 2 /* Map */ :\n                            return base.set(key, value);\n                        case 3 /* Set */ :\n                            return base.add(value);\n                        default:\n                            return base[key] = value;\n                    }\n                case REMOVE:\n                    switch(type){\n                        case 1 /* Array */ :\n                            return base.splice(key, 1);\n                        case 2 /* Map */ :\n                            return base.delete(key);\n                        case 3 /* Set */ :\n                            return base.delete(patch.value);\n                        default:\n                            return delete base[key];\n                    }\n                default:\n                    die(errorOffset + 1, op);\n            }\n        });\n        return draft;\n    }\n    function deepClonePatchValue(obj) {\n        if (!isDraftable(obj)) return obj;\n        if (Array.isArray(obj)) return obj.map(deepClonePatchValue);\n        if (isMap(obj)) return new Map(Array.from(obj.entries()).map(([k, v])=>[\n                k,\n                deepClonePatchValue(v)\n            ]));\n        if (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue));\n        const cloned = Object.create(getPrototypeOf(obj));\n        for(const key in obj)cloned[key] = deepClonePatchValue(obj[key]);\n        if (has(obj, DRAFTABLE)) cloned[DRAFTABLE] = obj[DRAFTABLE];\n        return cloned;\n    }\n    function clonePatchValueIfNeeded(obj) {\n        if (isDraft(obj)) {\n            return deepClonePatchValue(obj);\n        } else return obj;\n    }\n    loadPlugin(\"Patches\", {\n        applyPatches_,\n        generatePatches_,\n        generateReplacementPatches_\n    });\n}\n// src/plugins/mapset.ts\nfunction enableMapSet() {\n    class DraftMap extends Map {\n        constructor(target, parent){\n            super();\n            this[DRAFT_STATE] = {\n                type_: 2 /* Map */ ,\n                parent_: parent,\n                scope_: parent ? parent.scope_ : getCurrentScope(),\n                modified_: false,\n                finalized_: false,\n                copy_: void 0,\n                assigned_: void 0,\n                base_: target,\n                draft_: this,\n                isManual_: false,\n                revoked_: false\n            };\n        }\n        get size() {\n            return latest(this[DRAFT_STATE]).size;\n        }\n        has(key) {\n            return latest(this[DRAFT_STATE]).has(key);\n        }\n        set(key, value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!latest(state).has(key) || latest(state).get(key) !== value) {\n                prepareMapCopy(state);\n                markChanged(state);\n                state.assigned_.set(key, true);\n                state.copy_.set(key, value);\n                state.assigned_.set(key, true);\n            }\n            return this;\n        }\n        delete(key) {\n            if (!this.has(key)) {\n                return false;\n            }\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareMapCopy(state);\n            markChanged(state);\n            if (state.base_.has(key)) {\n                state.assigned_.set(key, false);\n            } else {\n                state.assigned_.delete(key);\n            }\n            state.copy_.delete(key);\n            return true;\n        }\n        clear() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (latest(state).size) {\n                prepareMapCopy(state);\n                markChanged(state);\n                state.assigned_ = /* @__PURE__ */ new Map();\n                each(state.base_, (key)=>{\n                    state.assigned_.set(key, false);\n                });\n                state.copy_.clear();\n            }\n        }\n        forEach(cb, thisArg) {\n            const state = this[DRAFT_STATE];\n            latest(state).forEach((_value, key, _map)=>{\n                cb.call(thisArg, this.get(key), key, this);\n            });\n        }\n        get(key) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            const value = latest(state).get(key);\n            if (state.finalized_ || !isDraftable(value)) {\n                return value;\n            }\n            if (value !== state.base_.get(key)) {\n                return value;\n            }\n            const draft = createProxy(value, state);\n            prepareMapCopy(state);\n            state.copy_.set(key, draft);\n            return draft;\n        }\n        keys() {\n            return latest(this[DRAFT_STATE]).keys();\n        }\n        values() {\n            const iterator = this.keys();\n            return {\n                [Symbol.iterator]: ()=>this.values(),\n                next: ()=>{\n                    const r = iterator.next();\n                    if (r.done) return r;\n                    const value = this.get(r.value);\n                    return {\n                        done: false,\n                        value\n                    };\n                }\n            };\n        }\n        entries() {\n            const iterator = this.keys();\n            return {\n                [Symbol.iterator]: ()=>this.entries(),\n                next: ()=>{\n                    const r = iterator.next();\n                    if (r.done) return r;\n                    const value = this.get(r.value);\n                    return {\n                        done: false,\n                        value: [\n                            r.value,\n                            value\n                        ]\n                    };\n                }\n            };\n        }\n        [(DRAFT_STATE, Symbol.iterator)]() {\n            return this.entries();\n        }\n    }\n    function proxyMap_(target, parent) {\n        return new DraftMap(target, parent);\n    }\n    function prepareMapCopy(state) {\n        if (!state.copy_) {\n            state.assigned_ = /* @__PURE__ */ new Map();\n            state.copy_ = new Map(state.base_);\n        }\n    }\n    class DraftSet extends Set {\n        constructor(target, parent){\n            super();\n            this[DRAFT_STATE] = {\n                type_: 3 /* Set */ ,\n                parent_: parent,\n                scope_: parent ? parent.scope_ : getCurrentScope(),\n                modified_: false,\n                finalized_: false,\n                copy_: void 0,\n                base_: target,\n                draft_: this,\n                drafts_: /* @__PURE__ */ new Map(),\n                revoked_: false,\n                isManual_: false\n            };\n        }\n        get size() {\n            return latest(this[DRAFT_STATE]).size;\n        }\n        has(value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!state.copy_) {\n                return state.base_.has(value);\n            }\n            if (state.copy_.has(value)) return true;\n            if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value))) return true;\n            return false;\n        }\n        add(value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!this.has(value)) {\n                prepareSetCopy(state);\n                markChanged(state);\n                state.copy_.add(value);\n            }\n            return this;\n        }\n        delete(value) {\n            if (!this.has(value)) {\n                return false;\n            }\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            markChanged(state);\n            return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : /* istanbul ignore next */ false);\n        }\n        clear() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (latest(state).size) {\n                prepareSetCopy(state);\n                markChanged(state);\n                state.copy_.clear();\n            }\n        }\n        values() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            return state.copy_.values();\n        }\n        entries() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            return state.copy_.entries();\n        }\n        keys() {\n            return this.values();\n        }\n        [(DRAFT_STATE, Symbol.iterator)]() {\n            return this.values();\n        }\n        forEach(cb, thisArg) {\n            const iterator = this.values();\n            let result = iterator.next();\n            while(!result.done){\n                cb.call(thisArg, result.value, result.value, this);\n                result = iterator.next();\n            }\n        }\n    }\n    function proxySet_(target, parent) {\n        return new DraftSet(target, parent);\n    }\n    function prepareSetCopy(state) {\n        if (!state.copy_) {\n            state.copy_ = /* @__PURE__ */ new Set();\n            state.base_.forEach((value)=>{\n                if (isDraftable(value)) {\n                    const draft = createProxy(value, state);\n                    state.drafts_.set(value, draft);\n                    state.copy_.add(draft);\n                } else {\n                    state.copy_.add(value);\n                }\n            });\n        }\n    }\n    function assertUnrevoked(state) {\n        if (state.revoked_) die(3, JSON.stringify(latest(state)));\n    }\n    loadPlugin(\"MapSet\", {\n        proxyMap_,\n        proxySet_\n    });\n}\n// src/immer.ts\nvar immer = new Immer2();\nvar produce = immer.produce;\nvar produceWithPatches = immer.produceWithPatches.bind(immer);\nvar setAutoFreeze = immer.setAutoFreeze.bind(immer);\nvar setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);\nvar applyPatches = immer.applyPatches.bind(immer);\nvar createDraft = immer.createDraft.bind(immer);\nvar finishDraft = immer.finishDraft.bind(immer);\nfunction castDraft(value) {\n    return value;\n}\nfunction castImmutable(value) {\n    return value;\n}\n //# sourceMappingURL=immer.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW1tZXIvZGlzdC9pbW1lci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG1CQUFtQjtBQUNuQixJQUFJQSxVQUFVQyxPQUFPQyxHQUFHLENBQUM7QUFDekIsSUFBSUMsWUFBWUYsT0FBT0MsR0FBRyxDQUFDO0FBQzNCLElBQUlFLGNBQWNILE9BQU9DLEdBQUcsQ0FBQztBQUU3QixzQkFBc0I7QUFDdEIsSUFBSUcsU0FBU0MsS0FBcUMsR0FBRztJQUNuRCxrQ0FBa0M7SUFDbEMsU0FBU0MsTUFBTTtRQUNiLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRUEsT0FBTyxnRkFBZ0YsRUFBRUEsT0FBTyx3Q0FBd0MsQ0FBQztJQUNySztJQUNBLFNBQVNDLEtBQUs7UUFDWixPQUFPLENBQUMsbUpBQW1KLEVBQUVBLE1BQU0sQ0FBQyxDQUFDO0lBQ3ZLO0lBQ0E7SUFDQSxTQUFTQyxJQUFJO1FBQ1gsT0FBTyx5SEFBeUhBO0lBQ2xJO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0EsU0FBU0QsS0FBSztRQUNaLE9BQU8sQ0FBQyxnQ0FBZ0MsRUFBRUEsTUFBTSxDQUFDO0lBQ25EO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQSxTQUFTQSxLQUFLO1FBQ1osT0FBTyxDQUFDLGlDQUFpQyxFQUFFQSxNQUFNLENBQUM7SUFDcEQ7Q0FHRCxHQUFHLENBQUU7QUFDTixTQUFTRSxJQUFJQyxLQUFLLEVBQUUsR0FBR0MsSUFBSTtJQUN6QixJQUFJTixJQUFxQyxFQUFFO1FBQ3pDLE1BQU1PLElBQUlSLE1BQU0sQ0FBQ00sTUFBTTtRQUN2QixNQUFNRyxNQUFNLE9BQU9ELE1BQU0sYUFBYUEsRUFBRUUsS0FBSyxDQUFDLE1BQU1ILFFBQVFDO1FBQzVELE1BQU0sSUFBSUcsTUFBTSxDQUFDLFFBQVEsRUFBRUYsSUFBSSxDQUFDO0lBQ2xDO0lBQ0EsTUFBTSxJQUFJRSxNQUNSLENBQUMsMkJBQTJCLEVBQUVMLE1BQU0sdUNBQXVDLENBQUM7QUFFaEY7QUFFQSxzQkFBc0I7QUFDdEIsSUFBSU0saUJBQWlCQyxPQUFPRCxjQUFjO0FBQzFDLFNBQVNFLFFBQVFDLEtBQUs7SUFDcEIsT0FBTyxDQUFDLENBQUNBLFNBQVMsQ0FBQyxDQUFDQSxLQUFLLENBQUNoQixZQUFZO0FBQ3hDO0FBQ0EsU0FBU2lCLFlBQVlELEtBQUs7SUFDeEIsSUFBSSxDQUFDQSxPQUNILE9BQU87SUFDVCxPQUFPRSxjQUFjRixVQUFVRyxNQUFNQyxPQUFPLENBQUNKLFVBQVUsQ0FBQyxDQUFDQSxLQUFLLENBQUNqQixVQUFVLElBQUksQ0FBQyxDQUFDaUIsTUFBTUssV0FBVyxFQUFFLENBQUN0QixVQUFVLElBQUl1QixNQUFNTixVQUFVTyxNQUFNUDtBQUN6STtBQUNBLElBQUlRLG1CQUFtQlYsT0FBT1csU0FBUyxDQUFDSixXQUFXLENBQUNLLFFBQVE7QUFDNUQsU0FBU1IsY0FBY0YsS0FBSztJQUMxQixJQUFJLENBQUNBLFNBQVMsT0FBT0EsVUFBVSxVQUM3QixPQUFPO0lBQ1QsTUFBTVcsUUFBUWQsZUFBZUc7SUFDN0IsSUFBSVcsVUFBVSxNQUFNO1FBQ2xCLE9BQU87SUFDVDtJQUNBLE1BQU1DLE9BQU9kLE9BQU9lLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDSCxPQUFPLGtCQUFrQkEsTUFBTU4sV0FBVztJQUNsRixJQUFJTyxTQUFTZCxRQUNYLE9BQU87SUFDVCxPQUFPLE9BQU9jLFFBQVEsY0FBY0csU0FBU0wsUUFBUSxDQUFDSSxJQUFJLENBQUNGLFVBQVVKO0FBQ3ZFO0FBQ0EsU0FBU1EsU0FBU2hCLEtBQUs7SUFDckIsSUFBSSxDQUFDRCxRQUFRQyxRQUNYVixJQUFJLElBQUlVO0lBQ1YsT0FBT0EsS0FBSyxDQUFDaEIsWUFBWSxDQUFDaUMsS0FBSztBQUNqQztBQUNBLFNBQVNDLEtBQUtDLEdBQUcsRUFBRUMsSUFBSTtJQUNyQixJQUFJQyxZQUFZRixTQUFTLEVBQUUsVUFBVSxLQUFJO1FBQ3ZDckIsT0FBT3dCLE9BQU8sQ0FBQ0gsS0FBS0ksT0FBTyxDQUFDLENBQUMsQ0FBQ0MsS0FBS3hCLE1BQU07WUFDdkNvQixLQUFLSSxLQUFLeEIsT0FBT21CO1FBQ25CO0lBQ0YsT0FBTztRQUNMQSxJQUFJSSxPQUFPLENBQUMsQ0FBQ0UsT0FBT0MsUUFBVU4sS0FBS00sT0FBT0QsT0FBT047SUFDbkQ7QUFDRjtBQUNBLFNBQVNFLFlBQVlqQyxLQUFLO0lBQ3hCLE1BQU11QyxRQUFRdkMsS0FBSyxDQUFDSixZQUFZO0lBQ2hDLE9BQU8yQyxRQUFRQSxNQUFNQyxLQUFLLEdBQUd6QixNQUFNQyxPQUFPLENBQUNoQixTQUFTLEVBQUUsU0FBUyxNQUFLa0IsTUFBTWxCLFNBQVMsRUFBRSxPQUFPLE1BQUttQixNQUFNbkIsU0FBUyxFQUFFLE9BQU8sTUFBSyxFQUFFLFVBQVU7QUFDNUk7QUFDQSxTQUFTeUMsSUFBSXpDLEtBQUssRUFBRTBDLElBQUk7SUFDdEIsT0FBT1QsWUFBWWpDLFdBQVcsRUFBRSxPQUFPLE1BQUtBLE1BQU15QyxHQUFHLENBQUNDLFFBQVFoQyxPQUFPVyxTQUFTLENBQUNJLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDMUIsT0FBTzBDO0FBQzVHO0FBQ0EsU0FBU0MsSUFBSTNDLEtBQUssRUFBRTBDLElBQUk7SUFDdEIsT0FBT1QsWUFBWWpDLFdBQVcsRUFBRSxPQUFPLE1BQUtBLE1BQU0yQyxHQUFHLENBQUNELFFBQVExQyxLQUFLLENBQUMwQyxLQUFLO0FBQzNFO0FBQ0EsU0FBU0UsSUFBSTVDLEtBQUssRUFBRTZDLGNBQWMsRUFBRWpDLEtBQUs7SUFDdkMsTUFBTWtDLElBQUliLFlBQVlqQztJQUN0QixJQUFJOEMsTUFBTSxFQUFFLE9BQU8sS0FDakI5QyxNQUFNNEMsR0FBRyxDQUFDQyxnQkFBZ0JqQztTQUN2QixJQUFJa0MsTUFBTSxFQUFFLE9BQU8sS0FBSTtRQUMxQjlDLE1BQU0rQyxHQUFHLENBQUNuQztJQUNaLE9BQ0VaLEtBQUssQ0FBQzZDLGVBQWUsR0FBR2pDO0FBQzVCO0FBQ0EsU0FBU29DLEdBQUdDLENBQUMsRUFBRUMsQ0FBQztJQUNkLElBQUlELE1BQU1DLEdBQUc7UUFDWCxPQUFPRCxNQUFNLEtBQUssSUFBSUEsTUFBTSxJQUFJQztJQUNsQyxPQUFPO1FBQ0wsT0FBT0QsTUFBTUEsS0FBS0MsTUFBTUE7SUFDMUI7QUFDRjtBQUNBLFNBQVNoQyxNQUFNaUMsTUFBTTtJQUNuQixPQUFPQSxrQkFBa0JDO0FBQzNCO0FBQ0EsU0FBU2pDLE1BQU1nQyxNQUFNO0lBQ25CLE9BQU9BLGtCQUFrQkU7QUFDM0I7QUFDQSxTQUFTQyxPQUFPZixLQUFLO0lBQ25CLE9BQU9BLE1BQU1nQixLQUFLLElBQUloQixNQUFNVixLQUFLO0FBQ25DO0FBQ0EsU0FBUzJCLFlBQVlDLElBQUksRUFBRUMsTUFBTTtJQUMvQixJQUFJeEMsTUFBTXVDLE9BQU87UUFDZixPQUFPLElBQUlMLElBQUlLO0lBQ2pCO0lBQ0EsSUFBSXRDLE1BQU1zQyxPQUFPO1FBQ2YsT0FBTyxJQUFJSixJQUFJSTtJQUNqQjtJQUNBLElBQUkxQyxNQUFNQyxPQUFPLENBQUN5QyxPQUNoQixPQUFPMUMsTUFBTU0sU0FBUyxDQUFDc0MsS0FBSyxDQUFDakMsSUFBSSxDQUFDK0I7SUFDcEMsSUFBSSxDQUFDQyxVQUFVNUMsY0FBYzJDLE9BQU87UUFDbEMsSUFBSSxDQUFDaEQsZUFBZWdELE9BQU87WUFDekIsTUFBTTFCLE1BQU0sYUFBYSxHQUFHckIsT0FBT2tELE1BQU0sQ0FBQztZQUMxQyxPQUFPbEQsT0FBT21ELE1BQU0sQ0FBQzlCLEtBQUswQjtRQUM1QjtRQUNBLE9BQU87WUFBRSxHQUFHQSxJQUFJO1FBQUM7SUFDbkI7SUFDQSxNQUFNSyxjQUFjcEQsT0FBT3FELHlCQUF5QixDQUFDTjtJQUNyRCxPQUFPSyxXQUFXLENBQUNsRSxZQUFZO0lBQy9CLElBQUlvRSxPQUFPQyxRQUFRQyxPQUFPLENBQUNKO0lBQzNCLElBQUssSUFBSUssSUFBSSxHQUFHQSxJQUFJSCxLQUFLSSxNQUFNLEVBQUVELElBQUs7UUFDcEMsTUFBTS9CLE1BQU00QixJQUFJLENBQUNHLEVBQUU7UUFDbkIsTUFBTUUsT0FBT1AsV0FBVyxDQUFDMUIsSUFBSTtRQUM3QixJQUFJaUMsS0FBS0MsUUFBUSxLQUFLLE9BQU87WUFDM0JELEtBQUtDLFFBQVEsR0FBRztZQUNoQkQsS0FBS0UsWUFBWSxHQUFHO1FBQ3RCO1FBQ0EsSUFBSUYsS0FBSzFCLEdBQUcsSUFBSTBCLEtBQUt6QixHQUFHLEVBQ3RCa0IsV0FBVyxDQUFDMUIsSUFBSSxHQUFHO1lBQ2pCbUMsY0FBYztZQUNkRCxVQUFVO1lBQ1YsNkNBQTZDO1lBQzdDRSxZQUFZSCxLQUFLRyxVQUFVO1lBQzNCNUQsT0FBTzZDLElBQUksQ0FBQ3JCLElBQUk7UUFDbEI7SUFDSjtJQUNBLE9BQU8xQixPQUFPa0QsTUFBTSxDQUFDbkQsZUFBZWdELE9BQU9LO0FBQzdDO0FBQ0EsU0FBU1csT0FBTzFDLEdBQUcsRUFBRTJDLE9BQU8sS0FBSztJQUMvQixJQUFJQyxTQUFTNUMsUUFBUXBCLFFBQVFvQixRQUFRLENBQUNsQixZQUFZa0IsTUFDaEQsT0FBT0E7SUFDVCxJQUFJRSxZQUFZRixPQUFPLEdBQUc7UUFDeEJBLElBQUlhLEdBQUcsR0FBR2IsSUFBSWdCLEdBQUcsR0FBR2hCLElBQUk2QyxLQUFLLEdBQUc3QyxJQUFJOEMsTUFBTSxHQUFHQztJQUMvQztJQUNBcEUsT0FBTytELE1BQU0sQ0FBQzFDO0lBQ2QsSUFBSTJDLE1BQ0Y1QyxLQUFLQyxLQUFLLENBQUNnRCxNQUFNbkUsUUFBVTZELE9BQU83RCxPQUFPLE9BQU87SUFDbEQsT0FBT21CO0FBQ1Q7QUFDQSxTQUFTK0M7SUFDUDVFLElBQUk7QUFDTjtBQUNBLFNBQVN5RSxTQUFTNUMsR0FBRztJQUNuQixPQUFPckIsT0FBT2lFLFFBQVEsQ0FBQzVDO0FBQ3pCO0FBRUEsdUJBQXVCO0FBQ3ZCLElBQUlpRCxVQUFVLENBQUM7QUFDZixTQUFTQyxVQUFVQyxTQUFTO0lBQzFCLE1BQU1uRixTQUFTaUYsT0FBTyxDQUFDRSxVQUFVO0lBQ2pDLElBQUksQ0FBQ25GLFFBQVE7UUFDWEcsSUFBSSxHQUFHZ0Y7SUFDVDtJQUNBLE9BQU9uRjtBQUNUO0FBQ0EsU0FBU29GLFdBQVdELFNBQVMsRUFBRUUsY0FBYztJQUMzQyxJQUFJLENBQUNKLE9BQU8sQ0FBQ0UsVUFBVSxFQUNyQkYsT0FBTyxDQUFDRSxVQUFVLEdBQUdFO0FBQ3pCO0FBRUEsb0JBQW9CO0FBQ3BCLElBQUlDO0FBQ0osU0FBU0M7SUFDUCxPQUFPRDtBQUNUO0FBQ0EsU0FBU0UsWUFBWUMsT0FBTyxFQUFFQyxNQUFNO0lBQ2xDLE9BQU87UUFDTEMsU0FBUyxFQUFFO1FBQ1hGO1FBQ0FDO1FBQ0Esc0VBQXNFO1FBQ3RFLHVFQUF1RTtRQUN2RUUsZ0JBQWdCO1FBQ2hCQyxvQkFBb0I7SUFDdEI7QUFDRjtBQUNBLFNBQVNDLGtCQUFrQkMsS0FBSyxFQUFFQyxhQUFhO0lBQzdDLElBQUlBLGVBQWU7UUFDakJkLFVBQVU7UUFDVmEsTUFBTUUsUUFBUSxHQUFHLEVBQUU7UUFDbkJGLE1BQU1HLGVBQWUsR0FBRyxFQUFFO1FBQzFCSCxNQUFNSSxjQUFjLEdBQUdIO0lBQ3pCO0FBQ0Y7QUFDQSxTQUFTSSxZQUFZTCxLQUFLO0lBQ3hCTSxXQUFXTjtJQUNYQSxNQUFNSixPQUFPLENBQUN2RCxPQUFPLENBQUNrRTtJQUN0QlAsTUFBTUosT0FBTyxHQUFHO0FBQ2xCO0FBQ0EsU0FBU1UsV0FBV04sS0FBSztJQUN2QixJQUFJQSxVQUFVVCxjQUFjO1FBQzFCQSxlQUFlUyxNQUFNTixPQUFPO0lBQzlCO0FBQ0Y7QUFDQSxTQUFTYyxXQUFXQyxNQUFNO0lBQ3hCLE9BQU9sQixlQUFlRSxZQUFZRixjQUFja0I7QUFDbEQ7QUFDQSxTQUFTRixZQUFZRyxLQUFLO0lBQ3hCLE1BQU1qRSxRQUFRaUUsS0FBSyxDQUFDNUcsWUFBWTtJQUNoQyxJQUFJMkMsTUFBTUMsS0FBSyxLQUFLLEVBQUUsVUFBVSxPQUFNRCxNQUFNQyxLQUFLLEtBQUssRUFBRSxTQUFTLEtBQy9ERCxNQUFNa0UsT0FBTztTQUVibEUsTUFBTW1FLFFBQVEsR0FBRztBQUNyQjtBQUVBLHVCQUF1QjtBQUN2QixTQUFTQyxjQUFjQyxNQUFNLEVBQUVkLEtBQUs7SUFDbENBLE1BQU1GLGtCQUFrQixHQUFHRSxNQUFNSixPQUFPLENBQUN0QixNQUFNO0lBQy9DLE1BQU15QyxZQUFZZixNQUFNSixPQUFPLENBQUMsRUFBRTtJQUNsQyxNQUFNb0IsYUFBYUYsV0FBVyxLQUFLLEtBQUtBLFdBQVdDO0lBQ25ELElBQUlDLFlBQVk7UUFDZCxJQUFJRCxTQUFTLENBQUNqSCxZQUFZLENBQUNtSCxTQUFTLEVBQUU7WUFDcENaLFlBQVlMO1lBQ1o1RixJQUFJO1FBQ047UUFDQSxJQUFJVyxZQUFZK0YsU0FBUztZQUN2QkEsU0FBU0ksU0FBU2xCLE9BQU9jO1lBQ3pCLElBQUksQ0FBQ2QsTUFBTU4sT0FBTyxFQUNoQnlCLFlBQVluQixPQUFPYztRQUN2QjtRQUNBLElBQUlkLE1BQU1FLFFBQVEsRUFBRTtZQUNsQmYsVUFBVSxXQUFXaUMsMkJBQTJCLENBQzlDTCxTQUFTLENBQUNqSCxZQUFZLENBQUNpQyxLQUFLLEVBQzVCK0UsUUFDQWQsTUFBTUUsUUFBUSxFQUNkRixNQUFNRyxlQUFlO1FBRXpCO0lBQ0YsT0FBTztRQUNMVyxTQUFTSSxTQUFTbEIsT0FBT2UsV0FBVyxFQUFFO0lBQ3hDO0lBQ0FWLFlBQVlMO0lBQ1osSUFBSUEsTUFBTUUsUUFBUSxFQUFFO1FBQ2xCRixNQUFNSSxjQUFjLENBQUNKLE1BQU1FLFFBQVEsRUFBRUYsTUFBTUcsZUFBZTtJQUM1RDtJQUNBLE9BQU9XLFdBQVdwSCxVQUFVb0gsU0FBUyxLQUFLO0FBQzVDO0FBQ0EsU0FBU0ksU0FBU0csU0FBUyxFQUFFdkcsS0FBSyxFQUFFd0csSUFBSTtJQUN0QyxJQUFJekMsU0FBUy9ELFFBQ1gsT0FBT0E7SUFDVCxNQUFNMkIsUUFBUTNCLEtBQUssQ0FBQ2hCLFlBQVk7SUFDaEMsSUFBSSxDQUFDMkMsT0FBTztRQUNWVCxLQUNFbEIsT0FDQSxDQUFDd0IsS0FBS2lGLGFBQWVDLGlCQUFpQkgsV0FBVzVFLE9BQU8zQixPQUFPd0IsS0FBS2lGLFlBQVlELE9BQ2hGO1FBR0YsT0FBT3hHO0lBQ1Q7SUFDQSxJQUFJMkIsTUFBTWdGLE1BQU0sS0FBS0osV0FDbkIsT0FBT3ZHO0lBQ1QsSUFBSSxDQUFDMkIsTUFBTXdFLFNBQVMsRUFBRTtRQUNwQkUsWUFBWUUsV0FBVzVFLE1BQU1WLEtBQUssRUFBRTtRQUNwQyxPQUFPVSxNQUFNVixLQUFLO0lBQ3BCO0lBQ0EsSUFBSSxDQUFDVSxNQUFNaUYsVUFBVSxFQUFFO1FBQ3JCakYsTUFBTWlGLFVBQVUsR0FBRztRQUNuQmpGLE1BQU1nRixNQUFNLENBQUMzQixrQkFBa0I7UUFDL0IsTUFBTWdCLFNBQVNyRSxNQUFNZ0IsS0FBSztRQUMxQixJQUFJa0UsYUFBYWI7UUFDakIsSUFBSWMsU0FBUztRQUNiLElBQUluRixNQUFNQyxLQUFLLEtBQUssRUFBRSxPQUFPLEtBQUk7WUFDL0JpRixhQUFhLElBQUlwRSxJQUFJdUQ7WUFDckJBLE9BQU9oQyxLQUFLO1lBQ1o4QyxTQUFTO1FBQ1g7UUFDQTVGLEtBQ0UyRixZQUNBLENBQUNyRixLQUFLaUYsYUFBZUMsaUJBQWlCSCxXQUFXNUUsT0FBT3FFLFFBQVF4RSxLQUFLaUYsWUFBWUQsTUFBTU07UUFFekZULFlBQVlFLFdBQVdQLFFBQVE7UUFDL0IsSUFBSVEsUUFBUUQsVUFBVW5CLFFBQVEsRUFBRTtZQUM5QmYsVUFBVSxXQUFXMEMsZ0JBQWdCLENBQ25DcEYsT0FDQTZFLE1BQ0FELFVBQVVuQixRQUFRLEVBQ2xCbUIsVUFBVWxCLGVBQWU7UUFFN0I7SUFDRjtJQUNBLE9BQU8xRCxNQUFNZ0IsS0FBSztBQUNwQjtBQUNBLFNBQVMrRCxpQkFBaUJILFNBQVMsRUFBRVMsV0FBVyxFQUFFQyxZQUFZLEVBQUVuRixJQUFJLEVBQUUyRSxVQUFVLEVBQUVTLFFBQVEsRUFBRUMsV0FBVztJQUNyRyxJQUFJakksS0FBcUMsSUFBSXVILGVBQWVRLGNBQzFEM0gsSUFBSTtJQUNOLElBQUlTLFFBQVEwRyxhQUFhO1FBQ3ZCLE1BQU1ELE9BQU9VLFlBQVlGLGVBQWVBLFlBQVlwRixLQUFLLEtBQUssRUFBRSxPQUFPLE9BQU0sa0RBQWtEO1FBQy9ILENBQUNDLElBQUltRixZQUFZSSxTQUFTLEVBQUV0RixRQUFRb0YsU0FBU0csTUFBTSxDQUFDdkYsUUFBUSxLQUFLO1FBQ2pFLE1BQU13RixNQUFNbEIsU0FBU0csV0FBV0UsWUFBWUQ7UUFDNUN4RSxJQUFJaUYsY0FBY25GLE1BQU13RjtRQUN4QixJQUFJdkgsUUFBUXVILE1BQU07WUFDaEJmLFVBQVV4QixjQUFjLEdBQUc7UUFDN0IsT0FDRTtJQUNKLE9BQU8sSUFBSW9DLGFBQWE7UUFDdEJGLGFBQWE5RSxHQUFHLENBQUNzRTtJQUNuQjtJQUNBLElBQUl4RyxZQUFZd0csZUFBZSxDQUFDMUMsU0FBUzBDLGFBQWE7UUFDcEQsSUFBSSxDQUFDRixVQUFVMUIsTUFBTSxDQUFDMEMsV0FBVyxJQUFJaEIsVUFBVXZCLGtCQUFrQixHQUFHLEdBQUc7WUFDckU7UUFDRjtRQUNBb0IsU0FBU0csV0FBV0U7UUFDcEIsSUFBSSxDQUFDTyxlQUFlLENBQUNBLFlBQVlMLE1BQU0sQ0FBQy9CLE9BQU8sRUFDN0N5QixZQUFZRSxXQUFXRTtJQUMzQjtBQUNGO0FBQ0EsU0FBU0osWUFBWW5CLEtBQUssRUFBRWxGLEtBQUssRUFBRThELE9BQU8sS0FBSztJQUM3QyxJQUFJLENBQUNvQixNQUFNTixPQUFPLElBQUlNLE1BQU1MLE1BQU0sQ0FBQzBDLFdBQVcsSUFBSXJDLE1BQU1ILGNBQWMsRUFBRTtRQUN0RWxCLE9BQU83RCxPQUFPOEQ7SUFDaEI7QUFDRjtBQUVBLG9CQUFvQjtBQUNwQixTQUFTMEQsaUJBQWlCM0UsSUFBSSxFQUFFNEUsTUFBTTtJQUNwQyxNQUFNckgsVUFBVUQsTUFBTUMsT0FBTyxDQUFDeUM7SUFDOUIsTUFBTWxCLFFBQVE7UUFDWkMsT0FBT3hCLFVBQVUsRUFBRSxTQUFTLE1BQUssRUFBRSxVQUFVO1FBQzdDLG9EQUFvRDtRQUNwRHVHLFFBQVFjLFNBQVNBLE9BQU9kLE1BQU0sR0FBR2pDO1FBQ2pDLDBDQUEwQztRQUMxQ3lCLFdBQVc7UUFDWCw0QkFBNEI7UUFDNUJTLFlBQVk7UUFDWix1RUFBdUU7UUFDdkVRLFdBQVcsQ0FBQztRQUNaLDBCQUEwQjtRQUMxQnhDLFNBQVM2QztRQUNULGtCQUFrQjtRQUNsQnhHLE9BQU80QjtRQUNQLGtCQUFrQjtRQUNsQjZFLFFBQVE7UUFDUixZQUFZO1FBQ1oseUNBQXlDO1FBQ3pDL0UsT0FBTztRQUNQLG9DQUFvQztRQUNwQ2tELFNBQVM7UUFDVDhCLFdBQVc7SUFDYjtJQUNBLElBQUlwRixTQUFTWjtJQUNiLElBQUlpRyxRQUFRQztJQUNaLElBQUl6SCxTQUFTO1FBQ1htQyxTQUFTO1lBQUNaO1NBQU07UUFDaEJpRyxRQUFRRTtJQUNWO0lBQ0EsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHQyxNQUFNQyxTQUFTLENBQUMzRixRQUFRcUY7SUFDbERqRyxNQUFNK0YsTUFBTSxHQUFHTTtJQUNmckcsTUFBTWtFLE9BQU8sR0FBR2tDO0lBQ2hCLE9BQU9DO0FBQ1Q7QUFDQSxJQUFJSCxjQUFjO0lBQ2hCOUYsS0FBSUosS0FBSyxFQUFFRyxJQUFJO1FBQ2IsSUFBSUEsU0FBUzlDLGFBQ1gsT0FBTzJDO1FBQ1QsTUFBTXdHLFNBQVN6RixPQUFPZjtRQUN0QixJQUFJLENBQUNFLElBQUlzRyxRQUFRckcsT0FBTztZQUN0QixPQUFPc0csa0JBQWtCekcsT0FBT3dHLFFBQVFyRztRQUMxQztRQUNBLE1BQU05QixRQUFRbUksTUFBTSxDQUFDckcsS0FBSztRQUMxQixJQUFJSCxNQUFNaUYsVUFBVSxJQUFJLENBQUMzRyxZQUFZRCxRQUFRO1lBQzNDLE9BQU9BO1FBQ1Q7UUFDQSxJQUFJQSxVQUFVcUksS0FBSzFHLE1BQU1WLEtBQUssRUFBRWEsT0FBTztZQUNyQ3dHLFlBQVkzRztZQUNaLE9BQU9BLE1BQU1nQixLQUFLLENBQUNiLEtBQUssR0FBR3lHLFlBQVl2SSxPQUFPMkI7UUFDaEQ7UUFDQSxPQUFPM0I7SUFDVDtJQUNBNkIsS0FBSUYsS0FBSyxFQUFFRyxJQUFJO1FBQ2IsT0FBT0EsUUFBUVksT0FBT2Y7SUFDeEI7SUFDQTJCLFNBQVEzQixLQUFLO1FBQ1gsT0FBTzBCLFFBQVFDLE9BQU8sQ0FBQ1osT0FBT2Y7SUFDaEM7SUFDQUssS0FBSUwsS0FBSyxFQUFFRyxJQUFJLEVBQUU5QixLQUFLO1FBQ3BCLE1BQU15RCxPQUFPK0UsdUJBQXVCOUYsT0FBT2YsUUFBUUc7UUFDbkQsSUFBSTJCLE1BQU16QixLQUFLO1lBQ2J5QixLQUFLekIsR0FBRyxDQUFDbEIsSUFBSSxDQUFDYSxNQUFNK0YsTUFBTSxFQUFFMUg7WUFDNUIsT0FBTztRQUNUO1FBQ0EsSUFBSSxDQUFDMkIsTUFBTXdFLFNBQVMsRUFBRTtZQUNwQixNQUFNc0MsV0FBV0osS0FBSzNGLE9BQU9mLFFBQVFHO1lBQ3JDLE1BQU00RyxlQUFlRCxVQUFVLENBQUN6SixZQUFZO1lBQzVDLElBQUkwSixnQkFBZ0JBLGFBQWF6SCxLQUFLLEtBQUtqQixPQUFPO2dCQUNoRDJCLE1BQU1nQixLQUFLLENBQUNiLEtBQUssR0FBRzlCO2dCQUNwQjJCLE1BQU15RixTQUFTLENBQUN0RixLQUFLLEdBQUc7Z0JBQ3hCLE9BQU87WUFDVDtZQUNBLElBQUlNLEdBQUdwQyxPQUFPeUksYUFBY3pJLENBQUFBLFVBQVUsS0FBSyxLQUFLNkIsSUFBSUYsTUFBTVYsS0FBSyxFQUFFYSxLQUFJLEdBQ25FLE9BQU87WUFDVHdHLFlBQVkzRztZQUNaZ0gsWUFBWWhIO1FBQ2Q7UUFDQSxJQUFJQSxNQUFNZ0IsS0FBSyxDQUFDYixLQUFLLEtBQUs5QixTQUFTLHdEQUF3RDtRQUMxRkEsQ0FBQUEsVUFBVSxLQUFLLEtBQUs4QixRQUFRSCxNQUFNZ0IsS0FBSyxLQUFLLG9CQUFvQjtRQUNqRWlHLE9BQU9DLEtBQUssQ0FBQzdJLFVBQVU0SSxPQUFPQyxLQUFLLENBQUNsSCxNQUFNZ0IsS0FBSyxDQUFDYixLQUFLLEdBQ25ELE9BQU87UUFDVEgsTUFBTWdCLEtBQUssQ0FBQ2IsS0FBSyxHQUFHOUI7UUFDcEIyQixNQUFNeUYsU0FBUyxDQUFDdEYsS0FBSyxHQUFHO1FBQ3hCLE9BQU87SUFDVDtJQUNBZ0gsZ0JBQWVuSCxLQUFLLEVBQUVHLElBQUk7UUFDeEIsSUFBSXVHLEtBQUsxRyxNQUFNVixLQUFLLEVBQUVhLFVBQVUsS0FBSyxLQUFLQSxRQUFRSCxNQUFNVixLQUFLLEVBQUU7WUFDN0RVLE1BQU15RixTQUFTLENBQUN0RixLQUFLLEdBQUc7WUFDeEJ3RyxZQUFZM0c7WUFDWmdILFlBQVloSDtRQUNkLE9BQU87WUFDTCxPQUFPQSxNQUFNeUYsU0FBUyxDQUFDdEYsS0FBSztRQUM5QjtRQUNBLElBQUlILE1BQU1nQixLQUFLLEVBQUU7WUFDZixPQUFPaEIsTUFBTWdCLEtBQUssQ0FBQ2IsS0FBSztRQUMxQjtRQUNBLE9BQU87SUFDVDtJQUNBLGdGQUFnRjtJQUNoRixrQ0FBa0M7SUFDbENpSCwwQkFBeUJwSCxLQUFLLEVBQUVHLElBQUk7UUFDbEMsTUFBTWtILFFBQVF0RyxPQUFPZjtRQUNyQixNQUFNOEIsT0FBT0osUUFBUTBGLHdCQUF3QixDQUFDQyxPQUFPbEg7UUFDckQsSUFBSSxDQUFDMkIsTUFDSCxPQUFPQTtRQUNULE9BQU87WUFDTEMsVUFBVTtZQUNWQyxjQUFjaEMsTUFBTUMsS0FBSyxLQUFLLEVBQUUsU0FBUyxPQUFNRSxTQUFTO1lBQ3hEOEIsWUFBWUgsS0FBS0csVUFBVTtZQUMzQjVELE9BQU9nSixLQUFLLENBQUNsSCxLQUFLO1FBQ3BCO0lBQ0Y7SUFDQW1IO1FBQ0UzSixJQUFJO0lBQ047SUFDQU8sZ0JBQWU4QixLQUFLO1FBQ2xCLE9BQU85QixlQUFlOEIsTUFBTVYsS0FBSztJQUNuQztJQUNBaUk7UUFDRTVKLElBQUk7SUFDTjtBQUNGO0FBQ0EsSUFBSXdJLGFBQWEsQ0FBQztBQUNsQjVHLEtBQUsyRyxhQUFhLENBQUNyRyxLQUFLMkg7SUFDdEJyQixVQUFVLENBQUN0RyxJQUFJLEdBQUc7UUFDaEI0SCxTQUFTLENBQUMsRUFBRSxHQUFHQSxTQUFTLENBQUMsRUFBRSxDQUFDLEVBQUU7UUFDOUIsT0FBT0QsR0FBR3hKLEtBQUssQ0FBQyxJQUFJLEVBQUV5SjtJQUN4QjtBQUNGO0FBQ0F0QixXQUFXZ0IsY0FBYyxHQUFHLFNBQVNuSCxLQUFLLEVBQUVHLElBQUk7SUFDOUMsSUFBSTVDLEtBQXFDLElBQUkySixNQUFNUSxTQUFTdkgsUUFDMUR4QyxJQUFJO0lBQ04sT0FBT3dJLFdBQVc5RixHQUFHLENBQUNsQixJQUFJLENBQUMsSUFBSSxFQUFFYSxPQUFPRyxNQUFNLEtBQUs7QUFDckQ7QUFDQWdHLFdBQVc5RixHQUFHLEdBQUcsU0FBU0wsS0FBSyxFQUFFRyxJQUFJLEVBQUU5QixLQUFLO0lBQzFDLElBQUlkLEtBQXFDLElBQUk0QyxTQUFTLFlBQVkrRyxNQUFNUSxTQUFTdkgsUUFDL0V4QyxJQUFJO0lBQ04sT0FBT3VJLFlBQVk3RixHQUFHLENBQUNsQixJQUFJLENBQUMsSUFBSSxFQUFFYSxLQUFLLENBQUMsRUFBRSxFQUFFRyxNQUFNOUIsT0FBTzJCLEtBQUssQ0FBQyxFQUFFO0FBQ25FO0FBQ0EsU0FBUzBHLEtBQUt6QyxLQUFLLEVBQUU5RCxJQUFJO0lBQ3ZCLE1BQU1ILFFBQVFpRSxLQUFLLENBQUM1RyxZQUFZO0lBQ2hDLE1BQU1tSixTQUFTeEcsUUFBUWUsT0FBT2YsU0FBU2lFO0lBQ3ZDLE9BQU91QyxNQUFNLENBQUNyRyxLQUFLO0FBQ3JCO0FBQ0EsU0FBU3NHLGtCQUFrQnpHLEtBQUssRUFBRXdHLE1BQU0sRUFBRXJHLElBQUk7SUFDNUMsTUFBTTJCLE9BQU8rRSx1QkFBdUJMLFFBQVFyRztJQUM1QyxPQUFPMkIsT0FBTyxDQUFDLEtBQUssQ0FBQyxJQUFJQSxPQUFPQSxLQUFLekQsS0FBSyxHQUFHLENBQzNDLHNFQUFzRTtJQUN0RSw0REFBNEQ7SUFDNUR5RCxLQUFLMUIsR0FBRyxFQUFFakIsS0FBS2EsTUFBTStGLE1BQU0sQ0FDN0IsSUFBSSxLQUFLO0FBQ1g7QUFDQSxTQUFTYyx1QkFBdUJMLE1BQU0sRUFBRXJHLElBQUk7SUFDMUMsSUFBSSxDQUFFQSxDQUFBQSxRQUFRcUcsTUFBSyxHQUNqQixPQUFPLEtBQUs7SUFDZCxJQUFJeEgsUUFBUWQsZUFBZXNJO0lBQzNCLE1BQU94SCxNQUFPO1FBQ1osTUFBTThDLE9BQU8zRCxPQUFPaUosd0JBQXdCLENBQUNwSSxPQUFPbUI7UUFDcEQsSUFBSTJCLE1BQ0YsT0FBT0E7UUFDVDlDLFFBQVFkLGVBQWVjO0lBQ3pCO0lBQ0EsT0FBTyxLQUFLO0FBQ2Q7QUFDQSxTQUFTZ0ksWUFBWWhILEtBQUs7SUFDeEIsSUFBSSxDQUFDQSxNQUFNd0UsU0FBUyxFQUFFO1FBQ3BCeEUsTUFBTXdFLFNBQVMsR0FBRztRQUNsQixJQUFJeEUsTUFBTWlELE9BQU8sRUFBRTtZQUNqQitELFlBQVloSCxNQUFNaUQsT0FBTztRQUMzQjtJQUNGO0FBQ0Y7QUFDQSxTQUFTMEQsWUFBWTNHLEtBQUs7SUFDeEIsSUFBSSxDQUFDQSxNQUFNZ0IsS0FBSyxFQUFFO1FBQ2hCaEIsTUFBTWdCLEtBQUssR0FBR0MsWUFDWmpCLE1BQU1WLEtBQUssRUFDWFUsTUFBTWdGLE1BQU0sQ0FBQzlCLE1BQU0sQ0FBQ3lFLHFCQUFxQjtJQUU3QztBQUNGO0FBRUEseUJBQXlCO0FBQ3pCLElBQUlDLFNBQVM7SUFDWGxKLFlBQVltSixNQUFNLENBQUU7UUFDbEIsSUFBSSxDQUFDakMsV0FBVyxHQUFHO1FBQ25CLElBQUksQ0FBQytCLHFCQUFxQixHQUFHO1FBQzdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7S0FrQkMsR0FDRCxJQUFJLENBQUNHLE9BQU8sR0FBRyxDQUFDNUcsTUFBTTZHLFFBQVF2RTtZQUM1QixJQUFJLE9BQU90QyxTQUFTLGNBQWMsT0FBTzZHLFdBQVcsWUFBWTtnQkFDOUQsTUFBTUMsY0FBY0Q7Z0JBQ3BCQSxTQUFTN0c7Z0JBQ1QsTUFBTStHLE9BQU8sSUFBSTtnQkFDakIsT0FBTyxTQUFTQyxlQUFlQyxRQUFRSCxXQUFXLEVBQUUsR0FBR25LLElBQUk7b0JBQ3pELE9BQU9vSyxLQUFLSCxPQUFPLENBQUNLLE9BQU8sQ0FBQ2xFLFFBQVU4RCxPQUFPNUksSUFBSSxDQUFDLElBQUksRUFBRThFLFVBQVVwRztnQkFDcEU7WUFDRjtZQUNBLElBQUksT0FBT2tLLFdBQVcsWUFDcEJwSyxJQUFJO1lBQ04sSUFBSTZGLGtCQUFrQixLQUFLLEtBQUssT0FBT0Esa0JBQWtCLFlBQ3ZEN0YsSUFBSTtZQUNOLElBQUkwRztZQUNKLElBQUkvRixZQUFZNEMsT0FBTztnQkFDckIsTUFBTXFDLFFBQVFRLFdBQVcsSUFBSTtnQkFDN0IsTUFBTXNDLFFBQVFPLFlBQVkxRixNQUFNLEtBQUs7Z0JBQ3JDLElBQUlrSCxXQUFXO2dCQUNmLElBQUk7b0JBQ0YvRCxTQUFTMEQsT0FBTzFCO29CQUNoQitCLFdBQVc7Z0JBQ2IsU0FBVTtvQkFDUixJQUFJQSxVQUNGeEUsWUFBWUw7eUJBRVpNLFdBQVdOO2dCQUNmO2dCQUNBRCxrQkFBa0JDLE9BQU9DO2dCQUN6QixPQUFPWSxjQUFjQyxRQUFRZDtZQUMvQixPQUFPLElBQUksQ0FBQ3JDLFFBQVEsT0FBT0EsU0FBUyxVQUFVO2dCQUM1Q21ELFNBQVMwRCxPQUFPN0c7Z0JBQ2hCLElBQUltRCxXQUFXLEtBQUssR0FDbEJBLFNBQVNuRDtnQkFDWCxJQUFJbUQsV0FBV3BILFNBQ2JvSCxTQUFTLEtBQUs7Z0JBQ2hCLElBQUksSUFBSSxDQUFDdUIsV0FBVyxFQUNsQjFELE9BQU9tQyxRQUFRO2dCQUNqQixJQUFJYixlQUFlO29CQUNqQixNQUFNNkUsSUFBSSxFQUFFO29CQUNaLE1BQU1DLEtBQUssRUFBRTtvQkFDYjVGLFVBQVUsV0FBV2lDLDJCQUEyQixDQUFDekQsTUFBTW1ELFFBQVFnRSxHQUFHQztvQkFDbEU5RSxjQUFjNkUsR0FBR0M7Z0JBQ25CO2dCQUNBLE9BQU9qRTtZQUNULE9BQ0UxRyxJQUFJLEdBQUd1RDtRQUNYO1FBQ0EsSUFBSSxDQUFDcUgsa0JBQWtCLEdBQUcsQ0FBQ3JILE1BQU02RztZQUMvQixJQUFJLE9BQU83RyxTQUFTLFlBQVk7Z0JBQzlCLE9BQU8sQ0FBQ2xCLE9BQU8sR0FBR25DLE9BQVMsSUFBSSxDQUFDMEssa0JBQWtCLENBQUN2SSxPQUFPLENBQUNpRSxRQUFVL0MsS0FBSytDLFVBQVVwRztZQUN0RjtZQUNBLElBQUkySyxTQUFTQztZQUNiLE1BQU1wRSxTQUFTLElBQUksQ0FBQ3lELE9BQU8sQ0FBQzVHLE1BQU02RyxRQUFRLENBQUNNLEdBQUdDO2dCQUM1Q0UsVUFBVUg7Z0JBQ1ZJLGlCQUFpQkg7WUFDbkI7WUFDQSxPQUFPO2dCQUFDakU7Z0JBQVFtRTtnQkFBU0M7YUFBZTtRQUMxQztRQUNBLElBQUksT0FBT1osUUFBUWEsZUFBZSxXQUNoQyxJQUFJLENBQUNDLGFBQWEsQ0FBQ2QsT0FBT2EsVUFBVTtRQUN0QyxJQUFJLE9BQU9iLFFBQVFlLHlCQUF5QixXQUMxQyxJQUFJLENBQUNDLHVCQUF1QixDQUFDaEIsT0FBT2Usb0JBQW9CO0lBQzVEO0lBQ0FFLFlBQVk1SCxJQUFJLEVBQUU7UUFDaEIsSUFBSSxDQUFDNUMsWUFBWTRDLE9BQ2Z2RCxJQUFJO1FBQ04sSUFBSVMsUUFBUThDLE9BQ1ZBLE9BQU82SCxRQUFRN0g7UUFDakIsTUFBTXFDLFFBQVFRLFdBQVcsSUFBSTtRQUM3QixNQUFNc0MsUUFBUU8sWUFBWTFGLE1BQU0sS0FBSztRQUNyQ21GLEtBQUssQ0FBQ2hKLFlBQVksQ0FBQzJJLFNBQVMsR0FBRztRQUMvQm5DLFdBQVdOO1FBQ1gsT0FBTzhDO0lBQ1Q7SUFDQTJDLFlBQVkvRSxLQUFLLEVBQUVULGFBQWEsRUFBRTtRQUNoQyxNQUFNeEQsUUFBUWlFLFNBQVNBLEtBQUssQ0FBQzVHLFlBQVk7UUFDekMsSUFBSSxDQUFDMkMsU0FBUyxDQUFDQSxNQUFNZ0csU0FBUyxFQUM1QnJJLElBQUk7UUFDTixNQUFNLEVBQUVxSCxRQUFRekIsS0FBSyxFQUFFLEdBQUd2RDtRQUMxQnNELGtCQUFrQkMsT0FBT0M7UUFDekIsT0FBT1ksY0FBYyxLQUFLLEdBQUdiO0lBQy9CO0lBQ0E7Ozs7R0FJQyxHQUNEb0YsY0FBY3RLLEtBQUssRUFBRTtRQUNuQixJQUFJLENBQUN1SCxXQUFXLEdBQUd2SDtJQUNyQjtJQUNBOzs7O0dBSUMsR0FDRHdLLHdCQUF3QnhLLEtBQUssRUFBRTtRQUM3QixJQUFJLENBQUNzSixxQkFBcUIsR0FBR3RKO0lBQy9CO0lBQ0E0SyxhQUFhL0gsSUFBSSxFQUFFc0gsT0FBTyxFQUFFO1FBQzFCLElBQUk1RztRQUNKLElBQUtBLElBQUk0RyxRQUFRM0csTUFBTSxHQUFHLEdBQUdELEtBQUssR0FBR0EsSUFBSztZQUN4QyxNQUFNc0gsUUFBUVYsT0FBTyxDQUFDNUcsRUFBRTtZQUN4QixJQUFJc0gsTUFBTXJFLElBQUksQ0FBQ2hELE1BQU0sS0FBSyxLQUFLcUgsTUFBTUMsRUFBRSxLQUFLLFdBQVc7Z0JBQ3JEakksT0FBT2dJLE1BQU03SyxLQUFLO2dCQUNsQjtZQUNGO1FBQ0Y7UUFDQSxJQUFJdUQsSUFBSSxDQUFDLEdBQUc7WUFDVjRHLFVBQVVBLFFBQVFwSCxLQUFLLENBQUNRLElBQUk7UUFDOUI7UUFDQSxNQUFNd0gsbUJBQW1CMUcsVUFBVSxXQUFXMkcsYUFBYTtRQUMzRCxJQUFJakwsUUFBUThDLE9BQU87WUFDakIsT0FBT2tJLGlCQUFpQmxJLE1BQU1zSDtRQUNoQztRQUNBLE9BQU8sSUFBSSxDQUFDVixPQUFPLENBQ2pCNUcsTUFDQSxDQUFDK0MsUUFBVW1GLGlCQUFpQm5GLE9BQU91RTtJQUV2QztBQUNGO0FBQ0EsU0FBUzVCLFlBQVl2SSxLQUFLLEVBQUV5SCxNQUFNO0lBQ2hDLE1BQU03QixRQUFRdEYsTUFBTU4sU0FBU3FFLFVBQVUsVUFBVTRHLFNBQVMsQ0FBQ2pMLE9BQU95SCxVQUFVbEgsTUFBTVAsU0FBU3FFLFVBQVUsVUFBVTZHLFNBQVMsQ0FBQ2xMLE9BQU95SCxVQUFVRCxpQkFBaUJ4SCxPQUFPeUg7SUFDbEssTUFBTXZDLFFBQVF1QyxTQUFTQSxPQUFPZCxNQUFNLEdBQUdqQztJQUN2Q1EsTUFBTUosT0FBTyxDQUFDcUcsSUFBSSxDQUFDdkY7SUFDbkIsT0FBT0E7QUFDVDtBQUVBLHNCQUFzQjtBQUN0QixTQUFTOEUsUUFBUTFLLEtBQUs7SUFDcEIsSUFBSSxDQUFDRCxRQUFRQyxRQUNYVixJQUFJLElBQUlVO0lBQ1YsT0FBT29MLFlBQVlwTDtBQUNyQjtBQUNBLFNBQVNvTCxZQUFZcEwsS0FBSztJQUN4QixJQUFJLENBQUNDLFlBQVlELFVBQVUrRCxTQUFTL0QsUUFDbEMsT0FBT0E7SUFDVCxNQUFNMkIsUUFBUTNCLEtBQUssQ0FBQ2hCLFlBQVk7SUFDaEMsSUFBSXFNO0lBQ0osSUFBSTFKLE9BQU87UUFDVCxJQUFJLENBQUNBLE1BQU13RSxTQUFTLEVBQ2xCLE9BQU94RSxNQUFNVixLQUFLO1FBQ3BCVSxNQUFNaUYsVUFBVSxHQUFHO1FBQ25CeUUsT0FBT3pJLFlBQVk1QyxPQUFPMkIsTUFBTWdGLE1BQU0sQ0FBQzlCLE1BQU0sQ0FBQ3lFLHFCQUFxQjtJQUNyRSxPQUFPO1FBQ0wrQixPQUFPekksWUFBWTVDLE9BQU87SUFDNUI7SUFDQWtCLEtBQUttSyxNQUFNLENBQUM3SixLQUFLaUY7UUFDZnpFLElBQUlxSixNQUFNN0osS0FBSzRKLFlBQVkzRTtJQUM3QjtJQUNBLElBQUk5RSxPQUFPO1FBQ1RBLE1BQU1pRixVQUFVLEdBQUc7SUFDckI7SUFDQSxPQUFPeUU7QUFDVDtBQUVBLHlCQUF5QjtBQUN6QixTQUFTQztJQUNQLE1BQU1DLGNBQWM7SUFDcEIsSUFBSXJNLElBQXFDLEVBQUU7UUFDekNELE9BQU9rTSxJQUFJLENBQ1QsdUNBQ0EsU0FBU0wsRUFBRTtZQUNULE9BQU8sa0NBQWtDQTtRQUMzQyxHQUNBLFNBQVN0RSxJQUFJO1lBQ1gsT0FBTywrQ0FBK0NBO1FBQ3hELEdBQ0E7SUFFSjtJQUNBLE1BQU1nRixVQUFVO0lBQ2hCLE1BQU1DLE1BQU07SUFDWixNQUFNQyxTQUFTO0lBQ2YsU0FBUzNFLGlCQUFpQnBGLEtBQUssRUFBRWdLLFFBQVEsRUFBRXhCLE9BQU8sRUFBRUMsY0FBYztRQUNoRSxPQUFRekksTUFBTUMsS0FBSztZQUNqQixLQUFLLEVBQUUsVUFBVTtZQUNqQixLQUFLLEVBQUUsT0FBTztnQkFDWixPQUFPZ0ssNEJBQ0xqSyxPQUNBZ0ssVUFDQXhCLFNBQ0FDO1lBRUosS0FBSyxFQUFFLFNBQVM7Z0JBQ2QsT0FBT3lCLHFCQUFxQmxLLE9BQU9nSyxVQUFVeEIsU0FBU0M7WUFDeEQsS0FBSyxFQUFFLE9BQU87Z0JBQ1osT0FBTzBCLG1CQUNMbkssT0FDQWdLLFVBQ0F4QixTQUNBQztRQUVOO0lBQ0Y7SUFDQSxTQUFTeUIscUJBQXFCbEssS0FBSyxFQUFFZ0ssUUFBUSxFQUFFeEIsT0FBTyxFQUFFQyxjQUFjO1FBQ3BFLElBQUksRUFBRW5KLEtBQUssRUFBRW1HLFNBQVMsRUFBRSxHQUFHekY7UUFDM0IsSUFBSWdCLFFBQVFoQixNQUFNZ0IsS0FBSztRQUN2QixJQUFJQSxNQUFNYSxNQUFNLEdBQUd2QyxNQUFNdUMsTUFBTSxFQUFFOztZQUUvQixDQUFDdkMsT0FBTzBCLE1BQU0sR0FBRztnQkFBQ0E7Z0JBQU8xQjthQUFNO1lBQy9CLENBQUNrSixTQUFTQyxlQUFlLEdBQUc7Z0JBQUNBO2dCQUFnQkQ7YUFBUTtRQUN2RDtRQUNBLElBQUssSUFBSTVHLElBQUksR0FBR0EsSUFBSXRDLE1BQU11QyxNQUFNLEVBQUVELElBQUs7WUFDckMsSUFBSTZELFNBQVMsQ0FBQzdELEVBQUUsSUFBSVosS0FBSyxDQUFDWSxFQUFFLEtBQUt0QyxLQUFLLENBQUNzQyxFQUFFLEVBQUU7Z0JBQ3pDLE1BQU1pRCxPQUFPbUYsU0FBU3RFLE1BQU0sQ0FBQztvQkFBQzlEO2lCQUFFO2dCQUNoQzRHLFFBQVFnQixJQUFJLENBQUM7b0JBQ1hMLElBQUlVO29CQUNKaEY7b0JBQ0Esa0VBQWtFO29CQUNsRSwrREFBK0Q7b0JBQy9EeEcsT0FBTytMLHdCQUF3QnBKLEtBQUssQ0FBQ1ksRUFBRTtnQkFDekM7Z0JBQ0E2RyxlQUFlZSxJQUFJLENBQUM7b0JBQ2xCTCxJQUFJVTtvQkFDSmhGO29CQUNBeEcsT0FBTytMLHdCQUF3QjlLLEtBQUssQ0FBQ3NDLEVBQUU7Z0JBQ3pDO1lBQ0Y7UUFDRjtRQUNBLElBQUssSUFBSUEsSUFBSXRDLE1BQU11QyxNQUFNLEVBQUVELElBQUlaLE1BQU1hLE1BQU0sRUFBRUQsSUFBSztZQUNoRCxNQUFNaUQsT0FBT21GLFNBQVN0RSxNQUFNLENBQUM7Z0JBQUM5RDthQUFFO1lBQ2hDNEcsUUFBUWdCLElBQUksQ0FBQztnQkFDWEwsSUFBSVc7Z0JBQ0pqRjtnQkFDQSxrRUFBa0U7Z0JBQ2xFLCtEQUErRDtnQkFDL0R4RyxPQUFPK0wsd0JBQXdCcEosS0FBSyxDQUFDWSxFQUFFO1lBQ3pDO1FBQ0Y7UUFDQSxJQUFLLElBQUlBLElBQUlaLE1BQU1hLE1BQU0sR0FBRyxHQUFHdkMsTUFBTXVDLE1BQU0sSUFBSUQsR0FBRyxFQUFFQSxFQUFHO1lBQ3JELE1BQU1pRCxPQUFPbUYsU0FBU3RFLE1BQU0sQ0FBQztnQkFBQzlEO2FBQUU7WUFDaEM2RyxlQUFlZSxJQUFJLENBQUM7Z0JBQ2xCTCxJQUFJWTtnQkFDSmxGO1lBQ0Y7UUFDRjtJQUNGO0lBQ0EsU0FBU29GLDRCQUE0QmpLLEtBQUssRUFBRWdLLFFBQVEsRUFBRXhCLE9BQU8sRUFBRUMsY0FBYztRQUMzRSxNQUFNLEVBQUVuSixLQUFLLEVBQUUwQixLQUFLLEVBQUUsR0FBR2hCO1FBQ3pCVCxLQUFLUyxNQUFNeUYsU0FBUyxFQUFFLENBQUM1RixLQUFLd0s7WUFDMUIsTUFBTUMsWUFBWWxLLElBQUlkLE9BQU9PO1lBQzdCLE1BQU14QixRQUFRK0IsSUFBSVksT0FBT25CO1lBQ3pCLE1BQU1zSixLQUFLLENBQUNrQixnQkFBZ0JOLFNBQVM3SixJQUFJWixPQUFPTyxPQUFPZ0ssVUFBVUM7WUFDakUsSUFBSVEsY0FBY2pNLFNBQVM4SyxPQUFPVSxTQUNoQztZQUNGLE1BQU1oRixPQUFPbUYsU0FBU3RFLE1BQU0sQ0FBQzdGO1lBQzdCMkksUUFBUWdCLElBQUksQ0FBQ0wsT0FBT1ksU0FBUztnQkFBRVo7Z0JBQUl0RTtZQUFLLElBQUk7Z0JBQUVzRTtnQkFBSXRFO2dCQUFNeEc7WUFBTTtZQUM5RG9LLGVBQWVlLElBQUksQ0FDakJMLE9BQU9XLE1BQU07Z0JBQUVYLElBQUlZO2dCQUFRbEY7WUFBSyxJQUFJc0UsT0FBT1ksU0FBUztnQkFBRVosSUFBSVc7Z0JBQUtqRjtnQkFBTXhHLE9BQU8rTCx3QkFBd0JFO1lBQVcsSUFBSTtnQkFBRW5CLElBQUlVO2dCQUFTaEY7Z0JBQU14RyxPQUFPK0wsd0JBQXdCRTtZQUFXO1FBRXRMO0lBQ0Y7SUFDQSxTQUFTSCxtQkFBbUJuSyxLQUFLLEVBQUVnSyxRQUFRLEVBQUV4QixPQUFPLEVBQUVDLGNBQWM7UUFDbEUsSUFBSSxFQUFFbkosS0FBSyxFQUFFMEIsS0FBSyxFQUFFLEdBQUdoQjtRQUN2QixJQUFJNEIsSUFBSTtRQUNSdEMsTUFBTU0sT0FBTyxDQUFDLENBQUN2QjtZQUNiLElBQUksQ0FBQzJDLE1BQU1kLEdBQUcsQ0FBQzdCLFFBQVE7Z0JBQ3JCLE1BQU13RyxPQUFPbUYsU0FBU3RFLE1BQU0sQ0FBQztvQkFBQzlEO2lCQUFFO2dCQUNoQzRHLFFBQVFnQixJQUFJLENBQUM7b0JBQ1hMLElBQUlZO29CQUNKbEY7b0JBQ0F4RztnQkFDRjtnQkFDQW9LLGVBQWU4QixPQUFPLENBQUM7b0JBQ3JCcEIsSUFBSVc7b0JBQ0pqRjtvQkFDQXhHO2dCQUNGO1lBQ0Y7WUFDQXVEO1FBQ0Y7UUFDQUEsSUFBSTtRQUNKWixNQUFNcEIsT0FBTyxDQUFDLENBQUN2QjtZQUNiLElBQUksQ0FBQ2lCLE1BQU1ZLEdBQUcsQ0FBQzdCLFFBQVE7Z0JBQ3JCLE1BQU13RyxPQUFPbUYsU0FBU3RFLE1BQU0sQ0FBQztvQkFBQzlEO2lCQUFFO2dCQUNoQzRHLFFBQVFnQixJQUFJLENBQUM7b0JBQ1hMLElBQUlXO29CQUNKakY7b0JBQ0F4RztnQkFDRjtnQkFDQW9LLGVBQWU4QixPQUFPLENBQUM7b0JBQ3JCcEIsSUFBSVk7b0JBQ0psRjtvQkFDQXhHO2dCQUNGO1lBQ0Y7WUFDQXVEO1FBQ0Y7SUFDRjtJQUNBLFNBQVMrQyw0QkFBNEI2RixTQUFTLEVBQUVDLFdBQVcsRUFBRWpDLE9BQU8sRUFBRUMsY0FBYztRQUNsRkQsUUFBUWdCLElBQUksQ0FBQztZQUNYTCxJQUFJVTtZQUNKaEYsTUFBTSxFQUFFO1lBQ1J4RyxPQUFPb00sZ0JBQWdCeE4sVUFBVSxLQUFLLElBQUl3TjtRQUM1QztRQUNBaEMsZUFBZWUsSUFBSSxDQUFDO1lBQ2xCTCxJQUFJVTtZQUNKaEYsTUFBTSxFQUFFO1lBQ1J4RyxPQUFPbU07UUFDVDtJQUNGO0lBQ0EsU0FBU25CLGNBQWNwRixLQUFLLEVBQUV1RSxPQUFPO1FBQ25DQSxRQUFRNUksT0FBTyxDQUFDLENBQUNzSjtZQUNmLE1BQU0sRUFBRXJFLElBQUksRUFBRXNFLEVBQUUsRUFBRSxHQUFHRDtZQUNyQixJQUFJaEksT0FBTytDO1lBQ1gsSUFBSyxJQUFJckMsSUFBSSxHQUFHQSxJQUFJaUQsS0FBS2hELE1BQU0sR0FBRyxHQUFHRCxJQUFLO2dCQUN4QyxNQUFNOEksYUFBYWhMLFlBQVl3QjtnQkFDL0IsSUFBSW1ILElBQUl4RCxJQUFJLENBQUNqRCxFQUFFO2dCQUNmLElBQUksT0FBT3lHLE1BQU0sWUFBWSxPQUFPQSxNQUFNLFVBQVU7b0JBQ2xEQSxJQUFJLEtBQUtBO2dCQUNYO2dCQUNBLElBQUksQ0FBQ3FDLGVBQWUsRUFBRSxVQUFVLE9BQU1BLGVBQWUsRUFBRSxTQUFTLEdBQVgsS0FBbUJyQyxDQUFBQSxNQUFNLGVBQWVBLE1BQU0sYUFBWSxHQUM3RzFLLElBQUlpTSxjQUFjO2dCQUNwQixJQUFJLE9BQU8xSSxTQUFTLGNBQWNtSCxNQUFNLGFBQ3RDMUssSUFBSWlNLGNBQWM7Z0JBQ3BCMUksT0FBT2QsSUFBSWMsTUFBTW1IO2dCQUNqQixJQUFJLE9BQU9uSCxTQUFTLFVBQ2xCdkQsSUFBSWlNLGNBQWMsR0FBRy9FLEtBQUs4RixJQUFJLENBQUM7WUFDbkM7WUFDQSxNQUFNQyxPQUFPbEwsWUFBWXdCO1lBQ3pCLE1BQU03QyxRQUFRd00sb0JBQW9CM0IsTUFBTTdLLEtBQUs7WUFDN0MsTUFBTXdCLE1BQU1nRixJQUFJLENBQUNBLEtBQUtoRCxNQUFNLEdBQUcsRUFBRTtZQUNqQyxPQUFRc0g7Z0JBQ04sS0FBS1U7b0JBQ0gsT0FBUWU7d0JBQ04sS0FBSyxFQUFFLE9BQU87NEJBQ1osT0FBTzFKLEtBQUtiLEdBQUcsQ0FBQ1IsS0FBS3hCO3dCQUN2QixLQUFLLEVBQUUsT0FBTzs0QkFDWlYsSUFBSWlNO3dCQUNOOzRCQUNFLE9BQU8xSSxJQUFJLENBQUNyQixJQUFJLEdBQUd4QjtvQkFDdkI7Z0JBQ0YsS0FBS3lMO29CQUNILE9BQVFjO3dCQUNOLEtBQUssRUFBRSxTQUFTOzRCQUNkLE9BQU8vSyxRQUFRLE1BQU1xQixLQUFLc0ksSUFBSSxDQUFDbkwsU0FBUzZDLEtBQUs0SixNQUFNLENBQUNqTCxLQUFLLEdBQUd4Qjt3QkFDOUQsS0FBSyxFQUFFLE9BQU87NEJBQ1osT0FBTzZDLEtBQUtiLEdBQUcsQ0FBQ1IsS0FBS3hCO3dCQUN2QixLQUFLLEVBQUUsT0FBTzs0QkFDWixPQUFPNkMsS0FBS1YsR0FBRyxDQUFDbkM7d0JBQ2xCOzRCQUNFLE9BQU82QyxJQUFJLENBQUNyQixJQUFJLEdBQUd4QjtvQkFDdkI7Z0JBQ0YsS0FBSzBMO29CQUNILE9BQVFhO3dCQUNOLEtBQUssRUFBRSxTQUFTOzRCQUNkLE9BQU8xSixLQUFLNEosTUFBTSxDQUFDakwsS0FBSzt3QkFDMUIsS0FBSyxFQUFFLE9BQU87NEJBQ1osT0FBT3FCLEtBQUtvQixNQUFNLENBQUN6Qzt3QkFDckIsS0FBSyxFQUFFLE9BQU87NEJBQ1osT0FBT3FCLEtBQUtvQixNQUFNLENBQUM0RyxNQUFNN0ssS0FBSzt3QkFDaEM7NEJBQ0UsT0FBTyxPQUFPNkMsSUFBSSxDQUFDckIsSUFBSTtvQkFDM0I7Z0JBQ0Y7b0JBQ0VsQyxJQUFJaU0sY0FBYyxHQUFHVDtZQUN6QjtRQUNGO1FBQ0EsT0FBT2xGO0lBQ1Q7SUFDQSxTQUFTNEcsb0JBQW9CckwsR0FBRztRQUM5QixJQUFJLENBQUNsQixZQUFZa0IsTUFDZixPQUFPQTtRQUNULElBQUloQixNQUFNQyxPQUFPLENBQUNlLE1BQ2hCLE9BQU9BLElBQUl1TCxHQUFHLENBQUNGO1FBQ2pCLElBQUlsTSxNQUFNYSxNQUNSLE9BQU8sSUFBSXFCLElBQ1RyQyxNQUFNd00sSUFBSSxDQUFDeEwsSUFBSUcsT0FBTyxJQUFJb0wsR0FBRyxDQUFDLENBQUMsQ0FBQ0UsR0FBR0MsRUFBRSxHQUFLO2dCQUFDRDtnQkFBR0osb0JBQW9CSzthQUFHO1FBRXpFLElBQUl0TSxNQUFNWSxNQUNSLE9BQU8sSUFBSXNCLElBQUl0QyxNQUFNd00sSUFBSSxDQUFDeEwsS0FBS3VMLEdBQUcsQ0FBQ0Y7UUFDckMsTUFBTU0sU0FBU2hOLE9BQU9rRCxNQUFNLENBQUNuRCxlQUFlc0I7UUFDNUMsSUFBSyxNQUFNSyxPQUFPTCxJQUNoQjJMLE1BQU0sQ0FBQ3RMLElBQUksR0FBR2dMLG9CQUFvQnJMLEdBQUcsQ0FBQ0ssSUFBSTtRQUM1QyxJQUFJSyxJQUFJVixLQUFLcEMsWUFDWCtOLE1BQU0sQ0FBQy9OLFVBQVUsR0FBR29DLEdBQUcsQ0FBQ3BDLFVBQVU7UUFDcEMsT0FBTytOO0lBQ1Q7SUFDQSxTQUFTZix3QkFBd0I1SyxHQUFHO1FBQ2xDLElBQUlwQixRQUFRb0IsTUFBTTtZQUNoQixPQUFPcUwsb0JBQW9Cckw7UUFDN0IsT0FDRSxPQUFPQTtJQUNYO0lBQ0FvRCxXQUFXLFdBQVc7UUFDcEJ5RztRQUNBakU7UUFDQVQ7SUFDRjtBQUNGO0FBRUEsd0JBQXdCO0FBQ3hCLFNBQVN5RztJQUNQLE1BQU1DLGlCQUFpQnhLO1FBQ3JCbkMsWUFBWWtDLE1BQU0sRUFBRWtGLE1BQU0sQ0FBRTtZQUMxQixLQUFLO1lBQ0wsSUFBSSxDQUFDekksWUFBWSxHQUFHO2dCQUNsQjRDLE9BQU8sRUFBRSxPQUFPO2dCQUNoQmdELFNBQVM2QztnQkFDVGQsUUFBUWMsU0FBU0EsT0FBT2QsTUFBTSxHQUFHakM7Z0JBQ2pDeUIsV0FBVztnQkFDWFMsWUFBWTtnQkFDWmpFLE9BQU8sS0FBSztnQkFDWnlFLFdBQVcsS0FBSztnQkFDaEJuRyxPQUFPc0I7Z0JBQ1BtRixRQUFRLElBQUk7Z0JBQ1pDLFdBQVc7Z0JBQ1g3QixVQUFVO1lBQ1o7UUFDRjtRQUNBLElBQUltSCxPQUFPO1lBQ1QsT0FBT3ZLLE9BQU8sSUFBSSxDQUFDMUQsWUFBWSxFQUFFaU8sSUFBSTtRQUN2QztRQUNBcEwsSUFBSUwsR0FBRyxFQUFFO1lBQ1AsT0FBT2tCLE9BQU8sSUFBSSxDQUFDMUQsWUFBWSxFQUFFNkMsR0FBRyxDQUFDTDtRQUN2QztRQUNBUSxJQUFJUixHQUFHLEVBQUV4QixLQUFLLEVBQUU7WUFDZCxNQUFNMkIsUUFBUSxJQUFJLENBQUMzQyxZQUFZO1lBQy9Ca08sZ0JBQWdCdkw7WUFDaEIsSUFBSSxDQUFDZSxPQUFPZixPQUFPRSxHQUFHLENBQUNMLFFBQVFrQixPQUFPZixPQUFPSSxHQUFHLENBQUNQLFNBQVN4QixPQUFPO2dCQUMvRG1OLGVBQWV4TDtnQkFDZmdILFlBQVloSDtnQkFDWkEsTUFBTXlGLFNBQVMsQ0FBQ3BGLEdBQUcsQ0FBQ1IsS0FBSztnQkFDekJHLE1BQU1nQixLQUFLLENBQUNYLEdBQUcsQ0FBQ1IsS0FBS3hCO2dCQUNyQjJCLE1BQU15RixTQUFTLENBQUNwRixHQUFHLENBQUNSLEtBQUs7WUFDM0I7WUFDQSxPQUFPLElBQUk7UUFDYjtRQUNBeUMsT0FBT3pDLEdBQUcsRUFBRTtZQUNWLElBQUksQ0FBQyxJQUFJLENBQUNLLEdBQUcsQ0FBQ0wsTUFBTTtnQkFDbEIsT0FBTztZQUNUO1lBQ0EsTUFBTUcsUUFBUSxJQUFJLENBQUMzQyxZQUFZO1lBQy9Ca08sZ0JBQWdCdkw7WUFDaEJ3TCxlQUFleEw7WUFDZmdILFlBQVloSDtZQUNaLElBQUlBLE1BQU1WLEtBQUssQ0FBQ1ksR0FBRyxDQUFDTCxNQUFNO2dCQUN4QkcsTUFBTXlGLFNBQVMsQ0FBQ3BGLEdBQUcsQ0FBQ1IsS0FBSztZQUMzQixPQUFPO2dCQUNMRyxNQUFNeUYsU0FBUyxDQUFDbkQsTUFBTSxDQUFDekM7WUFDekI7WUFDQUcsTUFBTWdCLEtBQUssQ0FBQ3NCLE1BQU0sQ0FBQ3pDO1lBQ25CLE9BQU87UUFDVDtRQUNBd0MsUUFBUTtZQUNOLE1BQU1yQyxRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQixJQUFJZSxPQUFPZixPQUFPc0wsSUFBSSxFQUFFO2dCQUN0QkUsZUFBZXhMO2dCQUNmZ0gsWUFBWWhIO2dCQUNaQSxNQUFNeUYsU0FBUyxHQUFHLGFBQWEsR0FBRyxJQUFJNUU7Z0JBQ3RDdEIsS0FBS1MsTUFBTVYsS0FBSyxFQUFFLENBQUNPO29CQUNqQkcsTUFBTXlGLFNBQVMsQ0FBQ3BGLEdBQUcsQ0FBQ1IsS0FBSztnQkFDM0I7Z0JBQ0FHLE1BQU1nQixLQUFLLENBQUNxQixLQUFLO1lBQ25CO1FBQ0Y7UUFDQXpDLFFBQVE2TCxFQUFFLEVBQUVDLE9BQU8sRUFBRTtZQUNuQixNQUFNMUwsUUFBUSxJQUFJLENBQUMzQyxZQUFZO1lBQy9CMEQsT0FBT2YsT0FBT0osT0FBTyxDQUFDLENBQUMrTCxRQUFROUwsS0FBSytMO2dCQUNsQ0gsR0FBR3RNLElBQUksQ0FBQ3VNLFNBQVMsSUFBSSxDQUFDdEwsR0FBRyxDQUFDUCxNQUFNQSxLQUFLLElBQUk7WUFDM0M7UUFDRjtRQUNBTyxJQUFJUCxHQUFHLEVBQUU7WUFDUCxNQUFNRyxRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQixNQUFNM0IsUUFBUTBDLE9BQU9mLE9BQU9JLEdBQUcsQ0FBQ1A7WUFDaEMsSUFBSUcsTUFBTWlGLFVBQVUsSUFBSSxDQUFDM0csWUFBWUQsUUFBUTtnQkFDM0MsT0FBT0E7WUFDVDtZQUNBLElBQUlBLFVBQVUyQixNQUFNVixLQUFLLENBQUNjLEdBQUcsQ0FBQ1AsTUFBTTtnQkFDbEMsT0FBT3hCO1lBQ1Q7WUFDQSxNQUFNNEYsUUFBUTJDLFlBQVl2SSxPQUFPMkI7WUFDakN3TCxlQUFleEw7WUFDZkEsTUFBTWdCLEtBQUssQ0FBQ1gsR0FBRyxDQUFDUixLQUFLb0U7WUFDckIsT0FBT0E7UUFDVDtRQUNBeEMsT0FBTztZQUNMLE9BQU9WLE9BQU8sSUFBSSxDQUFDMUQsWUFBWSxFQUFFb0UsSUFBSTtRQUN2QztRQUNBb0ssU0FBUztZQUNQLE1BQU1DLFdBQVcsSUFBSSxDQUFDckssSUFBSTtZQUMxQixPQUFPO2dCQUNMLENBQUN2RSxPQUFPNE8sUUFBUSxDQUFDLEVBQUUsSUFBTSxJQUFJLENBQUNELE1BQU07Z0JBQ3BDRSxNQUFNO29CQUNKLE1BQU1DLElBQUlGLFNBQVNDLElBQUk7b0JBQ3ZCLElBQUlDLEVBQUVDLElBQUksRUFDUixPQUFPRDtvQkFDVCxNQUFNM04sUUFBUSxJQUFJLENBQUMrQixHQUFHLENBQUM0TCxFQUFFM04sS0FBSztvQkFDOUIsT0FBTzt3QkFDTDROLE1BQU07d0JBQ041TjtvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7UUFDQXNCLFVBQVU7WUFDUixNQUFNbU0sV0FBVyxJQUFJLENBQUNySyxJQUFJO1lBQzFCLE9BQU87Z0JBQ0wsQ0FBQ3ZFLE9BQU80TyxRQUFRLENBQUMsRUFBRSxJQUFNLElBQUksQ0FBQ25NLE9BQU87Z0JBQ3JDb00sTUFBTTtvQkFDSixNQUFNQyxJQUFJRixTQUFTQyxJQUFJO29CQUN2QixJQUFJQyxFQUFFQyxJQUFJLEVBQ1IsT0FBT0Q7b0JBQ1QsTUFBTTNOLFFBQVEsSUFBSSxDQUFDK0IsR0FBRyxDQUFDNEwsRUFBRTNOLEtBQUs7b0JBQzlCLE9BQU87d0JBQ0w0TixNQUFNO3dCQUNONU4sT0FBTzs0QkFBQzJOLEVBQUUzTixLQUFLOzRCQUFFQTt5QkFBTTtvQkFDekI7Z0JBQ0Y7WUFDRjtRQUNGO1FBQ0EsQ0FBRWhCLENBQUFBLGFBQWFILE9BQU80TyxRQUFRLEVBQUUsR0FBRztZQUNqQyxPQUFPLElBQUksQ0FBQ25NLE9BQU87UUFDckI7SUFDRjtJQUNBLFNBQVMySixVQUFVMUksTUFBTSxFQUFFa0YsTUFBTTtRQUMvQixPQUFPLElBQUl1RixTQUFTekssUUFBUWtGO0lBQzlCO0lBQ0EsU0FBUzBGLGVBQWV4TCxLQUFLO1FBQzNCLElBQUksQ0FBQ0EsTUFBTWdCLEtBQUssRUFBRTtZQUNoQmhCLE1BQU15RixTQUFTLEdBQUcsYUFBYSxHQUFHLElBQUk1RTtZQUN0Q2IsTUFBTWdCLEtBQUssR0FBRyxJQUFJSCxJQUFJYixNQUFNVixLQUFLO1FBQ25DO0lBQ0Y7SUFDQSxNQUFNNE0saUJBQWlCcEw7UUFDckJwQyxZQUFZa0MsTUFBTSxFQUFFa0YsTUFBTSxDQUFFO1lBQzFCLEtBQUs7WUFDTCxJQUFJLENBQUN6SSxZQUFZLEdBQUc7Z0JBQ2xCNEMsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCZ0QsU0FBUzZDO2dCQUNUZCxRQUFRYyxTQUFTQSxPQUFPZCxNQUFNLEdBQUdqQztnQkFDakN5QixXQUFXO2dCQUNYUyxZQUFZO2dCQUNaakUsT0FBTyxLQUFLO2dCQUNaMUIsT0FBT3NCO2dCQUNQbUYsUUFBUSxJQUFJO2dCQUNaNUMsU0FBUyxhQUFhLEdBQUcsSUFBSXRDO2dCQUM3QnNELFVBQVU7Z0JBQ1Y2QixXQUFXO1lBQ2I7UUFDRjtRQUNBLElBQUlzRixPQUFPO1lBQ1QsT0FBT3ZLLE9BQU8sSUFBSSxDQUFDMUQsWUFBWSxFQUFFaU8sSUFBSTtRQUN2QztRQUNBcEwsSUFBSTdCLEtBQUssRUFBRTtZQUNULE1BQU0yQixRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQixJQUFJLENBQUNBLE1BQU1nQixLQUFLLEVBQUU7Z0JBQ2hCLE9BQU9oQixNQUFNVixLQUFLLENBQUNZLEdBQUcsQ0FBQzdCO1lBQ3pCO1lBQ0EsSUFBSTJCLE1BQU1nQixLQUFLLENBQUNkLEdBQUcsQ0FBQzdCLFFBQ2xCLE9BQU87WUFDVCxJQUFJMkIsTUFBTW1ELE9BQU8sQ0FBQ2pELEdBQUcsQ0FBQzdCLFVBQVUyQixNQUFNZ0IsS0FBSyxDQUFDZCxHQUFHLENBQUNGLE1BQU1tRCxPQUFPLENBQUMvQyxHQUFHLENBQUMvQixTQUNoRSxPQUFPO1lBQ1QsT0FBTztRQUNUO1FBQ0FtQyxJQUFJbkMsS0FBSyxFQUFFO1lBQ1QsTUFBTTJCLFFBQVEsSUFBSSxDQUFDM0MsWUFBWTtZQUMvQmtPLGdCQUFnQnZMO1lBQ2hCLElBQUksQ0FBQyxJQUFJLENBQUNFLEdBQUcsQ0FBQzdCLFFBQVE7Z0JBQ3BCOE4sZUFBZW5NO2dCQUNmZ0gsWUFBWWhIO2dCQUNaQSxNQUFNZ0IsS0FBSyxDQUFDUixHQUFHLENBQUNuQztZQUNsQjtZQUNBLE9BQU8sSUFBSTtRQUNiO1FBQ0FpRSxPQUFPakUsS0FBSyxFQUFFO1lBQ1osSUFBSSxDQUFDLElBQUksQ0FBQzZCLEdBQUcsQ0FBQzdCLFFBQVE7Z0JBQ3BCLE9BQU87WUFDVDtZQUNBLE1BQU0yQixRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQm1NLGVBQWVuTTtZQUNmZ0gsWUFBWWhIO1lBQ1osT0FBT0EsTUFBTWdCLEtBQUssQ0FBQ3NCLE1BQU0sQ0FBQ2pFLFVBQVcyQixDQUFBQSxNQUFNbUQsT0FBTyxDQUFDakQsR0FBRyxDQUFDN0IsU0FBUzJCLE1BQU1nQixLQUFLLENBQUNzQixNQUFNLENBQUN0QyxNQUFNbUQsT0FBTyxDQUFDL0MsR0FBRyxDQUFDL0IsVUFDbkcsd0JBQXdCLEdBQ3hCLEtBQ0Y7UUFDRjtRQUNBZ0UsUUFBUTtZQUNOLE1BQU1yQyxRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQixJQUFJZSxPQUFPZixPQUFPc0wsSUFBSSxFQUFFO2dCQUN0QmEsZUFBZW5NO2dCQUNmZ0gsWUFBWWhIO2dCQUNaQSxNQUFNZ0IsS0FBSyxDQUFDcUIsS0FBSztZQUNuQjtRQUNGO1FBQ0F3SixTQUFTO1lBQ1AsTUFBTTdMLFFBQVEsSUFBSSxDQUFDM0MsWUFBWTtZQUMvQmtPLGdCQUFnQnZMO1lBQ2hCbU0sZUFBZW5NO1lBQ2YsT0FBT0EsTUFBTWdCLEtBQUssQ0FBQzZLLE1BQU07UUFDM0I7UUFDQWxNLFVBQVU7WUFDUixNQUFNSyxRQUFRLElBQUksQ0FBQzNDLFlBQVk7WUFDL0JrTyxnQkFBZ0J2TDtZQUNoQm1NLGVBQWVuTTtZQUNmLE9BQU9BLE1BQU1nQixLQUFLLENBQUNyQixPQUFPO1FBQzVCO1FBQ0E4QixPQUFPO1lBQ0wsT0FBTyxJQUFJLENBQUNvSyxNQUFNO1FBQ3BCO1FBQ0EsQ0FBRXhPLENBQUFBLGFBQWFILE9BQU80TyxRQUFRLEVBQUUsR0FBRztZQUNqQyxPQUFPLElBQUksQ0FBQ0QsTUFBTTtRQUNwQjtRQUNBak0sUUFBUTZMLEVBQUUsRUFBRUMsT0FBTyxFQUFFO1lBQ25CLE1BQU1JLFdBQVcsSUFBSSxDQUFDRCxNQUFNO1lBQzVCLElBQUl4SCxTQUFTeUgsU0FBU0MsSUFBSTtZQUMxQixNQUFPLENBQUMxSCxPQUFPNEgsSUFBSSxDQUFFO2dCQUNuQlIsR0FBR3RNLElBQUksQ0FBQ3VNLFNBQVNySCxPQUFPaEcsS0FBSyxFQUFFZ0csT0FBT2hHLEtBQUssRUFBRSxJQUFJO2dCQUNqRGdHLFNBQVN5SCxTQUFTQyxJQUFJO1lBQ3hCO1FBQ0Y7SUFDRjtJQUNBLFNBQVN4QyxVQUFVM0ksTUFBTSxFQUFFa0YsTUFBTTtRQUMvQixPQUFPLElBQUlvRyxTQUFTdEwsUUFBUWtGO0lBQzlCO0lBQ0EsU0FBU3FHLGVBQWVuTSxLQUFLO1FBQzNCLElBQUksQ0FBQ0EsTUFBTWdCLEtBQUssRUFBRTtZQUNoQmhCLE1BQU1nQixLQUFLLEdBQUcsYUFBYSxHQUFHLElBQUlGO1lBQ2xDZCxNQUFNVixLQUFLLENBQUNNLE9BQU8sQ0FBQyxDQUFDdkI7Z0JBQ25CLElBQUlDLFlBQVlELFFBQVE7b0JBQ3RCLE1BQU00RixRQUFRMkMsWUFBWXZJLE9BQU8yQjtvQkFDakNBLE1BQU1tRCxPQUFPLENBQUM5QyxHQUFHLENBQUNoQyxPQUFPNEY7b0JBQ3pCakUsTUFBTWdCLEtBQUssQ0FBQ1IsR0FBRyxDQUFDeUQ7Z0JBQ2xCLE9BQU87b0JBQ0xqRSxNQUFNZ0IsS0FBSyxDQUFDUixHQUFHLENBQUNuQztnQkFDbEI7WUFDRjtRQUNGO0lBQ0Y7SUFDQSxTQUFTa04sZ0JBQWdCdkwsS0FBSztRQUM1QixJQUFJQSxNQUFNbUUsUUFBUSxFQUNoQnhHLElBQUksR0FBR3lPLEtBQUtDLFNBQVMsQ0FBQ3RMLE9BQU9mO0lBQ2pDO0lBQ0E0QyxXQUFXLFVBQVU7UUFBRTBHO1FBQVdDO0lBQVU7QUFDOUM7QUFFQSxlQUFlO0FBQ2YsSUFBSStDLFFBQVEsSUFBSTFFO0FBQ2hCLElBQUlFLFVBQVV3RSxNQUFNeEUsT0FBTztBQUMzQixJQUFJUyxxQkFBcUIrRCxNQUFNL0Qsa0JBQWtCLENBQUNnRSxJQUFJLENBQ3BERDtBQUVGLElBQUkzRCxnQkFBZ0IyRCxNQUFNM0QsYUFBYSxDQUFDNEQsSUFBSSxDQUFDRDtBQUM3QyxJQUFJekQsMEJBQTBCeUQsTUFBTXpELHVCQUF1QixDQUFDMEQsSUFBSSxDQUFDRDtBQUNqRSxJQUFJckQsZUFBZXFELE1BQU1yRCxZQUFZLENBQUNzRCxJQUFJLENBQUNEO0FBQzNDLElBQUl4RCxjQUFjd0QsTUFBTXhELFdBQVcsQ0FBQ3lELElBQUksQ0FBQ0Q7QUFDekMsSUFBSXRELGNBQWNzRCxNQUFNdEQsV0FBVyxDQUFDdUQsSUFBSSxDQUFDRDtBQUN6QyxTQUFTRSxVQUFVbk8sS0FBSztJQUN0QixPQUFPQTtBQUNUO0FBQ0EsU0FBU29PLGNBQWNwTyxLQUFLO0lBQzFCLE9BQU9BO0FBQ1Q7QUFxQkUsQ0FDRixrQ0FBa0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbmVwb2ludC13ZWIvLi9ub2RlX21vZHVsZXMvaW1tZXIvZGlzdC9pbW1lci5tanM/YTRmNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvZW52LnRzXG52YXIgTk9USElORyA9IFN5bWJvbC5mb3IoXCJpbW1lci1ub3RoaW5nXCIpO1xudmFyIERSQUZUQUJMRSA9IFN5bWJvbC5mb3IoXCJpbW1lci1kcmFmdGFibGVcIik7XG52YXIgRFJBRlRfU1RBVEUgPSBTeW1ib2wuZm9yKFwiaW1tZXItc3RhdGVcIik7XG5cbi8vIHNyYy91dGlscy9lcnJvcnMudHNcbnZhciBlcnJvcnMgPSBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgPyBbXG4gIC8vIEFsbCBlcnJvciBjb2Rlcywgc3RhcnRpbmcgYnkgMDpcbiAgZnVuY3Rpb24ocGx1Z2luKSB7XG4gICAgcmV0dXJuIGBUaGUgcGx1Z2luIGZvciAnJHtwbHVnaW59JyBoYXMgbm90IGJlZW4gbG9hZGVkIGludG8gSW1tZXIuIFRvIGVuYWJsZSB0aGUgcGx1Z2luLCBpbXBvcnQgYW5kIGNhbGwgXFxgZW5hYmxlJHtwbHVnaW59KClcXGAgd2hlbiBpbml0aWFsaXppbmcgeW91ciBhcHBsaWNhdGlvbi5gO1xuICB9LFxuICBmdW5jdGlvbih0aGluZykge1xuICAgIHJldHVybiBgcHJvZHVjZSBjYW4gb25seSBiZSBjYWxsZWQgb24gdGhpbmdzIHRoYXQgYXJlIGRyYWZ0YWJsZTogcGxhaW4gb2JqZWN0cywgYXJyYXlzLCBNYXAsIFNldCBvciBjbGFzc2VzIHRoYXQgYXJlIG1hcmtlZCB3aXRoICdbaW1tZXJhYmxlXTogdHJ1ZScuIEdvdCAnJHt0aGluZ30nYDtcbiAgfSxcbiAgXCJUaGlzIG9iamVjdCBoYXMgYmVlbiBmcm96ZW4gYW5kIHNob3VsZCBub3QgYmUgbXV0YXRlZFwiLFxuICBmdW5jdGlvbihkYXRhKSB7XG4gICAgcmV0dXJuIFwiQ2Fubm90IHVzZSBhIHByb3h5IHRoYXQgaGFzIGJlZW4gcmV2b2tlZC4gRGlkIHlvdSBwYXNzIGFuIG9iamVjdCBmcm9tIGluc2lkZSBhbiBpbW1lciBmdW5jdGlvbiB0byBhbiBhc3luYyBwcm9jZXNzPyBcIiArIGRhdGE7XG4gIH0sXG4gIFwiQW4gaW1tZXIgcHJvZHVjZXIgcmV0dXJuZWQgYSBuZXcgdmFsdWUgKmFuZCogbW9kaWZpZWQgaXRzIGRyYWZ0LiBFaXRoZXIgcmV0dXJuIGEgbmV3IHZhbHVlICpvciogbW9kaWZ5IHRoZSBkcmFmdC5cIixcbiAgXCJJbW1lciBmb3JiaWRzIGNpcmN1bGFyIHJlZmVyZW5jZXNcIixcbiAgXCJUaGUgZmlyc3Qgb3Igc2Vjb25kIGFyZ3VtZW50IHRvIGBwcm9kdWNlYCBtdXN0IGJlIGEgZnVuY3Rpb25cIixcbiAgXCJUaGUgdGhpcmQgYXJndW1lbnQgdG8gYHByb2R1Y2VgIG11c3QgYmUgYSBmdW5jdGlvbiBvciB1bmRlZmluZWRcIixcbiAgXCJGaXJzdCBhcmd1bWVudCB0byBgY3JlYXRlRHJhZnRgIG11c3QgYmUgYSBwbGFpbiBvYmplY3QsIGFuIGFycmF5LCBvciBhbiBpbW1lcmFibGUgb2JqZWN0XCIsXG4gIFwiRmlyc3QgYXJndW1lbnQgdG8gYGZpbmlzaERyYWZ0YCBtdXN0IGJlIGEgZHJhZnQgcmV0dXJuZWQgYnkgYGNyZWF0ZURyYWZ0YFwiLFxuICBmdW5jdGlvbih0aGluZykge1xuICAgIHJldHVybiBgJ2N1cnJlbnQnIGV4cGVjdHMgYSBkcmFmdCwgZ290OiAke3RoaW5nfWA7XG4gIH0sXG4gIFwiT2JqZWN0LmRlZmluZVByb3BlcnR5KCkgY2Fubm90IGJlIHVzZWQgb24gYW4gSW1tZXIgZHJhZnRcIixcbiAgXCJPYmplY3Quc2V0UHJvdG90eXBlT2YoKSBjYW5ub3QgYmUgdXNlZCBvbiBhbiBJbW1lciBkcmFmdFwiLFxuICBcIkltbWVyIG9ubHkgc3VwcG9ydHMgZGVsZXRpbmcgYXJyYXkgaW5kaWNlc1wiLFxuICBcIkltbWVyIG9ubHkgc3VwcG9ydHMgc2V0dGluZyBhcnJheSBpbmRpY2VzIGFuZCB0aGUgJ2xlbmd0aCcgcHJvcGVydHlcIixcbiAgZnVuY3Rpb24odGhpbmcpIHtcbiAgICByZXR1cm4gYCdvcmlnaW5hbCcgZXhwZWN0cyBhIGRyYWZ0LCBnb3Q6ICR7dGhpbmd9YDtcbiAgfVxuICAvLyBOb3RlOiBpZiBtb3JlIGVycm9ycyBhcmUgYWRkZWQsIHRoZSBlcnJvck9mZnNldCBpbiBQYXRjaGVzLnRzIHNob3VsZCBiZSBpbmNyZWFzZWRcbiAgLy8gU2VlIFBhdGNoZXMudHMgZm9yIGFkZGl0aW9uYWwgZXJyb3JzXG5dIDogW107XG5mdW5jdGlvbiBkaWUoZXJyb3IsIC4uLmFyZ3MpIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgIGNvbnN0IGUgPSBlcnJvcnNbZXJyb3JdO1xuICAgIGNvbnN0IG1zZyA9IHR5cGVvZiBlID09PSBcImZ1bmN0aW9uXCIgPyBlLmFwcGx5KG51bGwsIGFyZ3MpIDogZTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYFtJbW1lcl0gJHttc2d9YCk7XG4gIH1cbiAgdGhyb3cgbmV3IEVycm9yKFxuICAgIGBbSW1tZXJdIG1pbmlmaWVkIGVycm9yIG5yOiAke2Vycm9yfS4gRnVsbCBlcnJvciBhdDogaHR0cHM6Ly9iaXQubHkvM2NYRUtXZmBcbiAgKTtcbn1cblxuLy8gc3JjL3V0aWxzL2NvbW1vbi50c1xudmFyIGdldFByb3RvdHlwZU9mID0gT2JqZWN0LmdldFByb3RvdHlwZU9mO1xuZnVuY3Rpb24gaXNEcmFmdCh2YWx1ZSkge1xuICByZXR1cm4gISF2YWx1ZSAmJiAhIXZhbHVlW0RSQUZUX1NUQVRFXTtcbn1cbmZ1bmN0aW9uIGlzRHJhZnRhYmxlKHZhbHVlKSB7XG4gIGlmICghdmFsdWUpXG4gICAgcmV0dXJuIGZhbHNlO1xuICByZXR1cm4gaXNQbGFpbk9iamVjdCh2YWx1ZSkgfHwgQXJyYXkuaXNBcnJheSh2YWx1ZSkgfHwgISF2YWx1ZVtEUkFGVEFCTEVdIHx8ICEhdmFsdWUuY29uc3RydWN0b3I/LltEUkFGVEFCTEVdIHx8IGlzTWFwKHZhbHVlKSB8fCBpc1NldCh2YWx1ZSk7XG59XG52YXIgb2JqZWN0Q3RvclN0cmluZyA9IE9iamVjdC5wcm90b3R5cGUuY29uc3RydWN0b3IudG9TdHJpbmcoKTtcbmZ1bmN0aW9uIGlzUGxhaW5PYmplY3QodmFsdWUpIHtcbiAgaWYgKCF2YWx1ZSB8fCB0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIpXG4gICAgcmV0dXJuIGZhbHNlO1xuICBjb25zdCBwcm90byA9IGdldFByb3RvdHlwZU9mKHZhbHVlKTtcbiAgaWYgKHByb3RvID09PSBudWxsKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgY29uc3QgQ3RvciA9IE9iamVjdC5oYXNPd25Qcm9wZXJ0eS5jYWxsKHByb3RvLCBcImNvbnN0cnVjdG9yXCIpICYmIHByb3RvLmNvbnN0cnVjdG9yO1xuICBpZiAoQ3RvciA9PT0gT2JqZWN0KVxuICAgIHJldHVybiB0cnVlO1xuICByZXR1cm4gdHlwZW9mIEN0b3IgPT0gXCJmdW5jdGlvblwiICYmIEZ1bmN0aW9uLnRvU3RyaW5nLmNhbGwoQ3RvcikgPT09IG9iamVjdEN0b3JTdHJpbmc7XG59XG5mdW5jdGlvbiBvcmlnaW5hbCh2YWx1ZSkge1xuICBpZiAoIWlzRHJhZnQodmFsdWUpKVxuICAgIGRpZSgxNSwgdmFsdWUpO1xuICByZXR1cm4gdmFsdWVbRFJBRlRfU1RBVEVdLmJhc2VfO1xufVxuZnVuY3Rpb24gZWFjaChvYmosIGl0ZXIpIHtcbiAgaWYgKGdldEFyY2h0eXBlKG9iaikgPT09IDAgLyogT2JqZWN0ICovKSB7XG4gICAgT2JqZWN0LmVudHJpZXMob2JqKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcbiAgICAgIGl0ZXIoa2V5LCB2YWx1ZSwgb2JqKTtcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICBvYmouZm9yRWFjaCgoZW50cnksIGluZGV4KSA9PiBpdGVyKGluZGV4LCBlbnRyeSwgb2JqKSk7XG4gIH1cbn1cbmZ1bmN0aW9uIGdldEFyY2h0eXBlKHRoaW5nKSB7XG4gIGNvbnN0IHN0YXRlID0gdGhpbmdbRFJBRlRfU1RBVEVdO1xuICByZXR1cm4gc3RhdGUgPyBzdGF0ZS50eXBlXyA6IEFycmF5LmlzQXJyYXkodGhpbmcpID8gMSAvKiBBcnJheSAqLyA6IGlzTWFwKHRoaW5nKSA/IDIgLyogTWFwICovIDogaXNTZXQodGhpbmcpID8gMyAvKiBTZXQgKi8gOiAwIC8qIE9iamVjdCAqLztcbn1cbmZ1bmN0aW9uIGhhcyh0aGluZywgcHJvcCkge1xuICByZXR1cm4gZ2V0QXJjaHR5cGUodGhpbmcpID09PSAyIC8qIE1hcCAqLyA/IHRoaW5nLmhhcyhwcm9wKSA6IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbCh0aGluZywgcHJvcCk7XG59XG5mdW5jdGlvbiBnZXQodGhpbmcsIHByb3ApIHtcbiAgcmV0dXJuIGdldEFyY2h0eXBlKHRoaW5nKSA9PT0gMiAvKiBNYXAgKi8gPyB0aGluZy5nZXQocHJvcCkgOiB0aGluZ1twcm9wXTtcbn1cbmZ1bmN0aW9uIHNldCh0aGluZywgcHJvcE9yT2xkVmFsdWUsIHZhbHVlKSB7XG4gIGNvbnN0IHQgPSBnZXRBcmNodHlwZSh0aGluZyk7XG4gIGlmICh0ID09PSAyIC8qIE1hcCAqLylcbiAgICB0aGluZy5zZXQocHJvcE9yT2xkVmFsdWUsIHZhbHVlKTtcbiAgZWxzZSBpZiAodCA9PT0gMyAvKiBTZXQgKi8pIHtcbiAgICB0aGluZy5hZGQodmFsdWUpO1xuICB9IGVsc2VcbiAgICB0aGluZ1twcm9wT3JPbGRWYWx1ZV0gPSB2YWx1ZTtcbn1cbmZ1bmN0aW9uIGlzKHgsIHkpIHtcbiAgaWYgKHggPT09IHkpIHtcbiAgICByZXR1cm4geCAhPT0gMCB8fCAxIC8geCA9PT0gMSAvIHk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHggIT09IHggJiYgeSAhPT0geTtcbiAgfVxufVxuZnVuY3Rpb24gaXNNYXAodGFyZ2V0KSB7XG4gIHJldHVybiB0YXJnZXQgaW5zdGFuY2VvZiBNYXA7XG59XG5mdW5jdGlvbiBpc1NldCh0YXJnZXQpIHtcbiAgcmV0dXJuIHRhcmdldCBpbnN0YW5jZW9mIFNldDtcbn1cbmZ1bmN0aW9uIGxhdGVzdChzdGF0ZSkge1xuICByZXR1cm4gc3RhdGUuY29weV8gfHwgc3RhdGUuYmFzZV87XG59XG5mdW5jdGlvbiBzaGFsbG93Q29weShiYXNlLCBzdHJpY3QpIHtcbiAgaWYgKGlzTWFwKGJhc2UpKSB7XG4gICAgcmV0dXJuIG5ldyBNYXAoYmFzZSk7XG4gIH1cbiAgaWYgKGlzU2V0KGJhc2UpKSB7XG4gICAgcmV0dXJuIG5ldyBTZXQoYmFzZSk7XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkoYmFzZSkpXG4gICAgcmV0dXJuIEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGJhc2UpO1xuICBpZiAoIXN0cmljdCAmJiBpc1BsYWluT2JqZWN0KGJhc2UpKSB7XG4gICAgaWYgKCFnZXRQcm90b3R5cGVPZihiYXNlKSkge1xuICAgICAgY29uc3Qgb2JqID0gLyogQF9fUFVSRV9fICovIE9iamVjdC5jcmVhdGUobnVsbCk7XG4gICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihvYmosIGJhc2UpO1xuICAgIH1cbiAgICByZXR1cm4geyAuLi5iYXNlIH07XG4gIH1cbiAgY29uc3QgZGVzY3JpcHRvcnMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhiYXNlKTtcbiAgZGVsZXRlIGRlc2NyaXB0b3JzW0RSQUZUX1NUQVRFXTtcbiAgbGV0IGtleXMgPSBSZWZsZWN0Lm93bktleXMoZGVzY3JpcHRvcnMpO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBrZXkgPSBrZXlzW2ldO1xuICAgIGNvbnN0IGRlc2MgPSBkZXNjcmlwdG9yc1trZXldO1xuICAgIGlmIChkZXNjLndyaXRhYmxlID09PSBmYWxzZSkge1xuICAgICAgZGVzYy53cml0YWJsZSA9IHRydWU7XG4gICAgICBkZXNjLmNvbmZpZ3VyYWJsZSA9IHRydWU7XG4gICAgfVxuICAgIGlmIChkZXNjLmdldCB8fCBkZXNjLnNldClcbiAgICAgIGRlc2NyaXB0b3JzW2tleV0gPSB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgIC8vIGNvdWxkIGxpdmUgd2l0aCAhIWRlc2Muc2V0IGFzIHdlbGwgaGVyZS4uLlxuICAgICAgICBlbnVtZXJhYmxlOiBkZXNjLmVudW1lcmFibGUsXG4gICAgICAgIHZhbHVlOiBiYXNlW2tleV1cbiAgICAgIH07XG4gIH1cbiAgcmV0dXJuIE9iamVjdC5jcmVhdGUoZ2V0UHJvdG90eXBlT2YoYmFzZSksIGRlc2NyaXB0b3JzKTtcbn1cbmZ1bmN0aW9uIGZyZWV6ZShvYmosIGRlZXAgPSBmYWxzZSkge1xuICBpZiAoaXNGcm96ZW4ob2JqKSB8fCBpc0RyYWZ0KG9iaikgfHwgIWlzRHJhZnRhYmxlKG9iaikpXG4gICAgcmV0dXJuIG9iajtcbiAgaWYgKGdldEFyY2h0eXBlKG9iaikgPiAxKSB7XG4gICAgb2JqLnNldCA9IG9iai5hZGQgPSBvYmouY2xlYXIgPSBvYmouZGVsZXRlID0gZG9udE11dGF0ZUZyb3plbkNvbGxlY3Rpb25zO1xuICB9XG4gIE9iamVjdC5mcmVlemUob2JqKTtcbiAgaWYgKGRlZXApXG4gICAgZWFjaChvYmosIChfa2V5LCB2YWx1ZSkgPT4gZnJlZXplKHZhbHVlLCB0cnVlKSwgdHJ1ZSk7XG4gIHJldHVybiBvYmo7XG59XG5mdW5jdGlvbiBkb250TXV0YXRlRnJvemVuQ29sbGVjdGlvbnMoKSB7XG4gIGRpZSgyKTtcbn1cbmZ1bmN0aW9uIGlzRnJvemVuKG9iaikge1xuICByZXR1cm4gT2JqZWN0LmlzRnJvemVuKG9iaik7XG59XG5cbi8vIHNyYy91dGlscy9wbHVnaW5zLnRzXG52YXIgcGx1Z2lucyA9IHt9O1xuZnVuY3Rpb24gZ2V0UGx1Z2luKHBsdWdpbktleSkge1xuICBjb25zdCBwbHVnaW4gPSBwbHVnaW5zW3BsdWdpbktleV07XG4gIGlmICghcGx1Z2luKSB7XG4gICAgZGllKDAsIHBsdWdpbktleSk7XG4gIH1cbiAgcmV0dXJuIHBsdWdpbjtcbn1cbmZ1bmN0aW9uIGxvYWRQbHVnaW4ocGx1Z2luS2V5LCBpbXBsZW1lbnRhdGlvbikge1xuICBpZiAoIXBsdWdpbnNbcGx1Z2luS2V5XSlcbiAgICBwbHVnaW5zW3BsdWdpbktleV0gPSBpbXBsZW1lbnRhdGlvbjtcbn1cblxuLy8gc3JjL2NvcmUvc2NvcGUudHNcbnZhciBjdXJyZW50U2NvcGU7XG5mdW5jdGlvbiBnZXRDdXJyZW50U2NvcGUoKSB7XG4gIHJldHVybiBjdXJyZW50U2NvcGU7XG59XG5mdW5jdGlvbiBjcmVhdGVTY29wZShwYXJlbnRfLCBpbW1lcl8pIHtcbiAgcmV0dXJuIHtcbiAgICBkcmFmdHNfOiBbXSxcbiAgICBwYXJlbnRfLFxuICAgIGltbWVyXyxcbiAgICAvLyBXaGVuZXZlciB0aGUgbW9kaWZpZWQgZHJhZnQgY29udGFpbnMgYSBkcmFmdCBmcm9tIGFub3RoZXIgc2NvcGUsIHdlXG4gICAgLy8gbmVlZCB0byBwcmV2ZW50IGF1dG8tZnJlZXppbmcgc28gdGhlIHVub3duZWQgZHJhZnQgY2FuIGJlIGZpbmFsaXplZC5cbiAgICBjYW5BdXRvRnJlZXplXzogdHJ1ZSxcbiAgICB1bmZpbmFsaXplZERyYWZ0c186IDBcbiAgfTtcbn1cbmZ1bmN0aW9uIHVzZVBhdGNoZXNJblNjb3BlKHNjb3BlLCBwYXRjaExpc3RlbmVyKSB7XG4gIGlmIChwYXRjaExpc3RlbmVyKSB7XG4gICAgZ2V0UGx1Z2luKFwiUGF0Y2hlc1wiKTtcbiAgICBzY29wZS5wYXRjaGVzXyA9IFtdO1xuICAgIHNjb3BlLmludmVyc2VQYXRjaGVzXyA9IFtdO1xuICAgIHNjb3BlLnBhdGNoTGlzdGVuZXJfID0gcGF0Y2hMaXN0ZW5lcjtcbiAgfVxufVxuZnVuY3Rpb24gcmV2b2tlU2NvcGUoc2NvcGUpIHtcbiAgbGVhdmVTY29wZShzY29wZSk7XG4gIHNjb3BlLmRyYWZ0c18uZm9yRWFjaChyZXZva2VEcmFmdCk7XG4gIHNjb3BlLmRyYWZ0c18gPSBudWxsO1xufVxuZnVuY3Rpb24gbGVhdmVTY29wZShzY29wZSkge1xuICBpZiAoc2NvcGUgPT09IGN1cnJlbnRTY29wZSkge1xuICAgIGN1cnJlbnRTY29wZSA9IHNjb3BlLnBhcmVudF87XG4gIH1cbn1cbmZ1bmN0aW9uIGVudGVyU2NvcGUoaW1tZXIyKSB7XG4gIHJldHVybiBjdXJyZW50U2NvcGUgPSBjcmVhdGVTY29wZShjdXJyZW50U2NvcGUsIGltbWVyMik7XG59XG5mdW5jdGlvbiByZXZva2VEcmFmdChkcmFmdCkge1xuICBjb25zdCBzdGF0ZSA9IGRyYWZ0W0RSQUZUX1NUQVRFXTtcbiAgaWYgKHN0YXRlLnR5cGVfID09PSAwIC8qIE9iamVjdCAqLyB8fCBzdGF0ZS50eXBlXyA9PT0gMSAvKiBBcnJheSAqLylcbiAgICBzdGF0ZS5yZXZva2VfKCk7XG4gIGVsc2VcbiAgICBzdGF0ZS5yZXZva2VkXyA9IHRydWU7XG59XG5cbi8vIHNyYy9jb3JlL2ZpbmFsaXplLnRzXG5mdW5jdGlvbiBwcm9jZXNzUmVzdWx0KHJlc3VsdCwgc2NvcGUpIHtcbiAgc2NvcGUudW5maW5hbGl6ZWREcmFmdHNfID0gc2NvcGUuZHJhZnRzXy5sZW5ndGg7XG4gIGNvbnN0IGJhc2VEcmFmdCA9IHNjb3BlLmRyYWZ0c19bMF07XG4gIGNvbnN0IGlzUmVwbGFjZWQgPSByZXN1bHQgIT09IHZvaWQgMCAmJiByZXN1bHQgIT09IGJhc2VEcmFmdDtcbiAgaWYgKGlzUmVwbGFjZWQpIHtcbiAgICBpZiAoYmFzZURyYWZ0W0RSQUZUX1NUQVRFXS5tb2RpZmllZF8pIHtcbiAgICAgIHJldm9rZVNjb3BlKHNjb3BlKTtcbiAgICAgIGRpZSg0KTtcbiAgICB9XG4gICAgaWYgKGlzRHJhZnRhYmxlKHJlc3VsdCkpIHtcbiAgICAgIHJlc3VsdCA9IGZpbmFsaXplKHNjb3BlLCByZXN1bHQpO1xuICAgICAgaWYgKCFzY29wZS5wYXJlbnRfKVxuICAgICAgICBtYXliZUZyZWV6ZShzY29wZSwgcmVzdWx0KTtcbiAgICB9XG4gICAgaWYgKHNjb3BlLnBhdGNoZXNfKSB7XG4gICAgICBnZXRQbHVnaW4oXCJQYXRjaGVzXCIpLmdlbmVyYXRlUmVwbGFjZW1lbnRQYXRjaGVzXyhcbiAgICAgICAgYmFzZURyYWZ0W0RSQUZUX1NUQVRFXS5iYXNlXyxcbiAgICAgICAgcmVzdWx0LFxuICAgICAgICBzY29wZS5wYXRjaGVzXyxcbiAgICAgICAgc2NvcGUuaW52ZXJzZVBhdGNoZXNfXG4gICAgICApO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByZXN1bHQgPSBmaW5hbGl6ZShzY29wZSwgYmFzZURyYWZ0LCBbXSk7XG4gIH1cbiAgcmV2b2tlU2NvcGUoc2NvcGUpO1xuICBpZiAoc2NvcGUucGF0Y2hlc18pIHtcbiAgICBzY29wZS5wYXRjaExpc3RlbmVyXyhzY29wZS5wYXRjaGVzXywgc2NvcGUuaW52ZXJzZVBhdGNoZXNfKTtcbiAgfVxuICByZXR1cm4gcmVzdWx0ICE9PSBOT1RISU5HID8gcmVzdWx0IDogdm9pZCAwO1xufVxuZnVuY3Rpb24gZmluYWxpemUocm9vdFNjb3BlLCB2YWx1ZSwgcGF0aCkge1xuICBpZiAoaXNGcm96ZW4odmFsdWUpKVxuICAgIHJldHVybiB2YWx1ZTtcbiAgY29uc3Qgc3RhdGUgPSB2YWx1ZVtEUkFGVF9TVEFURV07XG4gIGlmICghc3RhdGUpIHtcbiAgICBlYWNoKFxuICAgICAgdmFsdWUsXG4gICAgICAoa2V5LCBjaGlsZFZhbHVlKSA9PiBmaW5hbGl6ZVByb3BlcnR5KHJvb3RTY29wZSwgc3RhdGUsIHZhbHVlLCBrZXksIGNoaWxkVmFsdWUsIHBhdGgpLFxuICAgICAgdHJ1ZVxuICAgICAgLy8gU2VlICM1OTAsIGRvbid0IHJlY3Vyc2UgaW50byBub24tZW51bWVyYWJsZSBvZiBub24gZHJhZnRlZCBvYmplY3RzXG4gICAgKTtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbiAgaWYgKHN0YXRlLnNjb3BlXyAhPT0gcm9vdFNjb3BlKVxuICAgIHJldHVybiB2YWx1ZTtcbiAgaWYgKCFzdGF0ZS5tb2RpZmllZF8pIHtcbiAgICBtYXliZUZyZWV6ZShyb290U2NvcGUsIHN0YXRlLmJhc2VfLCB0cnVlKTtcbiAgICByZXR1cm4gc3RhdGUuYmFzZV87XG4gIH1cbiAgaWYgKCFzdGF0ZS5maW5hbGl6ZWRfKSB7XG4gICAgc3RhdGUuZmluYWxpemVkXyA9IHRydWU7XG4gICAgc3RhdGUuc2NvcGVfLnVuZmluYWxpemVkRHJhZnRzXy0tO1xuICAgIGNvbnN0IHJlc3VsdCA9IHN0YXRlLmNvcHlfO1xuICAgIGxldCByZXN1bHRFYWNoID0gcmVzdWx0O1xuICAgIGxldCBpc1NldDIgPSBmYWxzZTtcbiAgICBpZiAoc3RhdGUudHlwZV8gPT09IDMgLyogU2V0ICovKSB7XG4gICAgICByZXN1bHRFYWNoID0gbmV3IFNldChyZXN1bHQpO1xuICAgICAgcmVzdWx0LmNsZWFyKCk7XG4gICAgICBpc1NldDIgPSB0cnVlO1xuICAgIH1cbiAgICBlYWNoKFxuICAgICAgcmVzdWx0RWFjaCxcbiAgICAgIChrZXksIGNoaWxkVmFsdWUpID0+IGZpbmFsaXplUHJvcGVydHkocm9vdFNjb3BlLCBzdGF0ZSwgcmVzdWx0LCBrZXksIGNoaWxkVmFsdWUsIHBhdGgsIGlzU2V0MilcbiAgICApO1xuICAgIG1heWJlRnJlZXplKHJvb3RTY29wZSwgcmVzdWx0LCBmYWxzZSk7XG4gICAgaWYgKHBhdGggJiYgcm9vdFNjb3BlLnBhdGNoZXNfKSB7XG4gICAgICBnZXRQbHVnaW4oXCJQYXRjaGVzXCIpLmdlbmVyYXRlUGF0Y2hlc18oXG4gICAgICAgIHN0YXRlLFxuICAgICAgICBwYXRoLFxuICAgICAgICByb290U2NvcGUucGF0Y2hlc18sXG4gICAgICAgIHJvb3RTY29wZS5pbnZlcnNlUGF0Y2hlc19cbiAgICAgICk7XG4gICAgfVxuICB9XG4gIHJldHVybiBzdGF0ZS5jb3B5Xztcbn1cbmZ1bmN0aW9uIGZpbmFsaXplUHJvcGVydHkocm9vdFNjb3BlLCBwYXJlbnRTdGF0ZSwgdGFyZ2V0T2JqZWN0LCBwcm9wLCBjaGlsZFZhbHVlLCByb290UGF0aCwgdGFyZ2V0SXNTZXQpIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiAmJiBjaGlsZFZhbHVlID09PSB0YXJnZXRPYmplY3QpXG4gICAgZGllKDUpO1xuICBpZiAoaXNEcmFmdChjaGlsZFZhbHVlKSkge1xuICAgIGNvbnN0IHBhdGggPSByb290UGF0aCAmJiBwYXJlbnRTdGF0ZSAmJiBwYXJlbnRTdGF0ZS50eXBlXyAhPT0gMyAvKiBTZXQgKi8gJiYgLy8gU2V0IG9iamVjdHMgYXJlIGF0b21pYyBzaW5jZSB0aGV5IGhhdmUgbm8ga2V5cy5cbiAgICAhaGFzKHBhcmVudFN0YXRlLmFzc2lnbmVkXywgcHJvcCkgPyByb290UGF0aC5jb25jYXQocHJvcCkgOiB2b2lkIDA7XG4gICAgY29uc3QgcmVzID0gZmluYWxpemUocm9vdFNjb3BlLCBjaGlsZFZhbHVlLCBwYXRoKTtcbiAgICBzZXQodGFyZ2V0T2JqZWN0LCBwcm9wLCByZXMpO1xuICAgIGlmIChpc0RyYWZ0KHJlcykpIHtcbiAgICAgIHJvb3RTY29wZS5jYW5BdXRvRnJlZXplXyA9IGZhbHNlO1xuICAgIH0gZWxzZVxuICAgICAgcmV0dXJuO1xuICB9IGVsc2UgaWYgKHRhcmdldElzU2V0KSB7XG4gICAgdGFyZ2V0T2JqZWN0LmFkZChjaGlsZFZhbHVlKTtcbiAgfVxuICBpZiAoaXNEcmFmdGFibGUoY2hpbGRWYWx1ZSkgJiYgIWlzRnJvemVuKGNoaWxkVmFsdWUpKSB7XG4gICAgaWYgKCFyb290U2NvcGUuaW1tZXJfLmF1dG9GcmVlemVfICYmIHJvb3RTY29wZS51bmZpbmFsaXplZERyYWZ0c18gPCAxKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGZpbmFsaXplKHJvb3RTY29wZSwgY2hpbGRWYWx1ZSk7XG4gICAgaWYgKCFwYXJlbnRTdGF0ZSB8fCAhcGFyZW50U3RhdGUuc2NvcGVfLnBhcmVudF8pXG4gICAgICBtYXliZUZyZWV6ZShyb290U2NvcGUsIGNoaWxkVmFsdWUpO1xuICB9XG59XG5mdW5jdGlvbiBtYXliZUZyZWV6ZShzY29wZSwgdmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBpZiAoIXNjb3BlLnBhcmVudF8gJiYgc2NvcGUuaW1tZXJfLmF1dG9GcmVlemVfICYmIHNjb3BlLmNhbkF1dG9GcmVlemVfKSB7XG4gICAgZnJlZXplKHZhbHVlLCBkZWVwKTtcbiAgfVxufVxuXG4vLyBzcmMvY29yZS9wcm94eS50c1xuZnVuY3Rpb24gY3JlYXRlUHJveHlQcm94eShiYXNlLCBwYXJlbnQpIHtcbiAgY29uc3QgaXNBcnJheSA9IEFycmF5LmlzQXJyYXkoYmFzZSk7XG4gIGNvbnN0IHN0YXRlID0ge1xuICAgIHR5cGVfOiBpc0FycmF5ID8gMSAvKiBBcnJheSAqLyA6IDAgLyogT2JqZWN0ICovLFxuICAgIC8vIFRyYWNrIHdoaWNoIHByb2R1Y2UgY2FsbCB0aGlzIGlzIGFzc29jaWF0ZWQgd2l0aC5cbiAgICBzY29wZV86IHBhcmVudCA/IHBhcmVudC5zY29wZV8gOiBnZXRDdXJyZW50U2NvcGUoKSxcbiAgICAvLyBUcnVlIGZvciBib3RoIHNoYWxsb3cgYW5kIGRlZXAgY2hhbmdlcy5cbiAgICBtb2RpZmllZF86IGZhbHNlLFxuICAgIC8vIFVzZWQgZHVyaW5nIGZpbmFsaXphdGlvbi5cbiAgICBmaW5hbGl6ZWRfOiBmYWxzZSxcbiAgICAvLyBUcmFjayB3aGljaCBwcm9wZXJ0aWVzIGhhdmUgYmVlbiBhc3NpZ25lZCAodHJ1ZSkgb3IgZGVsZXRlZCAoZmFsc2UpLlxuICAgIGFzc2lnbmVkXzoge30sXG4gICAgLy8gVGhlIHBhcmVudCBkcmFmdCBzdGF0ZS5cbiAgICBwYXJlbnRfOiBwYXJlbnQsXG4gICAgLy8gVGhlIGJhc2Ugc3RhdGUuXG4gICAgYmFzZV86IGJhc2UsXG4gICAgLy8gVGhlIGJhc2UgcHJveHkuXG4gICAgZHJhZnRfOiBudWxsLFxuICAgIC8vIHNldCBiZWxvd1xuICAgIC8vIFRoZSBiYXNlIGNvcHkgd2l0aCBhbnkgdXBkYXRlZCB2YWx1ZXMuXG4gICAgY29weV86IG51bGwsXG4gICAgLy8gQ2FsbGVkIGJ5IHRoZSBgcHJvZHVjZWAgZnVuY3Rpb24uXG4gICAgcmV2b2tlXzogbnVsbCxcbiAgICBpc01hbnVhbF86IGZhbHNlXG4gIH07XG4gIGxldCB0YXJnZXQgPSBzdGF0ZTtcbiAgbGV0IHRyYXBzID0gb2JqZWN0VHJhcHM7XG4gIGlmIChpc0FycmF5KSB7XG4gICAgdGFyZ2V0ID0gW3N0YXRlXTtcbiAgICB0cmFwcyA9IGFycmF5VHJhcHM7XG4gIH1cbiAgY29uc3QgeyByZXZva2UsIHByb3h5IH0gPSBQcm94eS5yZXZvY2FibGUodGFyZ2V0LCB0cmFwcyk7XG4gIHN0YXRlLmRyYWZ0XyA9IHByb3h5O1xuICBzdGF0ZS5yZXZva2VfID0gcmV2b2tlO1xuICByZXR1cm4gcHJveHk7XG59XG52YXIgb2JqZWN0VHJhcHMgPSB7XG4gIGdldChzdGF0ZSwgcHJvcCkge1xuICAgIGlmIChwcm9wID09PSBEUkFGVF9TVEFURSlcbiAgICAgIHJldHVybiBzdGF0ZTtcbiAgICBjb25zdCBzb3VyY2UgPSBsYXRlc3Qoc3RhdGUpO1xuICAgIGlmICghaGFzKHNvdXJjZSwgcHJvcCkpIHtcbiAgICAgIHJldHVybiByZWFkUHJvcEZyb21Qcm90byhzdGF0ZSwgc291cmNlLCBwcm9wKTtcbiAgICB9XG4gICAgY29uc3QgdmFsdWUgPSBzb3VyY2VbcHJvcF07XG4gICAgaWYgKHN0YXRlLmZpbmFsaXplZF8gfHwgIWlzRHJhZnRhYmxlKHZhbHVlKSkge1xuICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBpZiAodmFsdWUgPT09IHBlZWsoc3RhdGUuYmFzZV8sIHByb3ApKSB7XG4gICAgICBwcmVwYXJlQ29weShzdGF0ZSk7XG4gICAgICByZXR1cm4gc3RhdGUuY29weV9bcHJvcF0gPSBjcmVhdGVQcm94eSh2YWx1ZSwgc3RhdGUpO1xuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG4gIH0sXG4gIGhhcyhzdGF0ZSwgcHJvcCkge1xuICAgIHJldHVybiBwcm9wIGluIGxhdGVzdChzdGF0ZSk7XG4gIH0sXG4gIG93bktleXMoc3RhdGUpIHtcbiAgICByZXR1cm4gUmVmbGVjdC5vd25LZXlzKGxhdGVzdChzdGF0ZSkpO1xuICB9LFxuICBzZXQoc3RhdGUsIHByb3AsIHZhbHVlKSB7XG4gICAgY29uc3QgZGVzYyA9IGdldERlc2NyaXB0b3JGcm9tUHJvdG8obGF0ZXN0KHN0YXRlKSwgcHJvcCk7XG4gICAgaWYgKGRlc2M/LnNldCkge1xuICAgICAgZGVzYy5zZXQuY2FsbChzdGF0ZS5kcmFmdF8sIHZhbHVlKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAoIXN0YXRlLm1vZGlmaWVkXykge1xuICAgICAgY29uc3QgY3VycmVudDIgPSBwZWVrKGxhdGVzdChzdGF0ZSksIHByb3ApO1xuICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gY3VycmVudDI/LltEUkFGVF9TVEFURV07XG4gICAgICBpZiAoY3VycmVudFN0YXRlICYmIGN1cnJlbnRTdGF0ZS5iYXNlXyA9PT0gdmFsdWUpIHtcbiAgICAgICAgc3RhdGUuY29weV9bcHJvcF0gPSB2YWx1ZTtcbiAgICAgICAgc3RhdGUuYXNzaWduZWRfW3Byb3BdID0gZmFsc2U7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgICAgaWYgKGlzKHZhbHVlLCBjdXJyZW50MikgJiYgKHZhbHVlICE9PSB2b2lkIDAgfHwgaGFzKHN0YXRlLmJhc2VfLCBwcm9wKSkpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgcHJlcGFyZUNvcHkoc3RhdGUpO1xuICAgICAgbWFya0NoYW5nZWQoc3RhdGUpO1xuICAgIH1cbiAgICBpZiAoc3RhdGUuY29weV9bcHJvcF0gPT09IHZhbHVlICYmIC8vIHNwZWNpYWwgY2FzZTogaGFuZGxlIG5ldyBwcm9wcyB3aXRoIHZhbHVlICd1bmRlZmluZWQnXG4gICAgKHZhbHVlICE9PSB2b2lkIDAgfHwgcHJvcCBpbiBzdGF0ZS5jb3B5XykgfHwgLy8gc3BlY2lhbCBjYXNlOiBOYU5cbiAgICBOdW1iZXIuaXNOYU4odmFsdWUpICYmIE51bWJlci5pc05hTihzdGF0ZS5jb3B5X1twcm9wXSkpXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICBzdGF0ZS5jb3B5X1twcm9wXSA9IHZhbHVlO1xuICAgIHN0YXRlLmFzc2lnbmVkX1twcm9wXSA9IHRydWU7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sXG4gIGRlbGV0ZVByb3BlcnR5KHN0YXRlLCBwcm9wKSB7XG4gICAgaWYgKHBlZWsoc3RhdGUuYmFzZV8sIHByb3ApICE9PSB2b2lkIDAgfHwgcHJvcCBpbiBzdGF0ZS5iYXNlXykge1xuICAgICAgc3RhdGUuYXNzaWduZWRfW3Byb3BdID0gZmFsc2U7XG4gICAgICBwcmVwYXJlQ29weShzdGF0ZSk7XG4gICAgICBtYXJrQ2hhbmdlZChzdGF0ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRlbGV0ZSBzdGF0ZS5hc3NpZ25lZF9bcHJvcF07XG4gICAgfVxuICAgIGlmIChzdGF0ZS5jb3B5Xykge1xuICAgICAgZGVsZXRlIHN0YXRlLmNvcHlfW3Byb3BdO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSxcbiAgLy8gTm90ZTogV2UgbmV2ZXIgY29lcmNlIGBkZXNjLnZhbHVlYCBpbnRvIGFuIEltbWVyIGRyYWZ0LCBiZWNhdXNlIHdlIGNhbid0IG1ha2VcbiAgLy8gdGhlIHNhbWUgZ3VhcmFudGVlIGluIEVTNSBtb2RlLlxuICBnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Ioc3RhdGUsIHByb3ApIHtcbiAgICBjb25zdCBvd25lciA9IGxhdGVzdChzdGF0ZSk7XG4gICAgY29uc3QgZGVzYyA9IFJlZmxlY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG93bmVyLCBwcm9wKTtcbiAgICBpZiAoIWRlc2MpXG4gICAgICByZXR1cm4gZGVzYztcbiAgICByZXR1cm4ge1xuICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICBjb25maWd1cmFibGU6IHN0YXRlLnR5cGVfICE9PSAxIC8qIEFycmF5ICovIHx8IHByb3AgIT09IFwibGVuZ3RoXCIsXG4gICAgICBlbnVtZXJhYmxlOiBkZXNjLmVudW1lcmFibGUsXG4gICAgICB2YWx1ZTogb3duZXJbcHJvcF1cbiAgICB9O1xuICB9LFxuICBkZWZpbmVQcm9wZXJ0eSgpIHtcbiAgICBkaWUoMTEpO1xuICB9LFxuICBnZXRQcm90b3R5cGVPZihzdGF0ZSkge1xuICAgIHJldHVybiBnZXRQcm90b3R5cGVPZihzdGF0ZS5iYXNlXyk7XG4gIH0sXG4gIHNldFByb3RvdHlwZU9mKCkge1xuICAgIGRpZSgxMik7XG4gIH1cbn07XG52YXIgYXJyYXlUcmFwcyA9IHt9O1xuZWFjaChvYmplY3RUcmFwcywgKGtleSwgZm4pID0+IHtcbiAgYXJyYXlUcmFwc1trZXldID0gZnVuY3Rpb24oKSB7XG4gICAgYXJndW1lbnRzWzBdID0gYXJndW1lbnRzWzBdWzBdO1xuICAgIHJldHVybiBmbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICB9O1xufSk7XG5hcnJheVRyYXBzLmRlbGV0ZVByb3BlcnR5ID0gZnVuY3Rpb24oc3RhdGUsIHByb3ApIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiAmJiBpc05hTihwYXJzZUludChwcm9wKSkpXG4gICAgZGllKDEzKTtcbiAgcmV0dXJuIGFycmF5VHJhcHMuc2V0LmNhbGwodGhpcywgc3RhdGUsIHByb3AsIHZvaWQgMCk7XG59O1xuYXJyYXlUcmFwcy5zZXQgPSBmdW5jdGlvbihzdGF0ZSwgcHJvcCwgdmFsdWUpIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiAmJiBwcm9wICE9PSBcImxlbmd0aFwiICYmIGlzTmFOKHBhcnNlSW50KHByb3ApKSlcbiAgICBkaWUoMTQpO1xuICByZXR1cm4gb2JqZWN0VHJhcHMuc2V0LmNhbGwodGhpcywgc3RhdGVbMF0sIHByb3AsIHZhbHVlLCBzdGF0ZVswXSk7XG59O1xuZnVuY3Rpb24gcGVlayhkcmFmdCwgcHJvcCkge1xuICBjb25zdCBzdGF0ZSA9IGRyYWZ0W0RSQUZUX1NUQVRFXTtcbiAgY29uc3Qgc291cmNlID0gc3RhdGUgPyBsYXRlc3Qoc3RhdGUpIDogZHJhZnQ7XG4gIHJldHVybiBzb3VyY2VbcHJvcF07XG59XG5mdW5jdGlvbiByZWFkUHJvcEZyb21Qcm90byhzdGF0ZSwgc291cmNlLCBwcm9wKSB7XG4gIGNvbnN0IGRlc2MgPSBnZXREZXNjcmlwdG9yRnJvbVByb3RvKHNvdXJjZSwgcHJvcCk7XG4gIHJldHVybiBkZXNjID8gYHZhbHVlYCBpbiBkZXNjID8gZGVzYy52YWx1ZSA6IChcbiAgICAvLyBUaGlzIGlzIGEgdmVyeSBzcGVjaWFsIGNhc2UsIGlmIHRoZSBwcm9wIGlzIGEgZ2V0dGVyIGRlZmluZWQgYnkgdGhlXG4gICAgLy8gcHJvdG90eXBlLCB3ZSBzaG91bGQgaW52b2tlIGl0IHdpdGggdGhlIGRyYWZ0IGFzIGNvbnRleHQhXG4gICAgZGVzYy5nZXQ/LmNhbGwoc3RhdGUuZHJhZnRfKVxuICApIDogdm9pZCAwO1xufVxuZnVuY3Rpb24gZ2V0RGVzY3JpcHRvckZyb21Qcm90byhzb3VyY2UsIHByb3ApIHtcbiAgaWYgKCEocHJvcCBpbiBzb3VyY2UpKVxuICAgIHJldHVybiB2b2lkIDA7XG4gIGxldCBwcm90byA9IGdldFByb3RvdHlwZU9mKHNvdXJjZSk7XG4gIHdoaWxlIChwcm90bykge1xuICAgIGNvbnN0IGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHByb3RvLCBwcm9wKTtcbiAgICBpZiAoZGVzYylcbiAgICAgIHJldHVybiBkZXNjO1xuICAgIHByb3RvID0gZ2V0UHJvdG90eXBlT2YocHJvdG8pO1xuICB9XG4gIHJldHVybiB2b2lkIDA7XG59XG5mdW5jdGlvbiBtYXJrQ2hhbmdlZChzdGF0ZSkge1xuICBpZiAoIXN0YXRlLm1vZGlmaWVkXykge1xuICAgIHN0YXRlLm1vZGlmaWVkXyA9IHRydWU7XG4gICAgaWYgKHN0YXRlLnBhcmVudF8pIHtcbiAgICAgIG1hcmtDaGFuZ2VkKHN0YXRlLnBhcmVudF8pO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gcHJlcGFyZUNvcHkoc3RhdGUpIHtcbiAgaWYgKCFzdGF0ZS5jb3B5Xykge1xuICAgIHN0YXRlLmNvcHlfID0gc2hhbGxvd0NvcHkoXG4gICAgICBzdGF0ZS5iYXNlXyxcbiAgICAgIHN0YXRlLnNjb3BlXy5pbW1lcl8udXNlU3RyaWN0U2hhbGxvd0NvcHlfXG4gICAgKTtcbiAgfVxufVxuXG4vLyBzcmMvY29yZS9pbW1lckNsYXNzLnRzXG52YXIgSW1tZXIyID0gY2xhc3Mge1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICB0aGlzLmF1dG9GcmVlemVfID0gdHJ1ZTtcbiAgICB0aGlzLnVzZVN0cmljdFNoYWxsb3dDb3B5XyA9IGZhbHNlO1xuICAgIC8qKlxuICAgICAqIFRoZSBgcHJvZHVjZWAgZnVuY3Rpb24gdGFrZXMgYSB2YWx1ZSBhbmQgYSBcInJlY2lwZSBmdW5jdGlvblwiICh3aG9zZVxuICAgICAqIHJldHVybiB2YWx1ZSBvZnRlbiBkZXBlbmRzIG9uIHRoZSBiYXNlIHN0YXRlKS4gVGhlIHJlY2lwZSBmdW5jdGlvbiBpc1xuICAgICAqIGZyZWUgdG8gbXV0YXRlIGl0cyBmaXJzdCBhcmd1bWVudCBob3dldmVyIGl0IHdhbnRzLiBBbGwgbXV0YXRpb25zIGFyZVxuICAgICAqIG9ubHkgZXZlciBhcHBsaWVkIHRvIGEgX19jb3B5X18gb2YgdGhlIGJhc2Ugc3RhdGUuXG4gICAgICpcbiAgICAgKiBQYXNzIG9ubHkgYSBmdW5jdGlvbiB0byBjcmVhdGUgYSBcImN1cnJpZWQgcHJvZHVjZXJcIiB3aGljaCByZWxpZXZlcyB5b3VcbiAgICAgKiBmcm9tIHBhc3NpbmcgdGhlIHJlY2lwZSBmdW5jdGlvbiBldmVyeSB0aW1lLlxuICAgICAqXG4gICAgICogT25seSBwbGFpbiBvYmplY3RzIGFuZCBhcnJheXMgYXJlIG1hZGUgbXV0YWJsZS4gQWxsIG90aGVyIG9iamVjdHMgYXJlXG4gICAgICogY29uc2lkZXJlZCB1bmNvcHlhYmxlLlxuICAgICAqXG4gICAgICogTm90ZTogVGhpcyBmdW5jdGlvbiBpcyBfX2JvdW5kX18gdG8gaXRzIGBJbW1lcmAgaW5zdGFuY2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge2FueX0gYmFzZSAtIHRoZSBpbml0aWFsIHN0YXRlXG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gcmVjaXBlIC0gZnVuY3Rpb24gdGhhdCByZWNlaXZlcyBhIHByb3h5IG9mIHRoZSBiYXNlIHN0YXRlIGFzIGZpcnN0IGFyZ3VtZW50IGFuZCB3aGljaCBjYW4gYmUgZnJlZWx5IG1vZGlmaWVkXG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gcGF0Y2hMaXN0ZW5lciAtIG9wdGlvbmFsIGZ1bmN0aW9uIHRoYXQgd2lsbCBiZSBjYWxsZWQgd2l0aCBhbGwgdGhlIHBhdGNoZXMgcHJvZHVjZWQgaGVyZVxuICAgICAqIEByZXR1cm5zIHthbnl9IGEgbmV3IHN0YXRlLCBvciB0aGUgaW5pdGlhbCBzdGF0ZSBpZiBub3RoaW5nIHdhcyBtb2RpZmllZFxuICAgICAqL1xuICAgIHRoaXMucHJvZHVjZSA9IChiYXNlLCByZWNpcGUsIHBhdGNoTGlzdGVuZXIpID0+IHtcbiAgICAgIGlmICh0eXBlb2YgYmFzZSA9PT0gXCJmdW5jdGlvblwiICYmIHR5cGVvZiByZWNpcGUgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICBjb25zdCBkZWZhdWx0QmFzZSA9IHJlY2lwZTtcbiAgICAgICAgcmVjaXBlID0gYmFzZTtcbiAgICAgICAgY29uc3Qgc2VsZiA9IHRoaXM7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiBjdXJyaWVkUHJvZHVjZShiYXNlMiA9IGRlZmF1bHRCYXNlLCAuLi5hcmdzKSB7XG4gICAgICAgICAgcmV0dXJuIHNlbGYucHJvZHVjZShiYXNlMiwgKGRyYWZ0KSA9PiByZWNpcGUuY2FsbCh0aGlzLCBkcmFmdCwgLi4uYXJncykpO1xuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgaWYgKHR5cGVvZiByZWNpcGUgIT09IFwiZnVuY3Rpb25cIilcbiAgICAgICAgZGllKDYpO1xuICAgICAgaWYgKHBhdGNoTGlzdGVuZXIgIT09IHZvaWQgMCAmJiB0eXBlb2YgcGF0Y2hMaXN0ZW5lciAhPT0gXCJmdW5jdGlvblwiKVxuICAgICAgICBkaWUoNyk7XG4gICAgICBsZXQgcmVzdWx0O1xuICAgICAgaWYgKGlzRHJhZnRhYmxlKGJhc2UpKSB7XG4gICAgICAgIGNvbnN0IHNjb3BlID0gZW50ZXJTY29wZSh0aGlzKTtcbiAgICAgICAgY29uc3QgcHJveHkgPSBjcmVhdGVQcm94eShiYXNlLCB2b2lkIDApO1xuICAgICAgICBsZXQgaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIHJlc3VsdCA9IHJlY2lwZShwcm94eSk7XG4gICAgICAgICAgaGFzRXJyb3IgPSBmYWxzZTtcbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICBpZiAoaGFzRXJyb3IpXG4gICAgICAgICAgICByZXZva2VTY29wZShzY29wZSk7XG4gICAgICAgICAgZWxzZVxuICAgICAgICAgICAgbGVhdmVTY29wZShzY29wZSk7XG4gICAgICAgIH1cbiAgICAgICAgdXNlUGF0Y2hlc0luU2NvcGUoc2NvcGUsIHBhdGNoTGlzdGVuZXIpO1xuICAgICAgICByZXR1cm4gcHJvY2Vzc1Jlc3VsdChyZXN1bHQsIHNjb3BlKTtcbiAgICAgIH0gZWxzZSBpZiAoIWJhc2UgfHwgdHlwZW9mIGJhc2UgIT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgcmVzdWx0ID0gcmVjaXBlKGJhc2UpO1xuICAgICAgICBpZiAocmVzdWx0ID09PSB2b2lkIDApXG4gICAgICAgICAgcmVzdWx0ID0gYmFzZTtcbiAgICAgICAgaWYgKHJlc3VsdCA9PT0gTk9USElORylcbiAgICAgICAgICByZXN1bHQgPSB2b2lkIDA7XG4gICAgICAgIGlmICh0aGlzLmF1dG9GcmVlemVfKVxuICAgICAgICAgIGZyZWV6ZShyZXN1bHQsIHRydWUpO1xuICAgICAgICBpZiAocGF0Y2hMaXN0ZW5lcikge1xuICAgICAgICAgIGNvbnN0IHAgPSBbXTtcbiAgICAgICAgICBjb25zdCBpcCA9IFtdO1xuICAgICAgICAgIGdldFBsdWdpbihcIlBhdGNoZXNcIikuZ2VuZXJhdGVSZXBsYWNlbWVudFBhdGNoZXNfKGJhc2UsIHJlc3VsdCwgcCwgaXApO1xuICAgICAgICAgIHBhdGNoTGlzdGVuZXIocCwgaXApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICB9IGVsc2VcbiAgICAgICAgZGllKDEsIGJhc2UpO1xuICAgIH07XG4gICAgdGhpcy5wcm9kdWNlV2l0aFBhdGNoZXMgPSAoYmFzZSwgcmVjaXBlKSA9PiB7XG4gICAgICBpZiAodHlwZW9mIGJhc2UgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4gKHN0YXRlLCAuLi5hcmdzKSA9PiB0aGlzLnByb2R1Y2VXaXRoUGF0Y2hlcyhzdGF0ZSwgKGRyYWZ0KSA9PiBiYXNlKGRyYWZ0LCAuLi5hcmdzKSk7XG4gICAgICB9XG4gICAgICBsZXQgcGF0Y2hlcywgaW52ZXJzZVBhdGNoZXM7XG4gICAgICBjb25zdCByZXN1bHQgPSB0aGlzLnByb2R1Y2UoYmFzZSwgcmVjaXBlLCAocCwgaXApID0+IHtcbiAgICAgICAgcGF0Y2hlcyA9IHA7XG4gICAgICAgIGludmVyc2VQYXRjaGVzID0gaXA7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBbcmVzdWx0LCBwYXRjaGVzLCBpbnZlcnNlUGF0Y2hlc107XG4gICAgfTtcbiAgICBpZiAodHlwZW9mIGNvbmZpZz8uYXV0b0ZyZWV6ZSA9PT0gXCJib29sZWFuXCIpXG4gICAgICB0aGlzLnNldEF1dG9GcmVlemUoY29uZmlnLmF1dG9GcmVlemUpO1xuICAgIGlmICh0eXBlb2YgY29uZmlnPy51c2VTdHJpY3RTaGFsbG93Q29weSA9PT0gXCJib29sZWFuXCIpXG4gICAgICB0aGlzLnNldFVzZVN0cmljdFNoYWxsb3dDb3B5KGNvbmZpZy51c2VTdHJpY3RTaGFsbG93Q29weSk7XG4gIH1cbiAgY3JlYXRlRHJhZnQoYmFzZSkge1xuICAgIGlmICghaXNEcmFmdGFibGUoYmFzZSkpXG4gICAgICBkaWUoOCk7XG4gICAgaWYgKGlzRHJhZnQoYmFzZSkpXG4gICAgICBiYXNlID0gY3VycmVudChiYXNlKTtcbiAgICBjb25zdCBzY29wZSA9IGVudGVyU2NvcGUodGhpcyk7XG4gICAgY29uc3QgcHJveHkgPSBjcmVhdGVQcm94eShiYXNlLCB2b2lkIDApO1xuICAgIHByb3h5W0RSQUZUX1NUQVRFXS5pc01hbnVhbF8gPSB0cnVlO1xuICAgIGxlYXZlU2NvcGUoc2NvcGUpO1xuICAgIHJldHVybiBwcm94eTtcbiAgfVxuICBmaW5pc2hEcmFmdChkcmFmdCwgcGF0Y2hMaXN0ZW5lcikge1xuICAgIGNvbnN0IHN0YXRlID0gZHJhZnQgJiYgZHJhZnRbRFJBRlRfU1RBVEVdO1xuICAgIGlmICghc3RhdGUgfHwgIXN0YXRlLmlzTWFudWFsXylcbiAgICAgIGRpZSg5KTtcbiAgICBjb25zdCB7IHNjb3BlXzogc2NvcGUgfSA9IHN0YXRlO1xuICAgIHVzZVBhdGNoZXNJblNjb3BlKHNjb3BlLCBwYXRjaExpc3RlbmVyKTtcbiAgICByZXR1cm4gcHJvY2Vzc1Jlc3VsdCh2b2lkIDAsIHNjb3BlKTtcbiAgfVxuICAvKipcbiAgICogUGFzcyB0cnVlIHRvIGF1dG9tYXRpY2FsbHkgZnJlZXplIGFsbCBjb3BpZXMgY3JlYXRlZCBieSBJbW1lci5cbiAgICpcbiAgICogQnkgZGVmYXVsdCwgYXV0by1mcmVlemluZyBpcyBlbmFibGVkLlxuICAgKi9cbiAgc2V0QXV0b0ZyZWV6ZSh2YWx1ZSkge1xuICAgIHRoaXMuYXV0b0ZyZWV6ZV8gPSB2YWx1ZTtcbiAgfVxuICAvKipcbiAgICogUGFzcyB0cnVlIHRvIGVuYWJsZSBzdHJpY3Qgc2hhbGxvdyBjb3B5LlxuICAgKlxuICAgKiBCeSBkZWZhdWx0LCBpbW1lciBkb2VzIG5vdCBjb3B5IHRoZSBvYmplY3QgZGVzY3JpcHRvcnMgc3VjaCBhcyBnZXR0ZXIsIHNldHRlciBhbmQgbm9uLWVudW1yYWJsZSBwcm9wZXJ0aWVzLlxuICAgKi9cbiAgc2V0VXNlU3RyaWN0U2hhbGxvd0NvcHkodmFsdWUpIHtcbiAgICB0aGlzLnVzZVN0cmljdFNoYWxsb3dDb3B5XyA9IHZhbHVlO1xuICB9XG4gIGFwcGx5UGF0Y2hlcyhiYXNlLCBwYXRjaGVzKSB7XG4gICAgbGV0IGk7XG4gICAgZm9yIChpID0gcGF0Y2hlcy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgY29uc3QgcGF0Y2ggPSBwYXRjaGVzW2ldO1xuICAgICAgaWYgKHBhdGNoLnBhdGgubGVuZ3RoID09PSAwICYmIHBhdGNoLm9wID09PSBcInJlcGxhY2VcIikge1xuICAgICAgICBiYXNlID0gcGF0Y2gudmFsdWU7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoaSA+IC0xKSB7XG4gICAgICBwYXRjaGVzID0gcGF0Y2hlcy5zbGljZShpICsgMSk7XG4gICAgfVxuICAgIGNvbnN0IGFwcGx5UGF0Y2hlc0ltcGwgPSBnZXRQbHVnaW4oXCJQYXRjaGVzXCIpLmFwcGx5UGF0Y2hlc187XG4gICAgaWYgKGlzRHJhZnQoYmFzZSkpIHtcbiAgICAgIHJldHVybiBhcHBseVBhdGNoZXNJbXBsKGJhc2UsIHBhdGNoZXMpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5wcm9kdWNlKFxuICAgICAgYmFzZSxcbiAgICAgIChkcmFmdCkgPT4gYXBwbHlQYXRjaGVzSW1wbChkcmFmdCwgcGF0Y2hlcylcbiAgICApO1xuICB9XG59O1xuZnVuY3Rpb24gY3JlYXRlUHJveHkodmFsdWUsIHBhcmVudCkge1xuICBjb25zdCBkcmFmdCA9IGlzTWFwKHZhbHVlKSA/IGdldFBsdWdpbihcIk1hcFNldFwiKS5wcm94eU1hcF8odmFsdWUsIHBhcmVudCkgOiBpc1NldCh2YWx1ZSkgPyBnZXRQbHVnaW4oXCJNYXBTZXRcIikucHJveHlTZXRfKHZhbHVlLCBwYXJlbnQpIDogY3JlYXRlUHJveHlQcm94eSh2YWx1ZSwgcGFyZW50KTtcbiAgY29uc3Qgc2NvcGUgPSBwYXJlbnQgPyBwYXJlbnQuc2NvcGVfIDogZ2V0Q3VycmVudFNjb3BlKCk7XG4gIHNjb3BlLmRyYWZ0c18ucHVzaChkcmFmdCk7XG4gIHJldHVybiBkcmFmdDtcbn1cblxuLy8gc3JjL2NvcmUvY3VycmVudC50c1xuZnVuY3Rpb24gY3VycmVudCh2YWx1ZSkge1xuICBpZiAoIWlzRHJhZnQodmFsdWUpKVxuICAgIGRpZSgxMCwgdmFsdWUpO1xuICByZXR1cm4gY3VycmVudEltcGwodmFsdWUpO1xufVxuZnVuY3Rpb24gY3VycmVudEltcGwodmFsdWUpIHtcbiAgaWYgKCFpc0RyYWZ0YWJsZSh2YWx1ZSkgfHwgaXNGcm96ZW4odmFsdWUpKVxuICAgIHJldHVybiB2YWx1ZTtcbiAgY29uc3Qgc3RhdGUgPSB2YWx1ZVtEUkFGVF9TVEFURV07XG4gIGxldCBjb3B5O1xuICBpZiAoc3RhdGUpIHtcbiAgICBpZiAoIXN0YXRlLm1vZGlmaWVkXylcbiAgICAgIHJldHVybiBzdGF0ZS5iYXNlXztcbiAgICBzdGF0ZS5maW5hbGl6ZWRfID0gdHJ1ZTtcbiAgICBjb3B5ID0gc2hhbGxvd0NvcHkodmFsdWUsIHN0YXRlLnNjb3BlXy5pbW1lcl8udXNlU3RyaWN0U2hhbGxvd0NvcHlfKTtcbiAgfSBlbHNlIHtcbiAgICBjb3B5ID0gc2hhbGxvd0NvcHkodmFsdWUsIHRydWUpO1xuICB9XG4gIGVhY2goY29weSwgKGtleSwgY2hpbGRWYWx1ZSkgPT4ge1xuICAgIHNldChjb3B5LCBrZXksIGN1cnJlbnRJbXBsKGNoaWxkVmFsdWUpKTtcbiAgfSk7XG4gIGlmIChzdGF0ZSkge1xuICAgIHN0YXRlLmZpbmFsaXplZF8gPSBmYWxzZTtcbiAgfVxuICByZXR1cm4gY29weTtcbn1cblxuLy8gc3JjL3BsdWdpbnMvcGF0Y2hlcy50c1xuZnVuY3Rpb24gZW5hYmxlUGF0Y2hlcygpIHtcbiAgY29uc3QgZXJyb3JPZmZzZXQgPSAxNjtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgIGVycm9ycy5wdXNoKFxuICAgICAgJ1NldHMgY2Fubm90IGhhdmUgXCJyZXBsYWNlXCIgcGF0Y2hlcy4nLFxuICAgICAgZnVuY3Rpb24ob3ApIHtcbiAgICAgICAgcmV0dXJuIFwiVW5zdXBwb3J0ZWQgcGF0Y2ggb3BlcmF0aW9uOiBcIiArIG9wO1xuICAgICAgfSxcbiAgICAgIGZ1bmN0aW9uKHBhdGgpIHtcbiAgICAgICAgcmV0dXJuIFwiQ2Fubm90IGFwcGx5IHBhdGNoLCBwYXRoIGRvZXNuJ3QgcmVzb2x2ZTogXCIgKyBwYXRoO1xuICAgICAgfSxcbiAgICAgIFwiUGF0Y2hpbmcgcmVzZXJ2ZWQgYXR0cmlidXRlcyBsaWtlIF9fcHJvdG9fXywgcHJvdG90eXBlIGFuZCBjb25zdHJ1Y3RvciBpcyBub3QgYWxsb3dlZFwiXG4gICAgKTtcbiAgfVxuICBjb25zdCBSRVBMQUNFID0gXCJyZXBsYWNlXCI7XG4gIGNvbnN0IEFERCA9IFwiYWRkXCI7XG4gIGNvbnN0IFJFTU9WRSA9IFwicmVtb3ZlXCI7XG4gIGZ1bmN0aW9uIGdlbmVyYXRlUGF0Y2hlc18oc3RhdGUsIGJhc2VQYXRoLCBwYXRjaGVzLCBpbnZlcnNlUGF0Y2hlcykge1xuICAgIHN3aXRjaCAoc3RhdGUudHlwZV8pIHtcbiAgICAgIGNhc2UgMCAvKiBPYmplY3QgKi86XG4gICAgICBjYXNlIDIgLyogTWFwICovOlxuICAgICAgICByZXR1cm4gZ2VuZXJhdGVQYXRjaGVzRnJvbUFzc2lnbmVkKFxuICAgICAgICAgIHN0YXRlLFxuICAgICAgICAgIGJhc2VQYXRoLFxuICAgICAgICAgIHBhdGNoZXMsXG4gICAgICAgICAgaW52ZXJzZVBhdGNoZXNcbiAgICAgICAgKTtcbiAgICAgIGNhc2UgMSAvKiBBcnJheSAqLzpcbiAgICAgICAgcmV0dXJuIGdlbmVyYXRlQXJyYXlQYXRjaGVzKHN0YXRlLCBiYXNlUGF0aCwgcGF0Y2hlcywgaW52ZXJzZVBhdGNoZXMpO1xuICAgICAgY2FzZSAzIC8qIFNldCAqLzpcbiAgICAgICAgcmV0dXJuIGdlbmVyYXRlU2V0UGF0Y2hlcyhcbiAgICAgICAgICBzdGF0ZSxcbiAgICAgICAgICBiYXNlUGF0aCxcbiAgICAgICAgICBwYXRjaGVzLFxuICAgICAgICAgIGludmVyc2VQYXRjaGVzXG4gICAgICAgICk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIGdlbmVyYXRlQXJyYXlQYXRjaGVzKHN0YXRlLCBiYXNlUGF0aCwgcGF0Y2hlcywgaW52ZXJzZVBhdGNoZXMpIHtcbiAgICBsZXQgeyBiYXNlXywgYXNzaWduZWRfIH0gPSBzdGF0ZTtcbiAgICBsZXQgY29weV8gPSBzdGF0ZS5jb3B5XztcbiAgICBpZiAoY29weV8ubGVuZ3RoIDwgYmFzZV8ubGVuZ3RoKSB7XG4gICAgICA7XG4gICAgICBbYmFzZV8sIGNvcHlfXSA9IFtjb3B5XywgYmFzZV9dO1xuICAgICAgW3BhdGNoZXMsIGludmVyc2VQYXRjaGVzXSA9IFtpbnZlcnNlUGF0Y2hlcywgcGF0Y2hlc107XG4gICAgfVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYmFzZV8ubGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmIChhc3NpZ25lZF9baV0gJiYgY29weV9baV0gIT09IGJhc2VfW2ldKSB7XG4gICAgICAgIGNvbnN0IHBhdGggPSBiYXNlUGF0aC5jb25jYXQoW2ldKTtcbiAgICAgICAgcGF0Y2hlcy5wdXNoKHtcbiAgICAgICAgICBvcDogUkVQTEFDRSxcbiAgICAgICAgICBwYXRoLFxuICAgICAgICAgIC8vIE5lZWQgdG8gbWF5YmUgY2xvbmUgaXQsIGFzIGl0IGNhbiBpbiBmYWN0IGJlIHRoZSBvcmlnaW5hbCB2YWx1ZVxuICAgICAgICAgIC8vIGR1ZSB0byB0aGUgYmFzZS9jb3B5IGludmVyc2lvbiBhdCB0aGUgc3RhcnQgb2YgdGhpcyBmdW5jdGlvblxuICAgICAgICAgIHZhbHVlOiBjbG9uZVBhdGNoVmFsdWVJZk5lZWRlZChjb3B5X1tpXSlcbiAgICAgICAgfSk7XG4gICAgICAgIGludmVyc2VQYXRjaGVzLnB1c2goe1xuICAgICAgICAgIG9wOiBSRVBMQUNFLFxuICAgICAgICAgIHBhdGgsXG4gICAgICAgICAgdmFsdWU6IGNsb25lUGF0Y2hWYWx1ZUlmTmVlZGVkKGJhc2VfW2ldKVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IGJhc2VfLmxlbmd0aDsgaSA8IGNvcHlfLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBwYXRoID0gYmFzZVBhdGguY29uY2F0KFtpXSk7XG4gICAgICBwYXRjaGVzLnB1c2goe1xuICAgICAgICBvcDogQURELFxuICAgICAgICBwYXRoLFxuICAgICAgICAvLyBOZWVkIHRvIG1heWJlIGNsb25lIGl0LCBhcyBpdCBjYW4gaW4gZmFjdCBiZSB0aGUgb3JpZ2luYWwgdmFsdWVcbiAgICAgICAgLy8gZHVlIHRvIHRoZSBiYXNlL2NvcHkgaW52ZXJzaW9uIGF0IHRoZSBzdGFydCBvZiB0aGlzIGZ1bmN0aW9uXG4gICAgICAgIHZhbHVlOiBjbG9uZVBhdGNoVmFsdWVJZk5lZWRlZChjb3B5X1tpXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgICBmb3IgKGxldCBpID0gY29weV8ubGVuZ3RoIC0gMTsgYmFzZV8ubGVuZ3RoIDw9IGk7IC0taSkge1xuICAgICAgY29uc3QgcGF0aCA9IGJhc2VQYXRoLmNvbmNhdChbaV0pO1xuICAgICAgaW52ZXJzZVBhdGNoZXMucHVzaCh7XG4gICAgICAgIG9wOiBSRU1PVkUsXG4gICAgICAgIHBhdGhcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBnZW5lcmF0ZVBhdGNoZXNGcm9tQXNzaWduZWQoc3RhdGUsIGJhc2VQYXRoLCBwYXRjaGVzLCBpbnZlcnNlUGF0Y2hlcykge1xuICAgIGNvbnN0IHsgYmFzZV8sIGNvcHlfIH0gPSBzdGF0ZTtcbiAgICBlYWNoKHN0YXRlLmFzc2lnbmVkXywgKGtleSwgYXNzaWduZWRWYWx1ZSkgPT4ge1xuICAgICAgY29uc3Qgb3JpZ1ZhbHVlID0gZ2V0KGJhc2VfLCBrZXkpO1xuICAgICAgY29uc3QgdmFsdWUgPSBnZXQoY29weV8sIGtleSk7XG4gICAgICBjb25zdCBvcCA9ICFhc3NpZ25lZFZhbHVlID8gUkVNT1ZFIDogaGFzKGJhc2VfLCBrZXkpID8gUkVQTEFDRSA6IEFERDtcbiAgICAgIGlmIChvcmlnVmFsdWUgPT09IHZhbHVlICYmIG9wID09PSBSRVBMQUNFKVxuICAgICAgICByZXR1cm47XG4gICAgICBjb25zdCBwYXRoID0gYmFzZVBhdGguY29uY2F0KGtleSk7XG4gICAgICBwYXRjaGVzLnB1c2gob3AgPT09IFJFTU9WRSA/IHsgb3AsIHBhdGggfSA6IHsgb3AsIHBhdGgsIHZhbHVlIH0pO1xuICAgICAgaW52ZXJzZVBhdGNoZXMucHVzaChcbiAgICAgICAgb3AgPT09IEFERCA/IHsgb3A6IFJFTU9WRSwgcGF0aCB9IDogb3AgPT09IFJFTU9WRSA/IHsgb3A6IEFERCwgcGF0aCwgdmFsdWU6IGNsb25lUGF0Y2hWYWx1ZUlmTmVlZGVkKG9yaWdWYWx1ZSkgfSA6IHsgb3A6IFJFUExBQ0UsIHBhdGgsIHZhbHVlOiBjbG9uZVBhdGNoVmFsdWVJZk5lZWRlZChvcmlnVmFsdWUpIH1cbiAgICAgICk7XG4gICAgfSk7XG4gIH1cbiAgZnVuY3Rpb24gZ2VuZXJhdGVTZXRQYXRjaGVzKHN0YXRlLCBiYXNlUGF0aCwgcGF0Y2hlcywgaW52ZXJzZVBhdGNoZXMpIHtcbiAgICBsZXQgeyBiYXNlXywgY29weV8gfSA9IHN0YXRlO1xuICAgIGxldCBpID0gMDtcbiAgICBiYXNlXy5mb3JFYWNoKCh2YWx1ZSkgPT4ge1xuICAgICAgaWYgKCFjb3B5Xy5oYXModmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IHBhdGggPSBiYXNlUGF0aC5jb25jYXQoW2ldKTtcbiAgICAgICAgcGF0Y2hlcy5wdXNoKHtcbiAgICAgICAgICBvcDogUkVNT1ZFLFxuICAgICAgICAgIHBhdGgsXG4gICAgICAgICAgdmFsdWVcbiAgICAgICAgfSk7XG4gICAgICAgIGludmVyc2VQYXRjaGVzLnVuc2hpZnQoe1xuICAgICAgICAgIG9wOiBBREQsXG4gICAgICAgICAgcGF0aCxcbiAgICAgICAgICB2YWx1ZVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIGkrKztcbiAgICB9KTtcbiAgICBpID0gMDtcbiAgICBjb3B5Xy5mb3JFYWNoKCh2YWx1ZSkgPT4ge1xuICAgICAgaWYgKCFiYXNlXy5oYXModmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IHBhdGggPSBiYXNlUGF0aC5jb25jYXQoW2ldKTtcbiAgICAgICAgcGF0Y2hlcy5wdXNoKHtcbiAgICAgICAgICBvcDogQURELFxuICAgICAgICAgIHBhdGgsXG4gICAgICAgICAgdmFsdWVcbiAgICAgICAgfSk7XG4gICAgICAgIGludmVyc2VQYXRjaGVzLnVuc2hpZnQoe1xuICAgICAgICAgIG9wOiBSRU1PVkUsXG4gICAgICAgICAgcGF0aCxcbiAgICAgICAgICB2YWx1ZVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIGkrKztcbiAgICB9KTtcbiAgfVxuICBmdW5jdGlvbiBnZW5lcmF0ZVJlcGxhY2VtZW50UGF0Y2hlc18oYmFzZVZhbHVlLCByZXBsYWNlbWVudCwgcGF0Y2hlcywgaW52ZXJzZVBhdGNoZXMpIHtcbiAgICBwYXRjaGVzLnB1c2goe1xuICAgICAgb3A6IFJFUExBQ0UsXG4gICAgICBwYXRoOiBbXSxcbiAgICAgIHZhbHVlOiByZXBsYWNlbWVudCA9PT0gTk9USElORyA/IHZvaWQgMCA6IHJlcGxhY2VtZW50XG4gICAgfSk7XG4gICAgaW52ZXJzZVBhdGNoZXMucHVzaCh7XG4gICAgICBvcDogUkVQTEFDRSxcbiAgICAgIHBhdGg6IFtdLFxuICAgICAgdmFsdWU6IGJhc2VWYWx1ZVxuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIGFwcGx5UGF0Y2hlc18oZHJhZnQsIHBhdGNoZXMpIHtcbiAgICBwYXRjaGVzLmZvckVhY2goKHBhdGNoKSA9PiB7XG4gICAgICBjb25zdCB7IHBhdGgsIG9wIH0gPSBwYXRjaDtcbiAgICAgIGxldCBiYXNlID0gZHJhZnQ7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhdGgubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHBhcmVudFR5cGUgPSBnZXRBcmNodHlwZShiYXNlKTtcbiAgICAgICAgbGV0IHAgPSBwYXRoW2ldO1xuICAgICAgICBpZiAodHlwZW9mIHAgIT09IFwic3RyaW5nXCIgJiYgdHlwZW9mIHAgIT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICBwID0gXCJcIiArIHA7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKChwYXJlbnRUeXBlID09PSAwIC8qIE9iamVjdCAqLyB8fCBwYXJlbnRUeXBlID09PSAxIC8qIEFycmF5ICovKSAmJiAocCA9PT0gXCJfX3Byb3RvX19cIiB8fCBwID09PSBcImNvbnN0cnVjdG9yXCIpKVxuICAgICAgICAgIGRpZShlcnJvck9mZnNldCArIDMpO1xuICAgICAgICBpZiAodHlwZW9mIGJhc2UgPT09IFwiZnVuY3Rpb25cIiAmJiBwID09PSBcInByb3RvdHlwZVwiKVxuICAgICAgICAgIGRpZShlcnJvck9mZnNldCArIDMpO1xuICAgICAgICBiYXNlID0gZ2V0KGJhc2UsIHApO1xuICAgICAgICBpZiAodHlwZW9mIGJhc2UgIT09IFwib2JqZWN0XCIpXG4gICAgICAgICAgZGllKGVycm9yT2Zmc2V0ICsgMiwgcGF0aC5qb2luKFwiL1wiKSk7XG4gICAgICB9XG4gICAgICBjb25zdCB0eXBlID0gZ2V0QXJjaHR5cGUoYmFzZSk7XG4gICAgICBjb25zdCB2YWx1ZSA9IGRlZXBDbG9uZVBhdGNoVmFsdWUocGF0Y2gudmFsdWUpO1xuICAgICAgY29uc3Qga2V5ID0gcGF0aFtwYXRoLmxlbmd0aCAtIDFdO1xuICAgICAgc3dpdGNoIChvcCkge1xuICAgICAgICBjYXNlIFJFUExBQ0U6XG4gICAgICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgICAgICBjYXNlIDIgLyogTWFwICovOlxuICAgICAgICAgICAgICByZXR1cm4gYmFzZS5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICBjYXNlIDMgLyogU2V0ICovOlxuICAgICAgICAgICAgICBkaWUoZXJyb3JPZmZzZXQpO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgcmV0dXJuIGJhc2Vba2V5XSA9IHZhbHVlO1xuICAgICAgICAgIH1cbiAgICAgICAgY2FzZSBBREQ6XG4gICAgICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgICAgICBjYXNlIDEgLyogQXJyYXkgKi86XG4gICAgICAgICAgICAgIHJldHVybiBrZXkgPT09IFwiLVwiID8gYmFzZS5wdXNoKHZhbHVlKSA6IGJhc2Uuc3BsaWNlKGtleSwgMCwgdmFsdWUpO1xuICAgICAgICAgICAgY2FzZSAyIC8qIE1hcCAqLzpcbiAgICAgICAgICAgICAgcmV0dXJuIGJhc2Uuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgY2FzZSAzIC8qIFNldCAqLzpcbiAgICAgICAgICAgICAgcmV0dXJuIGJhc2UuYWRkKHZhbHVlKTtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgIHJldHVybiBiYXNlW2tleV0gPSB2YWx1ZTtcbiAgICAgICAgICB9XG4gICAgICAgIGNhc2UgUkVNT1ZFOlxuICAgICAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICAgICAgY2FzZSAxIC8qIEFycmF5ICovOlxuICAgICAgICAgICAgICByZXR1cm4gYmFzZS5zcGxpY2Uoa2V5LCAxKTtcbiAgICAgICAgICAgIGNhc2UgMiAvKiBNYXAgKi86XG4gICAgICAgICAgICAgIHJldHVybiBiYXNlLmRlbGV0ZShrZXkpO1xuICAgICAgICAgICAgY2FzZSAzIC8qIFNldCAqLzpcbiAgICAgICAgICAgICAgcmV0dXJuIGJhc2UuZGVsZXRlKHBhdGNoLnZhbHVlKTtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgIHJldHVybiBkZWxldGUgYmFzZVtrZXldO1xuICAgICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICBkaWUoZXJyb3JPZmZzZXQgKyAxLCBvcCk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIGRyYWZ0O1xuICB9XG4gIGZ1bmN0aW9uIGRlZXBDbG9uZVBhdGNoVmFsdWUob2JqKSB7XG4gICAgaWYgKCFpc0RyYWZ0YWJsZShvYmopKVxuICAgICAgcmV0dXJuIG9iajtcbiAgICBpZiAoQXJyYXkuaXNBcnJheShvYmopKVxuICAgICAgcmV0dXJuIG9iai5tYXAoZGVlcENsb25lUGF0Y2hWYWx1ZSk7XG4gICAgaWYgKGlzTWFwKG9iaikpXG4gICAgICByZXR1cm4gbmV3IE1hcChcbiAgICAgICAgQXJyYXkuZnJvbShvYmouZW50cmllcygpKS5tYXAoKFtrLCB2XSkgPT4gW2ssIGRlZXBDbG9uZVBhdGNoVmFsdWUodildKVxuICAgICAgKTtcbiAgICBpZiAoaXNTZXQob2JqKSlcbiAgICAgIHJldHVybiBuZXcgU2V0KEFycmF5LmZyb20ob2JqKS5tYXAoZGVlcENsb25lUGF0Y2hWYWx1ZSkpO1xuICAgIGNvbnN0IGNsb25lZCA9IE9iamVjdC5jcmVhdGUoZ2V0UHJvdG90eXBlT2Yob2JqKSk7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gb2JqKVxuICAgICAgY2xvbmVkW2tleV0gPSBkZWVwQ2xvbmVQYXRjaFZhbHVlKG9ialtrZXldKTtcbiAgICBpZiAoaGFzKG9iaiwgRFJBRlRBQkxFKSlcbiAgICAgIGNsb25lZFtEUkFGVEFCTEVdID0gb2JqW0RSQUZUQUJMRV07XG4gICAgcmV0dXJuIGNsb25lZDtcbiAgfVxuICBmdW5jdGlvbiBjbG9uZVBhdGNoVmFsdWVJZk5lZWRlZChvYmopIHtcbiAgICBpZiAoaXNEcmFmdChvYmopKSB7XG4gICAgICByZXR1cm4gZGVlcENsb25lUGF0Y2hWYWx1ZShvYmopO1xuICAgIH0gZWxzZVxuICAgICAgcmV0dXJuIG9iajtcbiAgfVxuICBsb2FkUGx1Z2luKFwiUGF0Y2hlc1wiLCB7XG4gICAgYXBwbHlQYXRjaGVzXyxcbiAgICBnZW5lcmF0ZVBhdGNoZXNfLFxuICAgIGdlbmVyYXRlUmVwbGFjZW1lbnRQYXRjaGVzX1xuICB9KTtcbn1cblxuLy8gc3JjL3BsdWdpbnMvbWFwc2V0LnRzXG5mdW5jdGlvbiBlbmFibGVNYXBTZXQoKSB7XG4gIGNsYXNzIERyYWZ0TWFwIGV4dGVuZHMgTWFwIHtcbiAgICBjb25zdHJ1Y3Rvcih0YXJnZXQsIHBhcmVudCkge1xuICAgICAgc3VwZXIoKTtcbiAgICAgIHRoaXNbRFJBRlRfU1RBVEVdID0ge1xuICAgICAgICB0eXBlXzogMiAvKiBNYXAgKi8sXG4gICAgICAgIHBhcmVudF86IHBhcmVudCxcbiAgICAgICAgc2NvcGVfOiBwYXJlbnQgPyBwYXJlbnQuc2NvcGVfIDogZ2V0Q3VycmVudFNjb3BlKCksXG4gICAgICAgIG1vZGlmaWVkXzogZmFsc2UsXG4gICAgICAgIGZpbmFsaXplZF86IGZhbHNlLFxuICAgICAgICBjb3B5Xzogdm9pZCAwLFxuICAgICAgICBhc3NpZ25lZF86IHZvaWQgMCxcbiAgICAgICAgYmFzZV86IHRhcmdldCxcbiAgICAgICAgZHJhZnRfOiB0aGlzLFxuICAgICAgICBpc01hbnVhbF86IGZhbHNlLFxuICAgICAgICByZXZva2VkXzogZmFsc2VcbiAgICAgIH07XG4gICAgfVxuICAgIGdldCBzaXplKCkge1xuICAgICAgcmV0dXJuIGxhdGVzdCh0aGlzW0RSQUZUX1NUQVRFXSkuc2l6ZTtcbiAgICB9XG4gICAgaGFzKGtleSkge1xuICAgICAgcmV0dXJuIGxhdGVzdCh0aGlzW0RSQUZUX1NUQVRFXSkuaGFzKGtleSk7XG4gICAgfVxuICAgIHNldChrZXksIHZhbHVlKSB7XG4gICAgICBjb25zdCBzdGF0ZSA9IHRoaXNbRFJBRlRfU1RBVEVdO1xuICAgICAgYXNzZXJ0VW5yZXZva2VkKHN0YXRlKTtcbiAgICAgIGlmICghbGF0ZXN0KHN0YXRlKS5oYXMoa2V5KSB8fCBsYXRlc3Qoc3RhdGUpLmdldChrZXkpICE9PSB2YWx1ZSkge1xuICAgICAgICBwcmVwYXJlTWFwQ29weShzdGF0ZSk7XG4gICAgICAgIG1hcmtDaGFuZ2VkKHN0YXRlKTtcbiAgICAgICAgc3RhdGUuYXNzaWduZWRfLnNldChrZXksIHRydWUpO1xuICAgICAgICBzdGF0ZS5jb3B5Xy5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIHN0YXRlLmFzc2lnbmVkXy5zZXQoa2V5LCB0cnVlKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBkZWxldGUoa2V5KSB7XG4gICAgICBpZiAoIXRoaXMuaGFzKGtleSkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzW0RSQUZUX1NUQVRFXTtcbiAgICAgIGFzc2VydFVucmV2b2tlZChzdGF0ZSk7XG4gICAgICBwcmVwYXJlTWFwQ29weShzdGF0ZSk7XG4gICAgICBtYXJrQ2hhbmdlZChzdGF0ZSk7XG4gICAgICBpZiAoc3RhdGUuYmFzZV8uaGFzKGtleSkpIHtcbiAgICAgICAgc3RhdGUuYXNzaWduZWRfLnNldChrZXksIGZhbHNlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN0YXRlLmFzc2lnbmVkXy5kZWxldGUoa2V5KTtcbiAgICAgIH1cbiAgICAgIHN0YXRlLmNvcHlfLmRlbGV0ZShrZXkpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGNsZWFyKCkge1xuICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzW0RSQUZUX1NUQVRFXTtcbiAgICAgIGFzc2VydFVucmV2b2tlZChzdGF0ZSk7XG4gICAgICBpZiAobGF0ZXN0KHN0YXRlKS5zaXplKSB7XG4gICAgICAgIHByZXBhcmVNYXBDb3B5KHN0YXRlKTtcbiAgICAgICAgbWFya0NoYW5nZWQoc3RhdGUpO1xuICAgICAgICBzdGF0ZS5hc3NpZ25lZF8gPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgICAgICBlYWNoKHN0YXRlLmJhc2VfLCAoa2V5KSA9PiB7XG4gICAgICAgICAgc3RhdGUuYXNzaWduZWRfLnNldChrZXksIGZhbHNlKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHN0YXRlLmNvcHlfLmNsZWFyKCk7XG4gICAgICB9XG4gICAgfVxuICAgIGZvckVhY2goY2IsIHRoaXNBcmcpIHtcbiAgICAgIGNvbnN0IHN0YXRlID0gdGhpc1tEUkFGVF9TVEFURV07XG4gICAgICBsYXRlc3Qoc3RhdGUpLmZvckVhY2goKF92YWx1ZSwga2V5LCBfbWFwKSA9PiB7XG4gICAgICAgIGNiLmNhbGwodGhpc0FyZywgdGhpcy5nZXQoa2V5KSwga2V5LCB0aGlzKTtcbiAgICAgIH0pO1xuICAgIH1cbiAgICBnZXQoa2V5KSB7XG4gICAgICBjb25zdCBzdGF0ZSA9IHRoaXNbRFJBRlRfU1RBVEVdO1xuICAgICAgYXNzZXJ0VW5yZXZva2VkKHN0YXRlKTtcbiAgICAgIGNvbnN0IHZhbHVlID0gbGF0ZXN0KHN0YXRlKS5nZXQoa2V5KTtcbiAgICAgIGlmIChzdGF0ZS5maW5hbGl6ZWRfIHx8ICFpc0RyYWZ0YWJsZSh2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgfVxuICAgICAgaWYgKHZhbHVlICE9PSBzdGF0ZS5iYXNlXy5nZXQoa2V5KSkge1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICB9XG4gICAgICBjb25zdCBkcmFmdCA9IGNyZWF0ZVByb3h5KHZhbHVlLCBzdGF0ZSk7XG4gICAgICBwcmVwYXJlTWFwQ29weShzdGF0ZSk7XG4gICAgICBzdGF0ZS5jb3B5Xy5zZXQoa2V5LCBkcmFmdCk7XG4gICAgICByZXR1cm4gZHJhZnQ7XG4gICAgfVxuICAgIGtleXMoKSB7XG4gICAgICByZXR1cm4gbGF0ZXN0KHRoaXNbRFJBRlRfU1RBVEVdKS5rZXlzKCk7XG4gICAgfVxuICAgIHZhbHVlcygpIHtcbiAgICAgIGNvbnN0IGl0ZXJhdG9yID0gdGhpcy5rZXlzKCk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBbU3ltYm9sLml0ZXJhdG9yXTogKCkgPT4gdGhpcy52YWx1ZXMoKSxcbiAgICAgICAgbmV4dDogKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHIgPSBpdGVyYXRvci5uZXh0KCk7XG4gICAgICAgICAgaWYgKHIuZG9uZSlcbiAgICAgICAgICAgIHJldHVybiByO1xuICAgICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5nZXQoci52YWx1ZSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGRvbmU6IGZhbHNlLFxuICAgICAgICAgICAgdmFsdWVcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH1cbiAgICBlbnRyaWVzKCkge1xuICAgICAgY29uc3QgaXRlcmF0b3IgPSB0aGlzLmtleXMoKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIFtTeW1ib2wuaXRlcmF0b3JdOiAoKSA9PiB0aGlzLmVudHJpZXMoKSxcbiAgICAgICAgbmV4dDogKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHIgPSBpdGVyYXRvci5uZXh0KCk7XG4gICAgICAgICAgaWYgKHIuZG9uZSlcbiAgICAgICAgICAgIHJldHVybiByO1xuICAgICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5nZXQoci52YWx1ZSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGRvbmU6IGZhbHNlLFxuICAgICAgICAgICAgdmFsdWU6IFtyLnZhbHVlLCB2YWx1ZV1cbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH1cbiAgICBbKERSQUZUX1NUQVRFLCBTeW1ib2wuaXRlcmF0b3IpXSgpIHtcbiAgICAgIHJldHVybiB0aGlzLmVudHJpZXMoKTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gcHJveHlNYXBfKHRhcmdldCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIG5ldyBEcmFmdE1hcCh0YXJnZXQsIHBhcmVudCk7XG4gIH1cbiAgZnVuY3Rpb24gcHJlcGFyZU1hcENvcHkoc3RhdGUpIHtcbiAgICBpZiAoIXN0YXRlLmNvcHlfKSB7XG4gICAgICBzdGF0ZS5hc3NpZ25lZF8gPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgICAgc3RhdGUuY29weV8gPSBuZXcgTWFwKHN0YXRlLmJhc2VfKTtcbiAgICB9XG4gIH1cbiAgY2xhc3MgRHJhZnRTZXQgZXh0ZW5kcyBTZXQge1xuICAgIGNvbnN0cnVjdG9yKHRhcmdldCwgcGFyZW50KSB7XG4gICAgICBzdXBlcigpO1xuICAgICAgdGhpc1tEUkFGVF9TVEFURV0gPSB7XG4gICAgICAgIHR5cGVfOiAzIC8qIFNldCAqLyxcbiAgICAgICAgcGFyZW50XzogcGFyZW50LFxuICAgICAgICBzY29wZV86IHBhcmVudCA/IHBhcmVudC5zY29wZV8gOiBnZXRDdXJyZW50U2NvcGUoKSxcbiAgICAgICAgbW9kaWZpZWRfOiBmYWxzZSxcbiAgICAgICAgZmluYWxpemVkXzogZmFsc2UsXG4gICAgICAgIGNvcHlfOiB2b2lkIDAsXG4gICAgICAgIGJhc2VfOiB0YXJnZXQsXG4gICAgICAgIGRyYWZ0XzogdGhpcyxcbiAgICAgICAgZHJhZnRzXzogLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKSxcbiAgICAgICAgcmV2b2tlZF86IGZhbHNlLFxuICAgICAgICBpc01hbnVhbF86IGZhbHNlXG4gICAgICB9O1xuICAgIH1cbiAgICBnZXQgc2l6ZSgpIHtcbiAgICAgIHJldHVybiBsYXRlc3QodGhpc1tEUkFGVF9TVEFURV0pLnNpemU7XG4gICAgfVxuICAgIGhhcyh2YWx1ZSkge1xuICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzW0RSQUZUX1NUQVRFXTtcbiAgICAgIGFzc2VydFVucmV2b2tlZChzdGF0ZSk7XG4gICAgICBpZiAoIXN0YXRlLmNvcHlfKSB7XG4gICAgICAgIHJldHVybiBzdGF0ZS5iYXNlXy5oYXModmFsdWUpO1xuICAgICAgfVxuICAgICAgaWYgKHN0YXRlLmNvcHlfLmhhcyh2YWx1ZSkpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgaWYgKHN0YXRlLmRyYWZ0c18uaGFzKHZhbHVlKSAmJiBzdGF0ZS5jb3B5Xy5oYXMoc3RhdGUuZHJhZnRzXy5nZXQodmFsdWUpKSlcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGFkZCh2YWx1ZSkge1xuICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzW0RSQUZUX1NUQVRFXTtcbiAgICAgIGFzc2VydFVucmV2b2tlZChzdGF0ZSk7XG4gICAgICBpZiAoIXRoaXMuaGFzKHZhbHVlKSkge1xuICAgICAgICBwcmVwYXJlU2V0Q29weShzdGF0ZSk7XG4gICAgICAgIG1hcmtDaGFuZ2VkKHN0YXRlKTtcbiAgICAgICAgc3RhdGUuY29weV8uYWRkKHZhbHVlKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBkZWxldGUodmFsdWUpIHtcbiAgICAgIGlmICghdGhpcy5oYXModmFsdWUpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHN0YXRlID0gdGhpc1tEUkFGVF9TVEFURV07XG4gICAgICBhc3NlcnRVbnJldm9rZWQoc3RhdGUpO1xuICAgICAgcHJlcGFyZVNldENvcHkoc3RhdGUpO1xuICAgICAgbWFya0NoYW5nZWQoc3RhdGUpO1xuICAgICAgcmV0dXJuIHN0YXRlLmNvcHlfLmRlbGV0ZSh2YWx1ZSkgfHwgKHN0YXRlLmRyYWZ0c18uaGFzKHZhbHVlKSA/IHN0YXRlLmNvcHlfLmRlbGV0ZShzdGF0ZS5kcmFmdHNfLmdldCh2YWx1ZSkpIDogKFxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgICAgICBmYWxzZVxuICAgICAgKSk7XG4gICAgfVxuICAgIGNsZWFyKCkge1xuICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzW0RSQUZUX1NUQVRFXTtcbiAgICAgIGFzc2VydFVucmV2b2tlZChzdGF0ZSk7XG4gICAgICBpZiAobGF0ZXN0KHN0YXRlKS5zaXplKSB7XG4gICAgICAgIHByZXBhcmVTZXRDb3B5KHN0YXRlKTtcbiAgICAgICAgbWFya0NoYW5nZWQoc3RhdGUpO1xuICAgICAgICBzdGF0ZS5jb3B5Xy5jbGVhcigpO1xuICAgICAgfVxuICAgIH1cbiAgICB2YWx1ZXMoKSB7XG4gICAgICBjb25zdCBzdGF0ZSA9IHRoaXNbRFJBRlRfU1RBVEVdO1xuICAgICAgYXNzZXJ0VW5yZXZva2VkKHN0YXRlKTtcbiAgICAgIHByZXBhcmVTZXRDb3B5KHN0YXRlKTtcbiAgICAgIHJldHVybiBzdGF0ZS5jb3B5Xy52YWx1ZXMoKTtcbiAgICB9XG4gICAgZW50cmllcygpIHtcbiAgICAgIGNvbnN0IHN0YXRlID0gdGhpc1tEUkFGVF9TVEFURV07XG4gICAgICBhc3NlcnRVbnJldm9rZWQoc3RhdGUpO1xuICAgICAgcHJlcGFyZVNldENvcHkoc3RhdGUpO1xuICAgICAgcmV0dXJuIHN0YXRlLmNvcHlfLmVudHJpZXMoKTtcbiAgICB9XG4gICAga2V5cygpIHtcbiAgICAgIHJldHVybiB0aGlzLnZhbHVlcygpO1xuICAgIH1cbiAgICBbKERSQUZUX1NUQVRFLCBTeW1ib2wuaXRlcmF0b3IpXSgpIHtcbiAgICAgIHJldHVybiB0aGlzLnZhbHVlcygpO1xuICAgIH1cbiAgICBmb3JFYWNoKGNiLCB0aGlzQXJnKSB7XG4gICAgICBjb25zdCBpdGVyYXRvciA9IHRoaXMudmFsdWVzKCk7XG4gICAgICBsZXQgcmVzdWx0ID0gaXRlcmF0b3IubmV4dCgpO1xuICAgICAgd2hpbGUgKCFyZXN1bHQuZG9uZSkge1xuICAgICAgICBjYi5jYWxsKHRoaXNBcmcsIHJlc3VsdC52YWx1ZSwgcmVzdWx0LnZhbHVlLCB0aGlzKTtcbiAgICAgICAgcmVzdWx0ID0gaXRlcmF0b3IubmV4dCgpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBwcm94eVNldF8odGFyZ2V0LCBwYXJlbnQpIHtcbiAgICByZXR1cm4gbmV3IERyYWZ0U2V0KHRhcmdldCwgcGFyZW50KTtcbiAgfVxuICBmdW5jdGlvbiBwcmVwYXJlU2V0Q29weShzdGF0ZSkge1xuICAgIGlmICghc3RhdGUuY29weV8pIHtcbiAgICAgIHN0YXRlLmNvcHlfID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICAgIHN0YXRlLmJhc2VfLmZvckVhY2goKHZhbHVlKSA9PiB7XG4gICAgICAgIGlmIChpc0RyYWZ0YWJsZSh2YWx1ZSkpIHtcbiAgICAgICAgICBjb25zdCBkcmFmdCA9IGNyZWF0ZVByb3h5KHZhbHVlLCBzdGF0ZSk7XG4gICAgICAgICAgc3RhdGUuZHJhZnRzXy5zZXQodmFsdWUsIGRyYWZ0KTtcbiAgICAgICAgICBzdGF0ZS5jb3B5Xy5hZGQoZHJhZnQpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHN0YXRlLmNvcHlfLmFkZCh2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBhc3NlcnRVbnJldm9rZWQoc3RhdGUpIHtcbiAgICBpZiAoc3RhdGUucmV2b2tlZF8pXG4gICAgICBkaWUoMywgSlNPTi5zdHJpbmdpZnkobGF0ZXN0KHN0YXRlKSkpO1xuICB9XG4gIGxvYWRQbHVnaW4oXCJNYXBTZXRcIiwgeyBwcm94eU1hcF8sIHByb3h5U2V0XyB9KTtcbn1cblxuLy8gc3JjL2ltbWVyLnRzXG52YXIgaW1tZXIgPSBuZXcgSW1tZXIyKCk7XG52YXIgcHJvZHVjZSA9IGltbWVyLnByb2R1Y2U7XG52YXIgcHJvZHVjZVdpdGhQYXRjaGVzID0gaW1tZXIucHJvZHVjZVdpdGhQYXRjaGVzLmJpbmQoXG4gIGltbWVyXG4pO1xudmFyIHNldEF1dG9GcmVlemUgPSBpbW1lci5zZXRBdXRvRnJlZXplLmJpbmQoaW1tZXIpO1xudmFyIHNldFVzZVN0cmljdFNoYWxsb3dDb3B5ID0gaW1tZXIuc2V0VXNlU3RyaWN0U2hhbGxvd0NvcHkuYmluZChpbW1lcik7XG52YXIgYXBwbHlQYXRjaGVzID0gaW1tZXIuYXBwbHlQYXRjaGVzLmJpbmQoaW1tZXIpO1xudmFyIGNyZWF0ZURyYWZ0ID0gaW1tZXIuY3JlYXRlRHJhZnQuYmluZChpbW1lcik7XG52YXIgZmluaXNoRHJhZnQgPSBpbW1lci5maW5pc2hEcmFmdC5iaW5kKGltbWVyKTtcbmZ1bmN0aW9uIGNhc3REcmFmdCh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWU7XG59XG5mdW5jdGlvbiBjYXN0SW1tdXRhYmxlKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZTtcbn1cbmV4cG9ydCB7XG4gIEltbWVyMiBhcyBJbW1lcixcbiAgYXBwbHlQYXRjaGVzLFxuICBjYXN0RHJhZnQsXG4gIGNhc3RJbW11dGFibGUsXG4gIGNyZWF0ZURyYWZ0LFxuICBjdXJyZW50LFxuICBlbmFibGVNYXBTZXQsXG4gIGVuYWJsZVBhdGNoZXMsXG4gIGZpbmlzaERyYWZ0LFxuICBmcmVlemUsXG4gIERSQUZUQUJMRSBhcyBpbW1lcmFibGUsXG4gIGlzRHJhZnQsXG4gIGlzRHJhZnRhYmxlLFxuICBOT1RISU5HIGFzIG5vdGhpbmcsXG4gIG9yaWdpbmFsLFxuICBwcm9kdWNlLFxuICBwcm9kdWNlV2l0aFBhdGNoZXMsXG4gIHNldEF1dG9GcmVlemUsXG4gIHNldFVzZVN0cmljdFNoYWxsb3dDb3B5XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW1tZXIubWpzLm1hcCJdLCJuYW1lcyI6WyJOT1RISU5HIiwiU3ltYm9sIiwiZm9yIiwiRFJBRlRBQkxFIiwiRFJBRlRfU1RBVEUiLCJlcnJvcnMiLCJwcm9jZXNzIiwicGx1Z2luIiwidGhpbmciLCJkYXRhIiwiZGllIiwiZXJyb3IiLCJhcmdzIiwiZSIsIm1zZyIsImFwcGx5IiwiRXJyb3IiLCJnZXRQcm90b3R5cGVPZiIsIk9iamVjdCIsImlzRHJhZnQiLCJ2YWx1ZSIsImlzRHJhZnRhYmxlIiwiaXNQbGFpbk9iamVjdCIsIkFycmF5IiwiaXNBcnJheSIsImNvbnN0cnVjdG9yIiwiaXNNYXAiLCJpc1NldCIsIm9iamVjdEN0b3JTdHJpbmciLCJwcm90b3R5cGUiLCJ0b1N0cmluZyIsInByb3RvIiwiQ3RvciIsImhhc093blByb3BlcnR5IiwiY2FsbCIsIkZ1bmN0aW9uIiwib3JpZ2luYWwiLCJiYXNlXyIsImVhY2giLCJvYmoiLCJpdGVyIiwiZ2V0QXJjaHR5cGUiLCJlbnRyaWVzIiwiZm9yRWFjaCIsImtleSIsImVudHJ5IiwiaW5kZXgiLCJzdGF0ZSIsInR5cGVfIiwiaGFzIiwicHJvcCIsImdldCIsInNldCIsInByb3BPck9sZFZhbHVlIiwidCIsImFkZCIsImlzIiwieCIsInkiLCJ0YXJnZXQiLCJNYXAiLCJTZXQiLCJsYXRlc3QiLCJjb3B5XyIsInNoYWxsb3dDb3B5IiwiYmFzZSIsInN0cmljdCIsInNsaWNlIiwiY3JlYXRlIiwiYXNzaWduIiwiZGVzY3JpcHRvcnMiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzIiwia2V5cyIsIlJlZmxlY3QiLCJvd25LZXlzIiwiaSIsImxlbmd0aCIsImRlc2MiLCJ3cml0YWJsZSIsImNvbmZpZ3VyYWJsZSIsImVudW1lcmFibGUiLCJmcmVlemUiLCJkZWVwIiwiaXNGcm96ZW4iLCJjbGVhciIsImRlbGV0ZSIsImRvbnRNdXRhdGVGcm96ZW5Db2xsZWN0aW9ucyIsIl9rZXkiLCJwbHVnaW5zIiwiZ2V0UGx1Z2luIiwicGx1Z2luS2V5IiwibG9hZFBsdWdpbiIsImltcGxlbWVudGF0aW9uIiwiY3VycmVudFNjb3BlIiwiZ2V0Q3VycmVudFNjb3BlIiwiY3JlYXRlU2NvcGUiLCJwYXJlbnRfIiwiaW1tZXJfIiwiZHJhZnRzXyIsImNhbkF1dG9GcmVlemVfIiwidW5maW5hbGl6ZWREcmFmdHNfIiwidXNlUGF0Y2hlc0luU2NvcGUiLCJzY29wZSIsInBhdGNoTGlzdGVuZXIiLCJwYXRjaGVzXyIsImludmVyc2VQYXRjaGVzXyIsInBhdGNoTGlzdGVuZXJfIiwicmV2b2tlU2NvcGUiLCJsZWF2ZVNjb3BlIiwicmV2b2tlRHJhZnQiLCJlbnRlclNjb3BlIiwiaW1tZXIyIiwiZHJhZnQiLCJyZXZva2VfIiwicmV2b2tlZF8iLCJwcm9jZXNzUmVzdWx0IiwicmVzdWx0IiwiYmFzZURyYWZ0IiwiaXNSZXBsYWNlZCIsIm1vZGlmaWVkXyIsImZpbmFsaXplIiwibWF5YmVGcmVlemUiLCJnZW5lcmF0ZVJlcGxhY2VtZW50UGF0Y2hlc18iLCJyb290U2NvcGUiLCJwYXRoIiwiY2hpbGRWYWx1ZSIsImZpbmFsaXplUHJvcGVydHkiLCJzY29wZV8iLCJmaW5hbGl6ZWRfIiwicmVzdWx0RWFjaCIsImlzU2V0MiIsImdlbmVyYXRlUGF0Y2hlc18iLCJwYXJlbnRTdGF0ZSIsInRhcmdldE9iamVjdCIsInJvb3RQYXRoIiwidGFyZ2V0SXNTZXQiLCJhc3NpZ25lZF8iLCJjb25jYXQiLCJyZXMiLCJhdXRvRnJlZXplXyIsImNyZWF0ZVByb3h5UHJveHkiLCJwYXJlbnQiLCJkcmFmdF8iLCJpc01hbnVhbF8iLCJ0cmFwcyIsIm9iamVjdFRyYXBzIiwiYXJyYXlUcmFwcyIsInJldm9rZSIsInByb3h5IiwiUHJveHkiLCJyZXZvY2FibGUiLCJzb3VyY2UiLCJyZWFkUHJvcEZyb21Qcm90byIsInBlZWsiLCJwcmVwYXJlQ29weSIsImNyZWF0ZVByb3h5IiwiZ2V0RGVzY3JpcHRvckZyb21Qcm90byIsImN1cnJlbnQyIiwiY3VycmVudFN0YXRlIiwibWFya0NoYW5nZWQiLCJOdW1iZXIiLCJpc05hTiIsImRlbGV0ZVByb3BlcnR5IiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwib3duZXIiLCJkZWZpbmVQcm9wZXJ0eSIsInNldFByb3RvdHlwZU9mIiwiZm4iLCJhcmd1bWVudHMiLCJwYXJzZUludCIsInVzZVN0cmljdFNoYWxsb3dDb3B5XyIsIkltbWVyMiIsImNvbmZpZyIsInByb2R1Y2UiLCJyZWNpcGUiLCJkZWZhdWx0QmFzZSIsInNlbGYiLCJjdXJyaWVkUHJvZHVjZSIsImJhc2UyIiwiaGFzRXJyb3IiLCJwIiwiaXAiLCJwcm9kdWNlV2l0aFBhdGNoZXMiLCJwYXRjaGVzIiwiaW52ZXJzZVBhdGNoZXMiLCJhdXRvRnJlZXplIiwic2V0QXV0b0ZyZWV6ZSIsInVzZVN0cmljdFNoYWxsb3dDb3B5Iiwic2V0VXNlU3RyaWN0U2hhbGxvd0NvcHkiLCJjcmVhdGVEcmFmdCIsImN1cnJlbnQiLCJmaW5pc2hEcmFmdCIsImFwcGx5UGF0Y2hlcyIsInBhdGNoIiwib3AiLCJhcHBseVBhdGNoZXNJbXBsIiwiYXBwbHlQYXRjaGVzXyIsInByb3h5TWFwXyIsInByb3h5U2V0XyIsInB1c2giLCJjdXJyZW50SW1wbCIsImNvcHkiLCJlbmFibGVQYXRjaGVzIiwiZXJyb3JPZmZzZXQiLCJSRVBMQUNFIiwiQUREIiwiUkVNT1ZFIiwiYmFzZVBhdGgiLCJnZW5lcmF0ZVBhdGNoZXNGcm9tQXNzaWduZWQiLCJnZW5lcmF0ZUFycmF5UGF0Y2hlcyIsImdlbmVyYXRlU2V0UGF0Y2hlcyIsImNsb25lUGF0Y2hWYWx1ZUlmTmVlZGVkIiwiYXNzaWduZWRWYWx1ZSIsIm9yaWdWYWx1ZSIsInVuc2hpZnQiLCJiYXNlVmFsdWUiLCJyZXBsYWNlbWVudCIsInBhcmVudFR5cGUiLCJqb2luIiwidHlwZSIsImRlZXBDbG9uZVBhdGNoVmFsdWUiLCJzcGxpY2UiLCJtYXAiLCJmcm9tIiwiayIsInYiLCJjbG9uZWQiLCJlbmFibGVNYXBTZXQiLCJEcmFmdE1hcCIsInNpemUiLCJhc3NlcnRVbnJldm9rZWQiLCJwcmVwYXJlTWFwQ29weSIsImNiIiwidGhpc0FyZyIsIl92YWx1ZSIsIl9tYXAiLCJ2YWx1ZXMiLCJpdGVyYXRvciIsIm5leHQiLCJyIiwiZG9uZSIsIkRyYWZ0U2V0IiwicHJlcGFyZVNldENvcHkiLCJKU09OIiwic3RyaW5naWZ5IiwiaW1tZXIiLCJiaW5kIiwiY2FzdERyYWZ0IiwiY2FzdEltbXV0YWJsZSIsIkltbWVyIiwiaW1tZXJhYmxlIiwibm90aGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/immer/dist/immer.mjs\n");

/***/ })

};
;