'use client';

import { Box, Tab, Tabs } from '@mui/material';
import React, { useState } from 'react';
import { TabPanel } from '..';
import { TabItem } from './models';

interface Props {
  tabs: TabItem[];
}

export const TabContainer = ({ tabs }: Props) => {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={selectedTab}
          onChange={handleChange}
          data-testid="tabSection-testId"
          aria-label="basic tabs example"
          centered
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.id}
              label={tab.label}
              icon={tab?.icon}
              iconPosition="start"
              data-testid={tab.dataTestId}
            />
          ))}
        </Tabs>
      </Box>
      {tabs.map((tab, index) => (
        <TabPanel key={tab.id} selectedTab={selectedTab} index={index}>
          {tab?.content}
        </TabPanel>
      ))}
    </Box>
  );
};
