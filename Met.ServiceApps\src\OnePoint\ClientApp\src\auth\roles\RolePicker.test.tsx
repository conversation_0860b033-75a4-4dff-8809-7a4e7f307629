import '@testing-library/jest-dom';
import { renderWithRoleProvider } from '@/../__test-utils__/mockRoleProvider';
import { fireEvent, screen } from '@testing-library/react';
import { RolePicker } from './RolePicker';
import { RoleState } from './RoleProvider';
import { Role } from './role';

describe('RolePicker', () => {
  it('allows changing user role', () => {
    const state: RoleState = {
      isAdmin: true,
      roles: Object.values(Role).filter((role) => role !== Role.Admin),
      currentRole: Role.BranchManager,
      setCurrentRole: jest.fn()
    };
    const onChange = jest.fn();

    renderWithRoleProvider(
      <RolePicker onChange={onChange} data-testid="rolePickerTest" />,
      state
    );

    expect(screen.getByTestId('rolePickerSelect')).toHaveTextContent(
      'Branch Manager'
    );

    fireEvent.mouseDown(screen.getByRole('combobox'));

    const roleOptions = Object.fromEntries(
      state.roles.map((role) => [
        role,
        screen.getByTestId(`rolePickerMenuItem${role}`)
      ])
    );
    expect(roleOptions[Role.BranchManager]).toHaveTextContent('Branch Manager');
    expect(roleOptions[Role.BranchAssociate]).toHaveTextContent(
      'Branch Associate'
    );
    expect(roleOptions[Role.Hub]).toHaveTextContent('Hub');
    expect(roleOptions[Role.CX]).toHaveTextContent('CX');
    expect(roleOptions[Role.JSS]).toHaveTextContent('JSS');
    expect(roleOptions[Role.DigitalCare]).toHaveTextContent('Digital Care');
    expect(roleOptions[Role.ProductMarketing]).toHaveTextContent(
      'Product Marketing'
    );

    fireEvent.click(roleOptions[Role.BranchAssociate]);

    expect(onChange).toHaveBeenCalledTimes(1);
    expect(state.setCurrentRole).toHaveBeenCalledWith(Role.BranchAssociate);
  });

  it('does not render when user has only one role', () => {
    const state: RoleState = {
      isAdmin: true,
      roles: [Role.BranchAssociate],
      currentRole: Role.BranchAssociate,
      setCurrentRole: jest.fn()
    };

    renderWithRoleProvider(
      <RolePicker onChange={jest.fn()} data-testid="rolePickerTest" />,
      state
    );

    expect(screen.queryByTestId('rolePickerTest')).not.toBeInTheDocument();
  });
});
