import { ArrowLeftIcon, CloseIcon } from '@/design/atoms';
import { PrintSVButtonAndModal } from '@/features/repair';
import { Box, IconButton, Typography } from '@mui/material';
import { PageHeaderContainer } from './PageHeaderContainer';

interface PageHeaderContainerWrapperProps {
  handleBackIconClick?: () => void;
  handleCloseIconClick?: () => void;
  serviceRequestNumber?: string;
  pageTitle?: string;
}

export const PageHeaderContainerWrapper = (
  props: PageHeaderContainerWrapperProps
) => {
  return (
    <PageHeaderContainer
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}
    >
      <Box
        data-testid="goBack-testId"
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        <>
          <IconButton
            onClick={props.handleBackIconClick}
            data-testid="goBackButton-testId"
          >
            <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
          </IconButton>
          <Typography variant="h5" color="neutral.800" textAlign="center">
            {props.pageTitle}
          </Typography>
        </>
      </Box>
      {props.serviceRequestNumber && (
        <Box
          sx={{
            display: 'flex',
            flex: 'auto',
            justifyContent: 'flex-end'
          }}
        >
          <PrintSVButtonAndModal
            svNumber={props.serviceRequestNumber}
            variant="tertiary"
          />
        </Box>
      )}
      <IconButton
        data-testid="closeBtn-testId"
        onClick={props.handleCloseIconClick}
      >
        <CloseIcon />
      </IconButton>
    </PageHeaderContainer>
  );
};
