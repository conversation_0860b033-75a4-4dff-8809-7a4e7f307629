'use client';

import { AppInsightsErrorBoundary } from '@microsoft/applicationinsights-react-js';
import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { JSXElementConstructor, ReactElement } from 'react';
import { reactPlugin } from './AppInsightsProvider';

interface Props {
  customError?: ReactElement;
  children: ReactElement<unknown, string | JSXElementConstructor<unknown>>;
}

export const ErrorBoundary = ({ children, customError }: Props) => {
  const { t } = useTranslation();

  const fallbackError = () => {
    return (
      <Grid
        container
        spacing={0}
        direction="column"
        alignItems="center"
        justifyContent="center"
        sx={{ minHeight: '100vh' }}
      >
        <Grid item xs={3}>
          <Typography variant="h3">
            {t('appInsights.errorBoundary.genericError')}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  return (
    <AppInsightsErrorBoundary
      appInsights={reactPlugin}
      onError={() => (customError ? customError : fallbackError())}
    >
      {children}
    </AppInsightsErrorBoundary>
  );
};
