'use client';

import { useSelectedLanguage } from 'next-export-i18n';
import React, { useEffect } from 'react';
import { setLanguage } from './helpers/languageContextHelper';

interface Props {
  children: React.ReactNode;
}

export const LanguageProvider = ({ children }: Props) => {
  const { lang } = useSelectedLanguage();
  useEffect(() => {
    if (lang) {
      setLanguage(lang);
    }
  }, [lang]);

  return <>{children}</>;
};
