import { EnvelopeIcon } from '@/design/atoms';
import {
  FormControl,
  FormControlProps,
  FormHelperText,
  IconButton,
  InputAdornment,
  InputLabel,
  Select as MuiSelect,
  SxProps
} from '@mui/material';
import { ReactNode } from 'react';
import {
  SelectFormControlVariant,
  mergeSelectFormControlProps
} from './variants';

interface Props extends Omit<FormControlProps, 'variant'> {
  name?: string;
  variant?: SelectFormControlVariant;
  inputLabel: ReactNode;
  children: ReactNode;
  value: string;
  onValueChange: (value: string) => void;
  sx?: SxProps;
  isIconVisible?: boolean;
  onClick?: () => void;
  helperText?: string | undefined;
  iconComponent?: React.ElementType;
}

export const SelectFormControl = ({
  name = 'default',
  variant = 'standard',
  inputLabel,
  children,
  value,
  onValueChange,
  isIconVisible,
  onClick,
  helperText,
  iconComponent,
  ...rest
}: Props) => {
  return (
    <FormControl
      {...mergeSelectFormControlProps(variant, rest)}
      size="small"
      fullWidth
    >
      <InputLabel>{inputLabel}</InputLabel>
      <MuiSelect
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        onOpen={() => (onClick ? onClick() : null)}
        data-testid={`select-${name}-testId`}
        IconComponent={iconComponent}
        startAdornment={
          isIconVisible ? (
            <InputAdornment position="start" data-testid="envelopeIcon">
              <IconButton style={{ width: 24, height: 24 }} tabIndex={-1}>
                <EnvelopeIcon />
              </IconButton>
            </InputAdornment>
          ) : null
        }
        MenuProps={{
          PaperProps: { sx: { maxHeight: 350 } }
        }}
      >
        {children}
      </MuiSelect>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};
