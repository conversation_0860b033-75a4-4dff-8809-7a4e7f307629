import { LIGHT_PALETTE } from '@/design/atoms';
import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';

export const QuestionBlockSearchIcon = (props: SvgIconProps) => {
  const primary500 =
    LIGHT_PALETTE.primary?.[500 as keyof typeof LIGHT_PALETTE.primary];
  const primary700 =
    LIGHT_PALETTE.primary?.[700 as keyof typeof LIGHT_PALETTE.primary];
  const white = LIGHT_PALETTE.common?.white;
  return (
    <SvgIcon {...props}>
      <svg
        width="54"
        height="38"
        viewBox="0 0 54 38"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_6884_23)">
          <path
            d="M27.9826 23.8566C27.9826 23.4721 27.9994 23.1043 28.033 22.7532H28.0162C27.9826 23.0708 27.9658 23.4052 27.9658 23.7395C27.9658 24.1909 27.9658 24.6256 28.0498 25.0603C27.9994 24.659 27.9826 24.2578 27.9826 23.8566ZM53.4794 34.974L47.6343 29.1562C48.6588 27.6516 49.2635 25.8293 49.2635 23.8733C49.2635 18.6573 45.0308 14.4443 39.7904 14.4443C34.55 14.4443 30.3341 18.6573 30.3341 23.8733C30.3341 29.0893 34.5668 33.2855 39.7904 33.2855H39.8072C41.7724 33.2688 43.6031 32.6669 45.1148 31.6472L50.9599 37.465C51.6654 38.1672 52.7907 38.1672 53.4794 37.465C54.168 36.7796 54.168 35.6595 53.4794 34.974ZM39.8072 29.7413C36.5319 29.7413 33.8949 27.1166 33.8949 23.8566C33.8949 20.5966 36.5319 17.9718 39.8072 17.9718C43.0825 17.9718 45.7195 20.6133 45.7195 23.8566C45.7195 27.0999 43.0657 29.7413 39.8072 29.7413Z"
            fill={primary700}
          />
          <path
            d="M28.5874 27.5178C28.9569 28.7215 29.5448 29.8249 30.3006 30.828C30.3006 30.8113 30.3174 30.8113 30.3174 30.7946C29.5784 29.8082 28.9905 28.7048 28.5874 27.5178ZM33.1896 33.553C33.1896 33.553 33.156 33.5697 33.1392 33.5865C34.3989 34.4391 35.8266 35.0576 37.3886 35.3753C37.4054 35.3753 37.4054 35.3586 37.4054 35.3586C35.8602 35.0242 34.4325 34.4056 33.1896 33.553Z"
            fill={primary500}
          />
          <path
            d="M38.1779 12.0202V5.41663C38.1779 2.42411 35.7257 0 32.7191 0H5.44199C2.43546 0 0 2.42411 0 5.41663V32.5667C0 35.5592 2.43546 38 5.44199 38H32.7191C32.7191 38 32.8871 38 32.9711 37.9833C34.8355 37.9164 36.4815 36.8632 37.3885 35.3753C35.8264 35.0576 34.3988 34.4391 33.139 33.5865C32.0473 32.8341 31.0731 31.8979 30.3005 30.828C29.5446 29.8249 28.9568 28.7215 28.5872 27.5178C28.4865 27.2503 28.4025 26.9828 28.3521 26.7154C28.3353 26.6652 28.3185 26.6318 28.3185 26.5816C28.2345 26.3141 28.1841 26.0466 28.1505 25.7624C28.1002 25.5284 28.0666 25.2943 28.0498 25.0603C27.9826 24.6256 27.9658 24.1909 27.9658 23.7396C27.9658 23.4052 27.9658 23.0708 28.0162 22.7532C28.4697 17.2363 32.7191 12.7893 38.1779 12.0202ZM32.0305 4.06247C33.1726 4.06247 34.0796 4.98196 34.0796 6.10207C34.0796 7.22217 33.1726 8.14166 32.0305 8.14166C30.8883 8.14166 29.9981 7.22217 29.9981 6.10207C29.9981 4.98196 30.9051 4.06247 32.0305 4.06247ZM6.83608 33.7871C6.61773 33.8707 6.38258 33.9208 6.13064 33.9208C5.00529 33.9208 4.08149 33.018 4.08149 31.8812C4.08149 30.7444 5.00529 29.8583 6.13064 29.8583C6.78569 29.8583 7.37356 30.1593 7.74308 30.6441C8.01182 30.9784 8.17978 31.4131 8.17978 31.8812C8.17978 32.7673 7.62551 33.5196 6.83608 33.7871ZM6.13064 8.14166C5.00529 8.14166 4.08149 7.22217 4.08149 6.10207C4.08149 4.98196 5.00529 4.06247 6.13064 4.06247C7.25599 4.06247 8.17978 4.98196 8.17978 6.10207C8.17978 7.22217 7.25599 8.14166 6.13064 8.14166ZM19.0806 31.2125C17.5689 31.2125 16.3596 29.9921 16.3596 28.4875C16.3596 26.9828 17.5689 25.7791 19.0806 25.7791C20.5922 25.7791 21.8016 26.9996 21.8016 28.4875C21.8016 29.9754 20.5922 31.2125 19.0806 31.2125ZM25.0432 19.0919L21.1297 21.4993V21.7167C21.1297 22.0678 21.0457 22.3854 20.8778 22.6696C20.5418 23.3049 19.87 23.7396 19.0806 23.7396C18.4087 23.7396 17.7872 23.4052 17.4177 22.9037C17.1826 22.5693 17.0314 22.1681 17.0314 21.7167V20.3458C17.0314 19.6436 17.4177 18.9749 18.0392 18.6071L22.9437 15.6648C23.4308 15.3304 23.8507 14.7286 23.8507 13.993C23.8507 13.04 23.0277 12.2041 21.9863 12.2041L17.0314 12.1373C16.2924 12.1373 15.6709 12.8227 15.6709 13.4914V14.1601C15.6709 15.3806 14.7639 16.1997 13.6218 16.1997C12.4796 16.1997 11.5894 15.3806 11.5894 14.1601V13.4914C11.5894 10.5658 14.0249 8.05807 17.0314 8.05807H21.9863C25.2784 8.05807 27.949 10.7998 27.949 13.993C27.949 16.1496 26.8404 18.0889 25.0432 19.0919Z"
            fill={primary500}
          />
        </g>
        <defs>
          <clipPath id="clip0_6884_23">
            <rect width="54" height="38" fill={white} />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};
