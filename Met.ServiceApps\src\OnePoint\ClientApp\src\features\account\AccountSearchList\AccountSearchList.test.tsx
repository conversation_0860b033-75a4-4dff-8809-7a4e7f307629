import { customerStore } from '@/../__test-utils__/customerStore';
import { AccountSearchList } from '@/features/account';
import { PagedListDetails } from '@/features/search';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';

import { ParentAccount, Site, SiteItem } from '@/features/account';
import '@testing-library/jest-dom';

const mockParentAccount: ParentAccount = {
  id: '1',
  accountNumber: '12345',
  accountDescription: 'Example Account',
  customerClass: 'A',
  ascFlatRate: 'FlatRate',
  accountEstablishedDate: { seconds: 0, nanos: 0 },
  accountTerminationDate: { seconds: 0, nanos: 0 },
  heldBillExpirationDate: { seconds: 0, nanos: 0 },
  holdBillIndicator: true,
  npaNumber: 'NPA123',
  sourceCode: 'SRC',
  autopayFlag: true,
  ripperParent: 'Parent',
  allowDropShip: true,
  paymentMethod: 'CreditCard',
  costCenter: 'CostCenter1',
  priceTolerance: 'Tolerance1',
  updatedAt: { seconds: 0, nanos: 0 }
};

const mockSites: Site[] = Array.from({ length: 3 }, (_, index) => ({
  id: `site${index + 1}`,
  carrierAccountNumber: `Carrier${index + 1}`,
  deliveryTerms: 'Terms',
  priceSchedule: 'Schedule',
  reasonCode: 'Reason',
  priorSiteNumber: `Prior${index + 1}`,
  priorAccountNumber: `PriorAcc${index + 1}`,
  salesHoldCode: 'Hold',
  gtmScreening: 'Screening',
  paymentTerms: 'Terms',
  freightForwarder: 'Forwarder',
  applyFreight: true,
  siteNumber: `Site${index + 1}`,
  siteName: `SiteName${index + 1}`,
  country: 'Country',
  addressLine1: 'Address1',
  addressLine2: 'Address2',
  city: 'City',
  county: 'County',
  state: 'State',
  province: 'Province',
  postalCode: 'PostalCode',
  shortDescription: 'ShortDesc',
  originalAccountSite: 'OriginalSite',
  accountAddressSet: 'AddressSet',
  effectiveFromDate: { seconds: 0, nanos: 0 },
  effectiveEndDate: { seconds: 0, nanos: 0 },
  eoriNumber: 'EORI',
  mailstop: 'Mailstop',
  locationId: 'LocationID',
  comments: 'Comments',
  description: 'Description',
  primary: true,
  addressLine3: 'Address3',
  addressLine4: 'Address4',
  countryCode: 'CountryCode',
  identifyingAddress: true,
  taxGeoCode: 'TaxGeo',
  taxInsideCityLimits: true,
  language: 'Language',
  updatedAt: { seconds: 0, nanos: 0 },
  accountType: 'Type',
  midParent: 'MidParent',
  salesRegion: 'Region',
  salesDivision: 'Division',
  incoterms: 'Incoterms',
  keyAccountId: 'KeyAccount',
  rebateProgram: 'Rebate',
  websiteInclusionOverrideFlag: 'Override',
  serviceProgram: 'Service',
  serviceManager: 'Manager',
  allowBackorders: true,
  allowPartialLineShipments: true,
  allowPartialOrderShipments: true,
  replacementAllowed: 'Allowed',
  allowSpecialPaymentTerms: true,
  freightGroup: 'FreightGroup',
  warehouse: 'Warehouse',
  shipmentPriority: 'Priority',
  reviewWarrantyClaims: true,
  affiliationLink: 'Link',
  anonymousCustomer: true,
  internalNote: 'InternalNote',
  deliveryNote: 'DeliveryNote',
  externalNote: 'ExternalNote',
  typeOfBusiness: 'BusinessType',
  financialDimensionsDg0Override: 'Override0',
  financialDimensionsDg1Override: 'Override1',
  financialDimensionsDg2Override: 'Override2',
  financialDimensionsDg3Override: 'Override3',
  financialDimensionsDg4Override: 'Override4',
  financialDimensionsDg5Override: 'Override5',
  customerCategoryCode: 'CategoryCode',
  ediLocationCode: 'EDILocation',
  keyAccountFlag: 'KeyFlag',
  statusPermissions: 'Permissions',
  customerSourceReference: 'SourceRef',
  tradingPartnerId: 'PartnerID',
  historicalAddressLine1: 'HistAddress1',
  historicalAddressLine2: 'HistAddress2',
  historicalAddressLine3: 'HistAddress3',
  historicalAddressLine4: 'HistAddress4',
  historicalAddressEffectiveEndDate: { seconds: 0, nanos: 0 },
  priceScheduleDescription: 'PriceDesc',
  siteUses: [],
  siteProfiles: [],
  siteResponsibilities: [],
  accountProfiles: [],
  accountResponsibilities: [],
  contacts: [],
  contactPoints: []
}));

export const mockSiteItems: SiteItem[] = mockSites.map((site) => ({
  ...site,
  parentAccount: mockParentAccount
}));
const paginationPagedData: PagedListDetails = {
  page: 1,
  totalItems: 3,
  totalPages: 1
};

const renderWithProvider = (component: JSX.Element) => {
  return render(<Provider store={customerStore()}>{component}</Provider>);
};

describe('AccountSearchList component tests', () => {
  const mockOnLoadMoreClick = jest.fn();

  it('Renders account items correctly', () => {
    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        isFetching={false}
        isViewMode={false}
        valueNotFound={false}
        items={mockSiteItems}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    mockSiteItems.forEach((item) => {
      expect(
        screen.getByTestId(`parentAccountName-${item.id}-testId`)
      ).toHaveTextContent(item.parentAccount.accountDescription);
      expect(
        screen.getByTestId(`parentAccountNumber-${item.id}-testId`)
      ).toHaveTextContent(item.parentAccount.accountNumber);
    });
  });

  it('Displays no records found text when items are not found', () => {
    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        isFetching={false}
        isViewMode={false}
        valueNotFound={true}
        items={[]}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    expect(screen.getByTestId('noRecordsFoundText-testId')).toBeInTheDocument();
  });

  it('Does not render anything when isListOpened is false', () => {
    const { container } = renderWithProvider(
      <AccountSearchList
        isListOpened={false}
        isFetching={true}
        isViewMode={false}
        valueNotFound={false}
        items={[]}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    expect(container.firstChild?.firstChild).toBeNull();
  });

  it('Renders loading spinner when is fetching', () => {
    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        isFetching={true}
        items={[]}
        isViewMode={true}
        valueNotFound={false}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    const loadingSpinner = screen.getByTestId('loadingSpinner-testId');
    expect(loadingSpinner).toBeInTheDocument();
  });

  it('Renders account not found when value not found', () => {
    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        items={[]}
        valueNotFound={true}
        isViewMode={true}
        isFetching={false}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    const icon = screen.getByTestId('noRecordsFoundIcon-testId');
    const notFoundLabel = screen.getByTestId('noRecordsFoundText-testId');

    expect(icon).toBeInTheDocument();
    expect(notFoundLabel).toHaveTextContent('No records found');
  });

  it('Displays alert when totalItems is greater than 50', () => {
    const paginationPagedData: PagedListDetails = {
      page: 1,
      totalItems: 51,
      totalPages: 6
    };

    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        isFetching={false}
        isViewMode={false}
        valueNotFound={false}
        items={mockSiteItems}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={true}
      />
    );

    const alertContainer = screen.getByTestId(
      'searchResultsMoreThan50AlertContainer-testId'
    );
    expect(alertContainer).toBeVisible();
  });

  it('Does not display alert when totalItems is 50 or less', () => {
    const paginationPagedData: PagedListDetails = {
      page: 1,
      totalItems: 50,
      totalPages: 5
    };

    renderWithProvider(
      <AccountSearchList
        isListOpened={true}
        isFetching={false}
        isViewMode={false}
        valueNotFound={false}
        items={mockSiteItems}
        onAccountSelected={jest.fn()}
        onLoadMoreClick={mockOnLoadMoreClick}
        paginationData={paginationPagedData}
        hasTooManySitesResults={false}
      />
    );

    const alertContainer = screen.queryByTestId(
      'searchResultsMoreThan50AlertContainer-testId'
    );
    expect(alertContainer).not.toBeInTheDocument();
  });
});
