'use client';
import { unstable_noStore as noStore } from 'next/cache';
import { ReactNode, createContext, useContext } from 'react';

interface EnvironmentConfig {
  appInsightsConnectionString?: string;
  clientId?: string;
}

export const EnvContext = createContext<EnvironmentConfig>({});

interface EnvProviderProps {
  children: ReactNode;
  config: EnvironmentConfig;
}

export const EnvProvider = ({ children, config }: EnvProviderProps) => {
  noStore();
  return <EnvContext.Provider value={config}>{children}</EnvContext.Provider>;
};

export const useEnvContext = () => useContext(EnvContext);
