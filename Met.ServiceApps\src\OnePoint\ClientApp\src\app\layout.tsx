import { EnvProvider } from '@/env';
import { Metadata, Viewport } from 'next';
import { App } from './App';

export const metadata: Metadata = {
  title: 'OnePoint'
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1
};

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children
}: {
  children: React.ReactNode;
}) {
  const config = {
    appInsightsConnectionString:
      process.env.APPLICATIONINSIGHTS_CONNECTION_STRING,
    clientId: process.env.CLIENT_ID
  };
  return (
    <html lang="en">
      <body>
        <EnvProvider config={config}>
          <App>{children}</App>
        </EnvProvider>
      </body>
    </html>
  );
}
