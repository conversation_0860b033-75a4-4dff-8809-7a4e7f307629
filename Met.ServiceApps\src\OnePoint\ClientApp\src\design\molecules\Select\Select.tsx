'use client';

import { SelectFormControl } from '@/design/molecules';
import {
  FormControlOwnProps,
  MenuItem,
  SxProps,
  Typography
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'next-export-i18n';
import { Control, Controller } from 'react-hook-form';

export enum SelectDefaultOption {
  All = 'All'
}

export interface SelectItem {
  id: string;
  value: string;
  disabled?: boolean;
}

interface SelectHoCProps {
  items: SelectItem[];
  name: string;
  value?: string;
  error: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control?: Control<any>;
  required?: boolean;
  placeHolder?: string;
  labelText?: string;
  variant?: FormControlOwnProps['variant'];
  onSelectChange?: (value: string) => void;
  sx?: SxProps;
  isIconVisible?: boolean;
  onClick?: () => void;
  withBorder?: boolean;
  helperText?: string | undefined;
  iconComponent?: React.ElementType;
  disabled?: boolean;
}

const SelectInternal = (props: SelectHoCProps) => {
  const {
    name,
    items,
    value,
    required,
    error,
    labelText,
    onSelectChange,
    sx,
    variant = 'filled',
    isIconVisible,
    onClick,
    withBorder,
    helperText,
    iconComponent,
    disabled = false
  } = props;
  const { t } = useTranslation();
  const asterik = t('common.required');
  const theme = useTheme();

  return (
    <SelectFormControl
      data-testid="selectFormControl-testId"
      name={name}
      variant={variant}
      value={value ?? ''}
      error={error}
      sx={
        sx ?? {
          '.MuiSvgIcon-root': {
            color: 'neutral.800'
          }
        }
      }
      iconComponent={iconComponent}
      onValueChange={(e) => {
        if (onSelectChange) {
          onSelectChange(e);
        }
      }}
      inputLabel={
        <Typography
          variant="p1"
          fontWeight={400}
          color="inherit"
          sx={{
            marginLeft: isIconVisible ? '44px' : 'inherit',
            width: isIconVisible ? '300px' : 'inherit'
          }}
        >
          {labelText}
          {required && ` ${asterik}`}
        </Typography>
      }
      isIconVisible={isIconVisible}
      onClick={onClick}
      helperText={helperText}
      disabled={disabled}
    >
      {items.map((item) => (
        <MenuItem
          data-testid={`${name}-select-option-${item.id}`}
          key={`${name}-select-option-${item.id}`}
          value={item.id}
          disabled={item.disabled}
          sx={{
            border: withBorder ? `1px solid` : 'none',
            borderColor: withBorder ? theme.palette.neutral[300] : 'none'
          }}
        >
          {item.value}
        </MenuItem>
      ))}
    </SelectFormControl>
  );
};

export const Select = (props: SelectHoCProps) => {
  const { name, value, control } = props;
  return (
    <>
      {control ? (
        <Controller
          control={control}
          name={name}
          defaultValue={value}
          disabled={props.disabled}
          render={({ field: { value, onChange } }) => (
            <SelectInternal
              data-testid="selectIntenal-testId"
              {...props}
              value={value}
              onSelectChange={(e) => {
                onChange(e);
                if (props.onSelectChange) {
                  props.onSelectChange(e);
                }
              }}
              onClick={props.onClick}
            />
          )}
        />
      ) : (
        <SelectInternal {...props} />
      )}
    </>
  );
};
