# CDL Integration Documentation - OnePoint Account Lookup

> **Integration Overview**
> 
> This document provides comprehensive documentation for the Customer Data Lookup (CDL) integration with OnePoint, enabling account lookup functionality using various search parameters.
> 
> **Integration Type:** REST API Integration  
> **Purpose:** Customer account lookup and validation  
> **System:** OnePoint ↔ CDL Service  
> **Last Updated:** January 2024

## Table of Contents

1. [Integration Architecture](#integration-architecture)
2. [Authentication & Security](#authentication--security)
3. [Account Lookup Endpoints](#account-lookup-endpoints)
4. [Search Parameters](#search-parameters)
5. [Response Formats](#response-formats)
6. [Error Handling](#error-handling)
7. [Implementation Examples](#implementation-examples)
8. [Performance Considerations](#performance-considerations)
9. [Integration Checklist](#integration-checklist)
10. [Monitoring & Troubleshooting](#monitoring--troubleshooting)

## Integration Architecture

### System Overview

```
OnePoint Application
        ↓
    CDL Service
        ↓
Customer Database
```

### Data Flow

1. **User Input**: Customer search criteria entered in OnePoint
2. **API Call**: OnePoint sends request to CDL service
3. **Data Lookup**: CDL queries customer database
4. **Response**: CDL returns matching customer accounts
5. **Display**: OnePoint presents results to user

### Integration Points

- **Primary**: Account search and lookup
- **Secondary**: Account validation and verification
- **Tertiary**: Customer data synchronization


### Environment Configuration

| Environment | Base URL | Token Endpoint |
|-------------|----------|----------------|
| Development | https://cdl-dev.milwaukeetool.com/api | /auth/token |
| Test | https://cdl-test.milwaukeetool.com/api | /auth/token |
| Production | https://cdl.milwaukeetool.com/api | /auth/token |

## OnePoint API Endpoints for CDL Integration

### 1. User Account Search

**Endpoint Details:**
- **Method:** POST
- **Endpoint:** `/api/users/search`
- **Description:** Search for user accounts using CDL integration with multiple search criteria

#### Request Body

```json
{
  "searchCriteria": {
    "customerNumber": "string",
    "companyName": "string",
    "contactName": "string",
    "phoneNumber": "string",
    "email": "string",
    "address": {
      "street": "string",
      "city": "string",
      "state": "string",
      "zipCode": "string"
    },
    "taxId": "string"
  },
  "searchOptions": {
    "fuzzyMatch": true,
    "maxResults": 25,
    "includeInactive": false
  }
}
```

#### Response Format

```json
{
  "users": [
    {
      "userId": "user_12345",
      "customerNumber": "CUST001",
      "companyName": "Milwaukee Electric Tool Corp",
      "contactName": "John Smith",
      "email": "<EMAIL>",
      "phoneNumber": "+1-************",
      "address": {
        "street": "13135 W Lisbon Rd",
        "city": "Brookfield",
        "state": "WI",
        "zipCode": "53005"
      },
      "accountStatus": "Active",
      "accountType": "Commercial",
      "lastActivity": "2024-01-14T09:15:00Z"
    }
  ],
  "totalResults": 1,
  "success": true
}
```

### 2. Quick User Lookup by Customer Number

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/users/lookup/{customerNumber}`
- **Description:** Fast lookup for exact customer number matches using CDL

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| customerNumber | string | ✓ | Exact customer number |

#### Example Request

```http
GET /api/users/lookup/CUST001
```

#### Response Format

```json
{
  "user": {
    "userId": "user_12345",
    "customerNumber": "CUST001",
    "companyName": "Milwaukee Electric Tool Corp",
    "contactName": "John Smith",
    "email": "<EMAIL>",
    "phoneNumber": "+1-************",
    "address": {
      "street": "13135 W Lisbon Rd",
      "city": "Brookfield",
      "state": "WI",
      "zipCode": "53005"
    },
    "accountStatus": "Active",
    "accountType": "Commercial",
    "creditLimit": 50000.00,
    "paymentTerms": "Net 30",
    "taxExempt": false
  },
  "success": true
}
```

### 3. User Search by Phone Number

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/users/search/phone/{phoneNumber}`
- **Description:** Lookup users by phone number using CDL integration

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| phoneNumber | string | ✓ | Phone number (any format) |

#### Example Request

```http
GET /api/users/search/phone/**********
```

### 4. User Search by Email

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/users/search/email/{email}`
- **Description:** Find users by email address using CDL lookup

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| email | string | ✓ | Email address |

#### Example Request

```http
GET /api/users/search/email/<EMAIL>
```

### 5. User Search by Company Name

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/users/search/company/{companyName}`
- **Description:** Search users by company name with fuzzy matching via CDL

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| companyName | string | ✓ | Company name (partial matches supported) |

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| fuzzyMatch | boolean | ✗ | Enable fuzzy matching (default: true) |
| maxResults | integer | ✗ | Maximum results (default: 25) |

#### Example Request

```http
GET /api/users/search/company/Milwaukee%20Tool?fuzzyMatch=true&maxResults=10
```

### 6. User Validation

**Endpoint Details:**
- **Method:** POST
- **Endpoint:** `/api/users/validate`
- **Description:** Validate user information against CDL data

#### Request Body

```json
{
  "customerNumber": "CUST001",
  "email": "<EMAIL>",
  "phoneNumber": "+1-************",
  "companyName": "Milwaukee Electric Tool Corp"
}
```

#### Response Format

```json
{
  "isValid": true,
  "validationResults": {
    "customerNumber": "valid",
    "email": "valid",
    "phoneNumber": "valid",
    "companyName": "valid"
  },
  "matchedUser": {
    "userId": "user_12345",
    "customerNumber": "CUST001",
    "companyName": "Milwaukee Electric Tool Corp",
    "contactName": "John Smith"
  },
  "success": true
}
```

## Search Parameters

### Primary Search Fields

| Field | Type | Format | Example | Notes |
|-------|------|--------|---------|-------|
| customerNumber | string | Alphanumeric | "CUST001" | Exact or partial match |
| companyName | string | Text | "Milwaukee Tool" | Fuzzy matching available |
| contactName | string | Text | "John Smith" | First, last, or full name |
| phoneNumber | string | Various | "************" | Auto-formatted |
| email | string | Email | "<EMAIL>" | Case insensitive |
| taxId | string | Various | "39-1234567" | EIN, SSN formats |

### Address Search Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| street | string | ✗ | Street address |
| city | string | ✗ | City name |
| state | string | ✗ | State/Province code |
| zipCode | string | ✗ | Postal code |
| country | string | ✗ | Country code (ISO 3166) |

### Search Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| fuzzyMatch | boolean | true | Enable fuzzy matching |
| maxResults | integer | 50 | Maximum results to return |
| includeInactive | boolean | false | Include inactive accounts |
| sortBy | string | "relevance" | Sort criteria |
| sortOrder | string | "desc" | Sort direction |

### Sort Options

| Value | Description |
|-------|-------------|
| relevance | Match score (default) |
| companyName | Company name alphabetical |
| customerNumber | Customer number |
| lastActivity | Last account activity |
| createdDate | Account creation date |

## Response Formats

### Standard Account Object

```json
{
  "accountId": "string",
  "customerNumber": "string",
  "companyName": "string",
  "contactInfo": {
    "primaryContact": "string",
    "email": "string",
    "phone": "string",
    "fax": "string",
    "website": "string"
  },
  "address": {
    "street": "string",
    "street2": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string",
    "country": "string",
    "coordinates": {
      "latitude": 0.0,
      "longitude": 0.0
    }
  },
  "accountDetails": {
    "accountType": "string",
    "status": "string",
    "creditLimit": 0.0,
    "paymentTerms": "string",
    "taxExempt": false,
    "taxId": "string",
    "industry": "string",
    "salesRep": "string"
  },
  "metadata": {
    "createdDate": "string",
    "lastModified": "string",
    "lastActivity": "string",
    "source": "string"
  },
  "matchScore": 0.0
}
```

## Error Handling

### HTTP Status Codes

| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| 200 | OK | Successful search with results |
| 204 | No Content | Successful search with no results |
| 400 | Bad Request | Invalid search parameters |
| 401 | Unauthorized | Invalid or expired token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Customer number not found |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | CDL service error |
| 503 | Service Unavailable | CDL service maintenance |

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "INVALID_SEARCH_CRITERIA",
    "message": "Search criteria validation failed",
    "details": {
      "field": "phoneNumber",
      "reason": "Invalid phone number format",
      "validFormats": ["(XXX) XXX-XXXX", "XXX-XXX-XXXX", "XXXXXXXXXX"]
    }
  },
  "requestId": "req_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

