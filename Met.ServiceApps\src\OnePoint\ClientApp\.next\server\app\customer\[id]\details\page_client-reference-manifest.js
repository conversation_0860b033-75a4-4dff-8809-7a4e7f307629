globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/customer/[id]/details/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/App.tsx":{"*":{"id":"(ssr)/./src/app/App.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/env/EnvProvider.tsx":{"*":{"id":"(ssr)/./src/env/EnvProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customer/[id]/details/page.tsx":{"*":{"id":"(ssr)/./src/app/customer/[id]/details/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\App.tsx":{"id":"(app-pages-browser)/./src/app/App.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\env\\EnvProvider.tsx":{"id":"(app-pages-browser)/./src/env/EnvProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\customer\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/page.tsx","name":"*","chunks":["app/customer/page","static/chunks/app/customer/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\customer\\[id]\\details\\page.tsx":{"id":"(app-pages-browser)/./src/app/customer/[id]/details/page.tsx","name":"*","chunks":["app/customer/[id]/details/page","static/chunks/app/customer/%5Bid%5D/details/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\not-found":[],"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\page":["static/css/app/page.css"],"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\customer\\page":["static/css/app/customer/page.css"],"C:\\Users\\<USER>\\source\\repos\\Met.ServiceApps\\Met.ServiceApps\\src\\OnePoint\\ClientApp\\src\\app\\customer\\[id]\\details\\page":["static/css/app/customer/[id]/details/page.css"]}}