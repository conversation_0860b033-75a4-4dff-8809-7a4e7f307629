'use client';

import { Role, useRoleActions, useRoleContext } from '@/auth';
import { ActionParent, setParentPage } from '@/config';
import {
  ArrowLeftIcon,
  CloseIcon,
  RealtimeIcon,
  RepairOrderIcon,
  SalesOrderIcon,
  ToteOrderIcon
} from '@/design/atoms';
import {
  Loading,
  MainButton,
  PageHeaderContainer,
  Snackbar
} from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { AccountSubTitle, onProfileConfirmed } from '@/features/account';
import { COUNTRY_CODES, selectBranch } from '@/features/branch';
import {
  Customer,
  CustomerCard,
  CustomerHeader,
  CustomerParams,
  CustomerServiceRequestModal,
  EditProfile,
  setSalesOrderCreationProfile,
  setSearchedCustomer,
  setSelectedCustomer,
  useGetCustomerQuery,
  useSearchCustomersQuery
} from '@/features/customer';
import { selectHub } from '@/features/hub';
import { CurrencyType } from '@/features/pricing';
import {
  ProductCardContainer,
  ProductProgressCode,
  SelectedProduct,
  SelectProductStrategies
} from '@/features/product';
import {
  onCustomerAccountChange,
  OrderSubmitted,
  RepairItemsContainer
} from '@/features/repair';
import { setSalesOrderReturnToPath } from '@/features/salesOrder';
import {
  ApiError,
  ApiErrorCode,
  ProfileSearchBy,
  SearchCustomerContainer
} from '@/features/search';
import {
  addSelectedProductToEdit,
  clearSelectedProducts,
  handleShipToSiteNumberChange,
  MultiToolOrders,
  MultiToolPickup,
  onTaxExemptCertFileChange,
  OrderHistory,
  OrderIds,
  OrderListDisplayOrigin,
  OrderType,
  removeSelectedProduct,
  selectedServiceOrderState,
  ServiceOrderPaymentProvider,
  updateReturnTo,
  useGetOrderDetails,
  useSubmitServiceOrdersToQueueMutation,
  useUpdateOrder
} from '@/features/serviceOrder';
import { useFetchLightingMaxRepairPricing } from '@/hooks';
import { useSignalR } from '@/signalr';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { SnackbarState } from '@/util';
import { Divider, IconButton, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import { skipToken } from '@reduxjs/toolkit/query/react';
import { useTranslation } from 'next-export-i18n';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

interface Props {
  params: {
    id: string;
  };
  searchParams?: {
    orderId?: string;
    groupId?: string;
  };
}

export default function CustomerPage(props: Props) {
  const { t } = useTranslation();
  const {
    canStartRepair,
    canStartSale,
    canStartRealtimeRepair,
    canStartToteOrder
  } = useRoleActions();

  const router = useRouter();
  const customerId = props.params.id;
  const [orderId] = useState(props.searchParams?.orderId);
  const [groupId] = useState(props.searchParams?.groupId);
  const pathname = usePathname();
  const [isAccountNotFound, setIsAccountNotFound] = useState(false);

  const searchParams = useSearchParams();
  const currentFullPath = `${pathname}?${searchParams.toString()}`;

  const searchEmail = searchParams?.get('e') ?? '';
  const searchPhone = searchParams?.get('p') ?? '';
  const searchFirst = searchParams?.get('fn') ?? '';
  const searchLast = searchParams?.get('ln') ?? '';
  const pageNumber = searchParams?.get('page');
  const forceRealtimeService = useRef(
    () => searchParams?.get('realtime') === 'true' && canStartRealtimeRepair
  );

  const page =
    pageNumber && !isNaN(parseInt(pageNumber)) ? parseInt(pageNumber) : null;
  const searchedCustomer = useAppSelector(
    (state) => state.customer.searchedCustomer
  );
  const { selectedProducts, serviceCartProducts } = useAppSelector(
    selectedServiceOrderState
  );

  const [showMultiToolScreen, setShowMultiToolScreen] = useState(false);
  const [currencyType, setCurrencyType] = useState<CurrencyType>(
    CurrencyType.Usd
  );
  const [selectedMultiToolOrders, setSelectedMultiToolOrders] = useState<
    MultiToolOrders[]
  >([]);
  const [snackBarState, setSnackBarState] = useState<SnackbarState>({
    open: true,
    message: t('features.customer.profileForm.userNotFound'),
    severity: 'error'
  });

  const snackBarError = t('common.snackBarError');
  const dispatch = useAppDispatch();
  const [snackbarState, setSnackbarState] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });
  const [orderWizardOpen, setOrderWizardOpen] = useState<boolean>(false);
  const [showAccountSelection, setShowAccountSelection] =
    useState<boolean>(false);
  const [isOrderSubmitted, setIsOrderSubmitted] = useState<boolean>(false);
  const [currentSelectedProduct, setCurrentSelectedProduct] = useState('');

  const [selectProductStrategy, setSelectProductStrategy] =
    useState<SelectProductStrategies>(SelectProductStrategies.MainProduct);

  const [selectedProduct, setSelectedProduct] = useState<SelectedProduct>();
  const [isRepairItemsOpen, setRepairItemsOpen] = useState(false);
  const selectedHub = useAppSelector(selectHub);

  const [orderIds, setOrderIds] = useState<OrderIds>();
  const [showNewServiceRequestModal, setShowNewServiceRequestModal] =
    useState(false);

  const [redirectToRepairPagePath, setRedirectToRepairPagePath] =
    useState<string>();
  const [isTaxExempt, setIsTaxExempt] = useState<boolean>(false);
  const [isSalesOrder, setIsSalesOrder] = useState<boolean>(false);
  const branchSelected = useAppSelector(selectBranch);
  const { getDetails, isErrorSearchServiceOrderGroup } = useGetOrderDetails();
  const {
    updateOrder,
    isLoadingUpdateServiceOrder,
    isErrorUpdateServiceOrder
  } = useUpdateOrder();
  const [
    submitServiceOrderTigger,
    {
      isError: isErrorSubmitServiceOrder,
      isLoading: isLoadingSubmitServiceOrder
    }
  ] = useSubmitServiceOrdersToQueueMutation();

  const [serviceRequestNumber, setServiceRequestNumber] = useState<
    string | undefined
  >();

  const {
    isConnected,
    joinServiceRequestGroup,
    addListener,
    connect,
    disconnect,
    removeListener
  } = useSignalR();

  const signalrMethod = 'RefreshServiceRequest';

  const signalRInitialized = useRef(false);

  // Function to initialize SignalR connection for a specific order
  const initializeSignalR = useCallback(
    async (serviceRequestId: string) => {
      if (!isConnected) {
        try {
          await connect();
        } catch (error) {
          console.error('Error connecting to SignalR', error);
          return;
        }
      }

      const handleUpdate = (update: {
        id: string;
        serviceRequestNumber: string;
      }) => {
        if (update.id === serviceRequestId) {
          setServiceRequestNumber(update.serviceRequestNumber);
        }
      };

      addListener(signalrMethod, handleUpdate);

      try {
        await joinServiceRequestGroup(serviceRequestId);
        signalRInitialized.current = true;
      } catch (err) {
        console.error('Error joining SignalR group:', err);
      }
    },
    [isConnected, connect, addListener, joinServiceRequestGroup]
  );

  useEffect(() => {
    return () => {
      if (signalRInitialized.current) {
        removeListener(signalrMethod);
        disconnect();
        signalRInitialized.current = false;
      }
    };
  }, [removeListener, disconnect]);

  useEffect(() => {
    if (searchEmail || searchPhone || searchFirst || searchLast) {
      dispatch(
        setSearchedCustomer({
          email: searchEmail,
          phone: searchPhone,
          firstName: searchFirst,
          lastName: searchLast
        } as CustomerParams)
      );
    }
  }, [dispatch, searchEmail, searchFirst, searchLast, searchPhone]);
  useEffect(() => {
    if (selectedProduct === undefined) return;
    const productAlreadyExist =
      selectedProducts.find((product) => product.id === selectedProduct.id) !==
      undefined;

    if (productAlreadyExist) return;

    dispatch(addSelectedProductToEdit(selectedProduct));
  }, [selectedProduct, dispatch, selectedProducts]);

  // Customer profile information coming from CDP
  const {
    data,
    error,
    refetch: reloadCustomerData,
    isFetching: isFetchingCustomersQuery
  } = useSearchCustomersQuery(customerId ? { ids: [customerId] } : skipToken);

  // Fetch customer profile from DB
  const {
    data: preferences,
    isError: isErrorCustomerQuery,
    isFetching: isFetchingCustomerQuery,
    refetch: reloadCustomerPreferences
  } = useGetCustomerQuery(customerId, {
    skip: !customerId,
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
    refetchOnFocus: true
  });

  const { canViewServiceHistory, hasHubSelection } = useRoleActions();

  const location = useMemo(
    () => (hasHubSelection ? selectedHub : branchSelected),
    [hasHubSelection, branchSelected, selectedHub]
  );
  const skus = useMemo(
    () => [
      ...serviceCartProducts.map((product) => product.sku),
      ...selectedProducts.map((x) => x.sku)
    ],
    [serviceCartProducts, selectedProducts]
  );

  useFetchLightingMaxRepairPricing({
    countryCode: location?.countryCode,
    skus: skus
  });

  const { currentRole } = useRoleContext();

  const isHubRole = currentRole === Role.Hub;
  const isCanadaBranch = branchSelected?.countryCode === COUNTRY_CODES.CANADA;
  const canProcessEserviceOrders = useMemo(
    () => isHubRole || isCanadaBranch,
    [isHubRole, isCanadaBranch]
  );

  const navigateToRepairPage = (isRealtime = false) => {
    const url = `/serviceOrder/repair?id=${customerId}${
      isRealtime ? '&realtime=true' : ''
    }`;
    if (isCanadaBranch) {
      router.push(url);
    } else {
      setRedirectToRepairPagePath(url);
      setShowNewServiceRequestModal(true);
    }
  };

  const navigateToToteOrderPage = () => {
    const url = `/serviceOrder/repair?id=${customerId}&isToteOrder=true`;
    if (isCanadaBranch) {
      router.push(url);
    } else {
      setRedirectToRepairPagePath(url);
      setShowNewServiceRequestModal(true);
    }
  };

  const goToToteOrderPage = () => {
    setIsSalesOrder(false);
    dispatch(onProfileConfirmed({}));
    navigateToToteOrderPage();
  };

  const goToRepairPage = () => {
    dispatch(setParentPage(ActionParent.NewServiceRequest));
    setIsSalesOrder(false);
    dispatch(updateReturnTo(currentFullPath));
    dispatch(onProfileConfirmed({}));
    navigateToRepairPage();
  };

  const goToRealtimeRepairPage = () => {
    dispatch(setParentPage(ActionParent.RealTimeService));
    setIsSalesOrder(false);
    navigateToRepairPage(true);
  };

  const handleCustomerServiceRequestModalNextClick = (file?: File) => {
    dispatch(onTaxExemptCertFileChange(file));
    setShowNewServiceRequestModal(false);

    if (isSalesOrder) {
      // We only get here if there is a selected branch with accounts
      const account = branchSelected!.accounts!.find(
        (acc) => acc.taxable === !isTaxExempt
      );

      if (!account) {
        throw new Error(
          `Account not found for tax exempt value ${isTaxExempt}`
        );
      }

      const { accountNumber, taxable } = account;

      dispatch(setSelectedCustomer(data!.users[0]));
      dispatch(
        setSalesOrderCreationProfile({
          isGuest: false,
          accountNumber,
          isTaxExempt: !taxable,
          site: undefined
        })
      );
      dispatch(setSalesOrderReturnToPath(currentFullPath));
      router.push('/salesOrder/create');
    } else {
      dispatch(onCustomerAccountChange());
      const repairPagePath = `${redirectToRepairPagePath}&isTaxExempt=${isTaxExempt}`;
      router.push(repairPagePath);
    }
  };

  const onCloseModalNewServiceRequestClickHandler = () => {
    setRedirectToRepairPagePath('');
    setShowNewServiceRequestModal(false);
    setIsTaxExempt(false);
  };

  const handleMultiToolProcess = (multiToolOrders: MultiToolOrders[]) => {
    setSelectedMultiToolOrders(multiToolOrders);
    setShowMultiToolScreen(true);
  };

  const handleRemovePickupOrder = (orderId: string) => {
    setSelectedMultiToolOrders(
      selectedMultiToolOrders.filter((x) => x.orderId !== orderId)
    );
    if (selectedMultiToolOrders.length === 1) {
      setShowMultiToolScreen(false);
    }
  };

  const handleApiError = useCallback(() => {
    setSnackbarState({
      message: snackBarError,
      severity: 'error',
      open: true
    });
  }, [snackBarError]);

  const handleStartSaleClick = () => {
    if (!data?.users[0]) return;

    dispatch(setParentPage(ActionParent.StartASale));

    setIsSalesOrder(true);

    if (
      !branchSelected ||
      !branchSelected.accounts ||
      branchSelected.accounts.length === 0
    ) {
      throw new Error('No branch selected or branch has no accounts mapped.');
    }

    // Only prompt if we have more than one account to choose
    if (branchSelected.accounts.length === 1) {
      const { accountNumber, taxable } = branchSelected.accounts[0];
      dispatch(setSelectedCustomer(data!.users[0]));
      dispatch(
        setSalesOrderCreationProfile({
          isGuest: false,
          accountNumber,
          isTaxExempt: !taxable,
          site: undefined
        })
      );
      dispatch(setSalesOrderReturnToPath(currentFullPath));
      router.push('/salesOrder/create');
    } else {
      setShowNewServiceRequestModal(true);
    }
  };

  const startOrderWizard = useCallback(
    async (orderIds: OrderIds, systemOrigin?: OrderType) => {
      window.scrollTo(0, 0);
      setOrderWizardOpen(true);

      const orderDetails = await getDetails(orderIds.groupId, orderIds.orderId);

      if (!isErrorSearchServiceOrderGroup) {
        const order = orderDetails!.order;

        const productToEdit = {
          ...orderDetails?.selectedProduct,
          shipToMetadata: order.shipToMetadata,
          shipToSiteNumber: order.shipToSiteNumber,
          progress: ProductProgressCode.SerialNumber,
          repairLines: order.repairLines[0],
          editMode: true,
          orderType: systemOrigin,
          masterTrackingNumber: order.masterTrackingNumber,
          toolNickName: order.toolNickname
        } as SelectedProduct;
        setSelectedProduct(productToEdit);
        setOrderIds(orderIds);

        const theCurrencyType =
          order.shipToMetadata?.currency === 'USA'
            ? CurrencyType.Usd
            : order.shipToMetadata?.currency === 'CAN'
              ? CurrencyType.Cad
              : CurrencyType.Usd;
        setCurrencyType(theCurrencyType);
      } else {
        handleApiError();
      }
    },
    [getDetails, handleApiError, isErrorSearchServiceOrderGroup]
  );

  const onCustomerProfileEdited = () => {
    reloadCustomerData();
    reloadCustomerPreferences();
  };

  useEffect(() => {
    if (orderId === undefined || groupId === undefined) return;
    setOrderWizardOpen(true);
    router.replace(`/customer/${customerId}/details`);
    dispatch(clearSelectedProducts());
    const intervalId = setTimeout(() => {
      startOrderWizard({
        orderId,
        groupId
      });
    }, 100);

    return () => clearTimeout(intervalId);
  }, [orderId, groupId, customerId, router, startOrderWizard, dispatch]);

  const handleProcessOrderClick = async (
    orderIds: OrderIds,
    systemOrigin: OrderType
  ) => {
    startOrderWizard(orderIds, systemOrigin);
  };

  const handleCloseSnackbar = () => {
    setSnackbarState({
      ...snackbarState,
      open: false
    });
  };

  const handleOrderSubmission = async (updatedProduct: SelectedProduct) => {
    try {
      await updateOrder(
        updatedProduct,
        orderIds!.orderId,
        selectedProduct?.proofOfPurchase.createdFilePath
      );

      if (!isErrorUpdateServiceOrder) {
        await submitServiceOrderTigger({
          ServiceOrdersIds: [orderIds!.orderId]
        });

        if (!isErrorSubmitServiceOrder) {
          await initializeSignalR(orderIds!.orderId);

          dispatch(removeSelectedProduct(updatedProduct.selectionId));
          setOrderWizardOpen(false);
          setIsOrderSubmitted(true);
        }
      }
    } catch (error) {
      handleApiError();
    }
  };

  const handleSelectReplacementProduct = (selectionId: string) => {
    setCurrentSelectedProduct(selectionId);
    setSelectProductStrategy(SelectProductStrategies.ReplacementProduct);
    setRepairItemsOpen(true);
  };

  const handleSelectRealTimeReplacementProduct = (selectionId: string) => {
    setCurrentSelectedProduct(selectionId);
    setSelectProductStrategy(
      SelectProductStrategies.RealTimeReplacementProduct
    );
    setRepairItemsOpen(true);
  };

  const handleProductSelection = () => {
    setRepairItemsOpen(false);
    setSelectProductStrategy(SelectProductStrategies.MainProduct);
  };

  const handleCancelButtonClick = () => {
    setOrderIds(undefined);
    setSelectedProduct(undefined);
    dispatch(removeSelectedProduct(selectedProduct!.selectionId));
    setOrderWizardOpen(false);
    setIsOrderSubmitted(false);
  };

  const multiToolBackClick = useCallback(() => {
    setShowMultiToolScreen(false);
    dispatch(onCustomerAccountChange());
  }, [dispatch]);

  if (showAccountSelection)
    return (
      <Box
        sx={{
          p: 2,
          display: 'flex',
          flexDirection: 'row',
          gap: 2
        }}
      >
        <Box
          sx={{
            alignContent: 'start',
            pt: 2.5
          }}
        >
          <IconButton
            data-testid={`account-selection-back-button-testId`}
            onClick={() => {
              setShowAccountSelection(false);
            }}
          >
            <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
          </IconButton>
        </Box>
        <div style={{ flex: 1 }}>
          <SearchCustomerContainer
            profileSearchBy={ProfileSearchBy.AccountInfo}
            showCustomerToggle={false}
            isViewMode={false}
            onCustomerNotFound={() => {}}
            isCustomerNotFound={false}
            selectedBranch={branchSelected}
            selectedHub={selectedHub}
            onCustomerSelectedNotFound={() => {}}
            isAccountNotFound={isAccountNotFound}
            onAccountNotFound={setIsAccountNotFound}
            accountSelectedHandler={(site) => {
              const shipToMetadata = {
                addressLine1: site.addressLine1,
                city: site.city,
                country: site.countryCode,
                postalCode: site.postalCode,
                state: site.state,
                paymentTerms: site.paymentTerms,
                shipToSiteName: site.siteName
              };
              setSelectedProduct((product) => ({
                ...product!,
                shipToSiteNumber: site.siteNumber,
                shipToMetadata: shipToMetadata
              }));
              setShowAccountSelection(false);
              dispatch(
                handleShipToSiteNumberChange({
                  selectionId: selectedProduct!.selectionId,
                  shipToSiteNumber: site.siteNumber,
                  shipToMetadata
                })
              );
              setSnackbarState({
                message: t('common.accountSuccessfullyUpdated'),
                open: true,
                severity: 'success'
              });
            }}
          />
        </div>
      </Box>
    );

  return (
    <>
      {showMultiToolScreen ? (
        <ServiceOrderPaymentProvider>
          <MultiToolPickup
            customer={data?.users[0] ?? ({} as Customer)}
            onBackClick={multiToolBackClick}
            multiTools={selectedMultiToolOrders}
            removePickupOrder={handleRemovePickupOrder}
          />
        </ServiceOrderPaymentProvider>
      ) : isRepairItemsOpen ? (
        <PageAreaBox
          pageAreaBoxName="ProductSearch"
          sx={{ maxWidth: '1460px' }}
        >
          <RepairItemsContainer
            onProductSelected={handleProductSelection}
            selectProductStrategy={selectProductStrategy}
            currentSelectedProduct={currentSelectedProduct}
          />
        </PageAreaBox>
      ) : (
        <>
          <PageHeaderContainer
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            {orderWizardOpen ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton
                  onClick={handleCancelButtonClick}
                  data-testid="pickupConfirmationBackBtn-testId"
                >
                  <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
                </IconButton>
                <Typography variant="h5">
                  {t('features.repair.confirmRepairDetails')}
                </Typography>
              </Box>
            ) : (
              <>
                {isOrderSubmitted ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h5">
                      {t('features.repair.confirmRepairDetails')}
                    </Typography>
                  </Box>
                ) : (
                  <CustomerHeader
                    searchParams={{
                      email: searchedCustomer?.email,
                      phone: searchedCustomer?.phoneNumber,
                      firstName: searchedCustomer?.firstName,
                      lastName: searchedCustomer?.lastName
                    }}
                  />
                )}
                <IconButton
                  onClick={() => router.push('/')}
                  data-testid="closeBtn-testId"
                >
                  <CloseIcon />
                </IconButton>
              </>
            )}
          </PageHeaderContainer>
          <PageAreaBox
            pageAreaBoxName="customer"
            sx={{
              height: '100%',
              bgcolor: 'background.paper',
              maxWidth: '1460px',
              padding: `0px 32px 0px 32px`,
              mt: '88px'
            }}
          >
            <Snackbar
              open={snackbarState.open}
              message={snackbarState.message}
              severity={snackbarState.severity}
              handleClose={handleCloseSnackbar}
            />
            {orderWizardOpen ? (
              <Loading
                isLoading={
                  !data?.users[0] ||
                  !orderIds?.groupId ||
                  !selectedProduct ||
                  isLoadingUpdateServiceOrder ||
                  isLoadingSubmitServiceOrder
                }
                fallbackContainerProps={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {canProcessEserviceOrders && (
                  <Box
                    data-testid={'customer-generic-account-details'}
                    sx={{
                      px: 3,
                      py: 2,
                      backgroundColor: 'common.white',
                      borderRadius: '4px',
                      border: '1px solid',
                      borderColor: 'neutral.400'
                    }}
                  >
                    <AccountSubTitle
                      accountName={
                        selectedProduct?.shipToMetadata?.shipToSiteName ?? ''
                      }
                      siteNumber={selectedProduct?.shipToSiteNumber ?? ''}
                      displayChangeAccountButton
                      handleChangeAccountButtonClick={() => {
                        setShowAccountSelection(true);
                      }}
                    />
                    <CustomerCard
                      customer={data?.users[0] ?? ({} as Customer)}
                      customerPreferences={preferences}
                      showOnlyCustomerInfo
                      containerSxProps={{
                        px: 0,
                        pb: 0
                      }}
                      isLoadingData={isFetchingCustomersQuery}
                    />
                  </Box>
                )}

                <ProductCardContainer
                  serviceOrderGroupTempId={orderIds?.groupId}
                  customerSelected={data?.users[0]}
                  hasWarrantyInteraction={false}
                  onSnackbarStateChange={() => {}}
                  onOrderWarrantyInteraction={() => {}}
                  updateServiceOrderGroupTempId={() => {}}
                  onAddToCart={handleOrderSubmission}
                  onSelectReplacementProduct={handleSelectReplacementProduct}
                  onSelectRealTimeReplacementProduct={
                    handleSelectRealTimeReplacementProduct
                  }
                  onCancelButtonClick={handleCancelButtonClick}
                  isOrderEditing={true}
                  handleSetRealTimeServiceType={() => {}}
                  forceRealtimeService={forceRealtimeService.current()}
                  currencyType={currencyType}
                />
              </Loading>
            ) : isOrderSubmitted ? (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    pt: '36px'
                  }}
                >
                  <>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                      }}
                    >
                      <CustomerCard
                        customer={data!.users[0]}
                        customerPreferences={preferences}
                        containerSxProps={{
                          p: 0
                        }}
                        isLoadingData={isFetchingCustomersQuery}
                      />
                    </Box>
                    <Divider sx={{ color: 'neutral.300', mt: 2.5 }} />
                  </>
                </Box>
                <OrderSubmitted
                  userId={data!.users[0].userId}
                  handleViewCustomerServiceHistoryClick={
                    handleCancelButtonClick
                  }
                  svNumber={serviceRequestNumber}
                  externalId={orderIds?.orderId}
                />
              </>
            ) : (
              <>
                {(error as ApiError)?.data.status == ApiErrorCode.NotFound &&
                  snackBarState.open && (
                    <Snackbar
                      open={true}
                      message={snackBarState.message}
                      handleClose={() =>
                        setSnackBarState({ ...snackBarState, open: false })
                      }
                      severity={snackBarState.severity}
                    />
                  )}
                {data && (preferences || !isErrorCustomerQuery) ? (
                  <>
                    <Box
                      sx={{
                        p: '36px',
                        backgroundColor: 'common.white',
                        mb: '24px',
                        borderRadius: '4px',
                        border: '1px solid',
                        borderColor: 'neutral.400'
                      }}
                    >
                      {(preferences || !isErrorCustomerQuery) && (
                        <EditProfile
                          customerProfile={data?.users[0]}
                          isModalMode={false}
                          preferences={preferences}
                          isFetchingCustomerData={isFetchingCustomerQuery}
                          onProfileSave={onCustomerProfileEdited}
                        />
                      )}
                    </Box>

                    <Grid container spacing={2}>
                      {canStartRepair && (
                        <Grid item xs>
                          <MainButton
                            description={t(
                              'features.home.mainButtons.startRepairOrder'
                            )}
                            icon={
                              <RepairOrderIcon
                                sx={{
                                  width: '96px',
                                  height: '96px',
                                  color: 'primary.700'
                                }}
                                viewBox="0 0 96 96"
                              />
                            }
                            testId="startRepair-testId"
                            onButtonClick={goToRepairPage}
                          />
                        </Grid>
                      )}
                      {canStartRealtimeRepair && (
                        <Grid item xs>
                          <MainButton
                            description={t(
                              'features.home.mainButtons.realtimeService'
                            )}
                            icon={
                              <RealtimeIcon
                                sx={{
                                  width: '96px',
                                  height: '96px',
                                  color: 'primary.700'
                                }}
                                viewBox="0 0 96 96"
                              />
                            }
                            testId="startRealtimeRepair-testId"
                            onButtonClick={goToRealtimeRepairPage}
                          />
                        </Grid>
                      )}
                      {canStartSale && (
                        <Grid item xs>
                          <MainButton
                            description={t(
                              'features.home.mainButtons.startSaleOrder'
                            )}
                            icon={
                              <SalesOrderIcon
                                sx={{
                                  width: '66px',
                                  height: '96px',
                                  color: 'primary.main'
                                }}
                                viewBox="0 0 66 96"
                              />
                            }
                            testId="startSale-testId"
                            onButtonClick={handleStartSaleClick}
                          />
                        </Grid>
                      )}
                      {canStartToteOrder && (
                        <Grid item xs>
                          <MainButton
                            description={t(
                              'features.home.mainButtons.newToteOrder'
                            )}
                            icon={
                              <ToteOrderIcon
                                sx={{
                                  width: '96px',
                                  height: '96px',
                                  color: 'primary.700'
                                }}
                                viewBox="0 0 96 96"
                              />
                            }
                            testId="newToteOrder-testId"
                            onButtonClick={goToToteOrderPage}
                          />
                        </Grid>
                      )}
                    </Grid>
                    {canViewServiceHistory && (
                      <Grid
                        container
                        alignItems="center"
                        justifyContent="center"
                        sx={{
                          pt: '1.5rem'
                        }}
                      >
                        <Grid
                          item
                          xs={12}
                          sx={{
                            p: '0px'
                          }}
                        >
                          <Box
                            sx={{
                              backgroundColor: 'common.white',
                              width: '100%',
                              border: '1px solid',
                              borderRadius: '4px',
                              borderColor: 'neutral.400'
                            }}
                          >
                            <OrderHistory
                              customer={data?.users[0]}
                              selectedBranch={branchSelected}
                              dataTestId={'orderHistoryTab-testId'}
                              displayOrigin={
                                OrderListDisplayOrigin.OrderHistory
                              }
                              onProcessMultipleOrderClick={
                                handleMultiToolProcess
                              }
                              page={page}
                              onProcessOrderClick={handleProcessOrderClick}
                            />
                          </Box>
                        </Grid>
                      </Grid>
                    )}
                  </>
                ) : (
                  <CircularProgress
                    sx={{
                      position: 'absolute',
                      left: '50%',
                      top: '50%',
                      zIndex: 1
                    }}
                  />
                )}
              </>
            )}
          </PageAreaBox>
        </>
      )}

      {data && !isErrorCustomerQuery && (
        <CustomerServiceRequestModal
          hideRepairDetails={isSalesOrder}
          showModal={showNewServiceRequestModal}
          customer={data!.users[0]}
          isTaxExempt={isTaxExempt}
          setIsTaxExempt={setIsTaxExempt}
          onNextClickHandler={handleCustomerServiceRequestModalNextClick}
          onCloseClickHandler={onCloseModalNewServiceRequestClickHandler}
        />
      )}
    </>
  );
}
