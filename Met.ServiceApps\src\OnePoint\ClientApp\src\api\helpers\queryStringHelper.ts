export const buildQueryStringUrl = (
  actionRoute: string,
  params: Record<string, string | number | boolean | undefined>
): string => {
  const queryString = Object.entries(params)
    .map(([key, value]) => {
      if (value === undefined) {
        return '';
      }

      return `${key}=${value}`;
    })
    .filter((param) => param !== '')
    .join('&');

  return `${actionRoute}?${queryString}`;
};
