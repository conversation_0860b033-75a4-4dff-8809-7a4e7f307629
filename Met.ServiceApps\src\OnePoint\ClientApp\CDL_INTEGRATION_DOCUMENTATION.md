# CDL Integration Documentation - OnePoint Account Lookup

## Overview

This document provides comprehensive documentation for the Customer Data Lookup (CDL) integration with OnePoint, enabling account lookup functionality using various search parameters.

**Integration Type**: REST API Integration  
**Purpose**: Customer account lookup and validation  
**System**: OnePoint ↔ CDL Service  
**Last Updated**: January 2024

## Table of Contents

1. [Integration Architecture](#integration-architecture)
2. [Authentication & Security](#authentication--security)
3. [Account Lookup Endpoints](#account-lookup-endpoints)
4. [Search Parameters](#search-parameters)
5. [Response Formats](#response-formats)
6. [Error Handling](#error-handling)
7. [Implementation Examples](#implementation-examples)
8. [Performance Considerations](#performance-considerations)

---

## Integration Architecture

### System Overview

```
OnePoint Application
        ↓
    CDL Service
        ↓
Customer Database
```

### Data Flow

1. **User Input**: Customer search criteria entered in OnePoint
2. **API Call**: OnePoint sends request to CDL service
3. **Data Lookup**: CDL queries customer database
4. **Response**: CDL returns matching customer accounts
5. **Display**: OnePoint presents results to user

### Integration Points

- **Primary**: Account search and lookup
- **Secondary**: Account validation and verification
- **Tertiary**: Customer data synchronization

---

## Authentication & Security

### Authentication Method

**Type**: Bearer Token Authentication

```http
Authorization: Bearer {cdl-service-token}
Content-Type: application/json
Accept: application/json
```

### Security Requirements

- **TLS 1.2+** for all communications
- **Token rotation** every 24 hours
- **Request signing** for sensitive operations
- **Rate limiting** enforcement

### Environment Configuration

| Environment | Base URL                                 | Token Endpoint |
| ----------- | ---------------------------------------- | -------------- |
| Development | `https://cdl-dev.milwaukeetool.com/api`  | `/auth/token`  |
| Test        | `https://cdl-test.milwaukeetool.com/api` | `/auth/token`  |
| Production  | `https://cdl.milwaukeetool.com/api`      | `/auth/token`  |

---

## Account Lookup Endpoints

### 1. Search Accounts by Multiple Criteria

**Endpoint**: `POST /api/accounts/search`

**Description**: Performs comprehensive account search using multiple parameters with fuzzy matching capabilities.

**Request Body**:

```json
{
  "searchCriteria": {
    "customerNumber": "string",
    "companyName": "string",
    "contactName": "string",
    "phoneNumber": "string",
    "email": "string",
    "address": {
      "street": "string",
      "city": "string",
      "state": "string",
      "zipCode": "string",
      "country": "string"
    },
    "taxId": "string",
    "accountType": "string"
  },
  "searchOptions": {
    "fuzzyMatch": true,
    "maxResults": 50,
    "includeInactive": false,
    "sortBy": "relevance",
    "sortOrder": "desc"
  }
}
```

**Response Format**:

```json
{
  "accounts": [
    {
      "accountId": "ACC123456",
      "customerNumber": "CUST001",
      "companyName": "Milwaukee Electric Tool Corp",
      "contactInfo": {
        "primaryContact": "John Smith",
        "email": "<EMAIL>",
        "phone": "+1-************",
        "fax": "******-555-0124"
      },
      "address": {
        "street": "13135 W Lisbon Rd",
        "city": "Brookfield",
        "state": "WI",
        "zipCode": "53005",
        "country": "US"
      },
      "accountDetails": {
        "accountType": "Commercial",
        "status": "Active",
        "creditLimit": 50000.0,
        "paymentTerms": "Net 30",
        "taxExempt": false,
        "taxId": "39-1234567"
      },
      "metadata": {
        "createdDate": "2020-01-15T00:00:00Z",
        "lastModified": "2024-01-10T14:30:00Z",
        "lastActivity": "2024-01-14T09:15:00Z"
      },
      "matchScore": 0.95
    }
  ],
  "pagination": {
    "totalResults": 1,
    "currentPage": 1,
    "totalPages": 1,
    "pageSize": 50
  },
  "searchMetadata": {
    "searchTime": 0.125,
    "fuzzyMatchUsed": true,
    "searchId": "search_12345"
  },
  "success": true
}
```

### 2. Quick Lookup by Customer Number

**Endpoint**: `GET /api/accounts/lookup/{customerNumber}`

**Description**: Fast lookup for exact customer number matches.

**Path Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| customerNumber | string | Yes | Exact customer number |

**Query Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| includeHistory | boolean | No | Include account history (default: false) |
| includeContacts | boolean | No | Include all contacts (default: true) |

**Example Request**:

```http
GET /api/accounts/lookup/CUST001?includeHistory=true&includeContacts=true
```

### 3. Search by Phone Number

**Endpoint**: `GET /api/accounts/search/phone/{phoneNumber}`

**Description**: Lookup accounts by phone number with format normalization.

**Path Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| phoneNumber | string | Yes | Phone number (any format) |

**Example Request**:

```http
GET /api/accounts/search/phone/**********
```

### 4. Search by Email Address

**Endpoint**: `GET /api/accounts/search/email/{email}`

**Description**: Find accounts associated with an email address.

**Path Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| email | string | Yes | Email address |

**Example Request**:

```http
GET /api/accounts/search/email/<EMAIL>
```

### 5. Search by Tax ID

**Endpoint**: `GET /api/accounts/search/taxid/{taxId}`

**Description**: Lookup accounts by tax identification number.

**Path Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| taxId | string | Yes | Tax ID (EIN, SSN, etc.) |

**Example Request**:

```http
GET /api/accounts/search/taxid/39-1234567
```

### 6. Advanced Address Search

**Endpoint**: `POST /api/accounts/search/address`

**Description**: Search accounts by address components with geocoding support.

**Request Body**:

```json
{
  "address": {
    "street": "13135 W Lisbon Rd",
    "city": "Brookfield",
    "state": "WI",
    "zipCode": "53005",
    "country": "US"
  },
  "searchRadius": 5,
  "radiusUnit": "miles",
  "exactMatch": false
}
```

---

## Search Parameters

### Primary Search Fields

| Field          | Type   | Format       | Example           | Notes                     |
| -------------- | ------ | ------------ | ----------------- | ------------------------- |
| customerNumber | string | Alphanumeric | "CUST001"         | Exact or partial match    |
| companyName    | string | Text         | "Milwaukee Tool"  | Fuzzy matching available  |
| contactName    | string | Text         | "John Smith"      | First, last, or full name |
| phoneNumber    | string | Various      | "************"    | Auto-formatted            |
| email          | string | Email        | "<EMAIL>" | Case insensitive          |
| taxId          | string | Various      | "39-1234567"      | EIN, SSN formats          |

### Address Search Fields

| Field   | Type   | Required | Description             |
| ------- | ------ | -------- | ----------------------- |
| street  | string | No       | Street address          |
| city    | string | No       | City name               |
| state   | string | No       | State/Province code     |
| zipCode | string | No       | Postal code             |
| country | string | No       | Country code (ISO 3166) |

### Search Options

| Option          | Type    | Default     | Description               |
| --------------- | ------- | ----------- | ------------------------- |
| fuzzyMatch      | boolean | true        | Enable fuzzy matching     |
| maxResults      | integer | 50          | Maximum results to return |
| includeInactive | boolean | false       | Include inactive accounts |
| sortBy          | string  | "relevance" | Sort criteria             |
| sortOrder       | string  | "desc"      | Sort direction            |

### Sort Options

| Value          | Description               |
| -------------- | ------------------------- |
| relevance      | Match score (default)     |
| companyName    | Company name alphabetical |
| customerNumber | Customer number           |
| lastActivity   | Last account activity     |
| createdDate    | Account creation date     |

---

## Response Formats

### Standard Account Object

```json
{
  "accountId": "string",
  "customerNumber": "string",
  "companyName": "string",
  "contactInfo": {
    "primaryContact": "string",
    "email": "string",
    "phone": "string",
    "fax": "string",
    "website": "string"
  },
  "address": {
    "street": "string",
    "street2": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string",
    "country": "string",
    "coordinates": {
      "latitude": 0.0,
      "longitude": 0.0
    }
  },
  "accountDetails": {
    "accountType": "string",
    "status": "string",
    "creditLimit": 0.0,
    "paymentTerms": "string",
    "taxExempt": false,
    "taxId": "string",
    "industry": "string",
    "salesRep": "string"
  },
  "metadata": {
    "createdDate": "string",
    "lastModified": "string",
    "lastActivity": "string",
    "source": "string"
  },
  "matchScore": 0.0
}
```

### Pagination Object

```json
{
  "totalResults": 0,
  "currentPage": 1,
  "totalPages": 1,
  "pageSize": 50,
  "hasNextPage": false,
  "hasPreviousPage": false
}
```

### Search Metadata

```json
{
  "searchTime": 0.0,
  "fuzzyMatchUsed": false,
  "searchId": "string",
  "cacheHit": false,
  "dataSource": "string"
}
```

---

## Error Handling

### HTTP Status Codes

| Status Code | Description           | Common Scenarios                  |
| ----------- | --------------------- | --------------------------------- |
| 200         | OK                    | Successful search with results    |
| 204         | No Content            | Successful search with no results |
| 400         | Bad Request           | Invalid search parameters         |
| 401         | Unauthorized          | Invalid or expired token          |
| 403         | Forbidden             | Insufficient permissions          |
| 404         | Not Found             | Customer number not found         |
| 422         | Unprocessable Entity  | Validation errors                 |
| 429         | Too Many Requests     | Rate limit exceeded               |
| 500         | Internal Server Error | CDL service error                 |
| 503         | Service Unavailable   | CDL service maintenance           |

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "INVALID_SEARCH_CRITERIA",
    "message": "Search criteria validation failed",
    "details": {
      "field": "phoneNumber",
      "reason": "Invalid phone number format",
      "validFormats": ["(XXX) XXX-XXXX", "XXX-XXX-XXXX", "XXXXXXXXXX"]
    }
  },
  "requestId": "req_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common Error Codes

| Error Code               | Description                         | Resolution                                |
| ------------------------ | ----------------------------------- | ----------------------------------------- |
| INVALID_SEARCH_CRITERIA  | Search parameters validation failed | Check parameter formats and requirements  |
| NO_RESULTS_FOUND         | No accounts match search criteria   | Broaden search criteria or check spelling |
| RATE_LIMIT_EXCEEDED      | Too many requests in time window    | Implement request throttling              |
| AUTHENTICATION_FAILED    | Invalid or expired token            | Refresh authentication token              |
| INSUFFICIENT_PERMISSIONS | User lacks required permissions     | Contact administrator for access          |
| SERVICE_UNAVAILABLE      | CDL service temporarily unavailable | Retry with exponential backoff            |
| INVALID_CUSTOMER_NUMBER  | Customer number format invalid      | Use valid customer number format          |
| SEARCH_TIMEOUT           | Search operation timed out          | Reduce search scope or retry              |

---

## Implementation Examples

### TypeScript SDK Implementation

```typescript
interface CDLSearchCriteria {
  customerNumber?: string;
  companyName?: string;
  contactName?: string;
  phoneNumber?: string;
  email?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  taxId?: string;
  accountType?: string;
}

interface CDLSearchOptions {
  fuzzyMatch?: boolean;
  maxResults?: number;
  includeInactive?: boolean;
  sortBy?: 'relevance' | 'companyName' | 'customerNumber' | 'lastActivity';
  sortOrder?: 'asc' | 'desc';
}

interface CDLAccount {
  accountId: string;
  customerNumber: string;
  companyName: string;
  contactInfo: {
    primaryContact: string;
    email: string;
    phone: string;
    fax?: string;
    website?: string;
  };
  address: {
    street: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  accountDetails: {
    accountType: string;
    status: string;
    creditLimit: number;
    paymentTerms: string;
    taxExempt: boolean;
    taxId: string;
    industry?: string;
    salesRep?: string;
  };
  metadata: {
    createdDate: string;
    lastModified: string;
    lastActivity: string;
    source: string;
  };
  matchScore: number;
}

class CDLService {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string, authToken: string) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        Authorization: `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new CDLError(
        error.error.code,
        error.error.message,
        response.status
      );
    }

    return response.json();
  }

  // Search accounts by multiple criteria
  async searchAccounts(
    criteria: CDLSearchCriteria,
    options: CDLSearchOptions = {}
  ): Promise<CDLSearchResponse> {
    return this.request('/api/accounts/search', {
      method: 'POST',
      body: JSON.stringify({
        searchCriteria: criteria,
        searchOptions: {
          fuzzyMatch: true,
          maxResults: 50,
          includeInactive: false,
          sortBy: 'relevance',
          sortOrder: 'desc',
          ...options
        }
      })
    });
  }

  // Quick lookup by customer number
  async lookupByCustomerNumber(
    customerNumber: string,
    includeHistory = false,
    includeContacts = true
  ): Promise<CDLAccount> {
    const params = new URLSearchParams({
      includeHistory: includeHistory.toString(),
      includeContacts: includeContacts.toString()
    });

    return this.request(`/api/accounts/lookup/${customerNumber}?${params}`);
  }

  // Search by phone number
  async searchByPhone(phoneNumber: string): Promise<CDLSearchResponse> {
    return this.request(
      `/api/accounts/search/phone/${encodeURIComponent(phoneNumber)}`
    );
  }

  // Search by email
  async searchByEmail(email: string): Promise<CDLSearchResponse> {
    return this.request(
      `/api/accounts/search/email/${encodeURIComponent(email)}`
    );
  }

  // Search by tax ID
  async searchByTaxId(taxId: string): Promise<CDLSearchResponse> {
    return this.request(
      `/api/accounts/search/taxid/${encodeURIComponent(taxId)}`
    );
  }

  // Advanced address search
  async searchByAddress(
    address: CDLSearchCriteria['address'],
    searchRadius = 5,
    radiusUnit: 'miles' | 'kilometers' = 'miles',
    exactMatch = false
  ): Promise<CDLSearchResponse> {
    return this.request('/api/accounts/search/address', {
      method: 'POST',
      body: JSON.stringify({
        address,
        searchRadius,
        radiusUnit,
        exactMatch
      })
    });
  }
}

class CDLError extends Error {
  constructor(
    public code: string,
    message: string,
    public statusCode: number
  ) {
    super(message);
    this.name = 'CDLError';
  }
}
```

### Usage Examples

```typescript
// Initialize CDL service
const cdlService = new CDLService(
  'https://cdl-test.milwaukeetool.com/api',
  'your-auth-token'
);

// Example 1: Search by company name
try {
  const results = await cdlService.searchAccounts(
    {
      companyName: 'Milwaukee Tool'
    },
    {
      fuzzyMatch: true,
      maxResults: 10
    }
  );

  console.log(`Found ${results.accounts.length} accounts`);
  results.accounts.forEach((account) => {
    console.log(`${account.companyName} - ${account.customerNumber}`);
  });
} catch (error) {
  if (error instanceof CDLError) {
    console.error(`CDL Error: ${error.code} - ${error.message}`);
  }
}

// Example 2: Quick customer lookup
try {
  const account = await cdlService.lookupByCustomerNumber(
    'CUST001',
    true,
    true
  );
  console.log(`Account: ${account.companyName}`);
  console.log(`Status: ${account.accountDetails.status}`);
} catch (error) {
  console.error('Customer not found');
}

// Example 3: Phone number search
try {
  const results = await cdlService.searchByPhone('(*************');
  if (results.accounts.length > 0) {
    console.log(`Found account: ${results.accounts[0].companyName}`);
  }
} catch (error) {
  console.error('Phone search failed');
}

// Example 4: Advanced search with multiple criteria
try {
  const results = await cdlService.searchAccounts(
    {
      companyName: 'Milwaukee',
      address: {
        state: 'WI',
        city: 'Brookfield'
      },
      accountType: 'Commercial'
    },
    {
      fuzzyMatch: true,
      maxResults: 25,
      sortBy: 'lastActivity'
    }
  );

  console.log(`Found ${results.pagination.totalResults} matching accounts`);
} catch (error) {
  console.error('Search failed:', error);
}
```

---

## Performance Considerations

### Caching Strategy

**Client-Side Caching**:

- Cache successful lookups for 5 minutes
- Cache "not found" results for 1 minute
- Implement LRU cache with 1000 entry limit

**CDL Service Caching**:

- Database query results cached for 2 minutes
- Frequently accessed accounts cached for 15 minutes
- Cache invalidation on account updates

### Rate Limiting

| Operation       | Limit | Window   | Burst |
| --------------- | ----- | -------- | ----- |
| Search requests | 100   | 1 minute | 10    |
| Lookup requests | 200   | 1 minute | 20    |
| Bulk operations | 10    | 1 minute | 2     |

### Optimization Guidelines

1. **Use specific search criteria** to reduce result sets
2. **Implement request debouncing** for user input
3. **Cache frequently accessed accounts** locally
4. **Use pagination** for large result sets
5. **Implement retry logic** with exponential backoff

### Performance Metrics

| Metric               | Target  | Threshold |
| -------------------- | ------- | --------- |
| Search response time | < 500ms | < 1000ms  |
| Lookup response time | < 200ms | < 500ms   |
| Cache hit ratio      | > 80%   | > 60%     |
| Error rate           | < 1%    | < 5%      |

---

## Integration Checklist

### Development Phase

- [ ] Set up CDL service authentication
- [ ] Implement basic search functionality
- [ ] Add error handling and retry logic
- [ ] Implement client-side caching
- [ ] Add request rate limiting
- [ ] Create unit tests for all search methods
- [ ] Test with various search criteria combinations

### Testing Phase

- [ ] Validate all search parameter combinations
- [ ] Test error scenarios and edge cases
- [ ] Performance testing with large datasets
- [ ] Load testing for concurrent users
- [ ] Security testing for authentication
- [ ] Integration testing with OnePoint workflows

### Production Deployment

- [ ] Configure production CDL endpoints
- [ ] Set up monitoring and alerting
- [ ] Implement logging for audit trails
- [ ] Configure backup authentication methods
- [ ] Set up performance dashboards
- [ ] Document troubleshooting procedures

---

## Monitoring and Troubleshooting

### Key Metrics to Monitor

1. **Response Times**: Track average and 95th percentile response times
2. **Error Rates**: Monitor 4xx and 5xx error percentages
3. **Cache Performance**: Track hit/miss ratios and cache size
4. **Rate Limiting**: Monitor throttled requests
5. **Authentication**: Track token refresh frequency and failures

### Common Issues and Solutions

| Issue                   | Symptoms            | Solution                                                |
| ----------------------- | ------------------- | ------------------------------------------------------- |
| Slow search performance | High response times | Optimize search criteria, check CDL service status      |
| Authentication failures | 401 errors          | Refresh tokens, check token expiration                  |
| Rate limiting           | 429 errors          | Implement request throttling, increase limits if needed |
| No search results       | Empty result sets   | Verify search criteria, check data availability         |
| Service unavailable     | 503 errors          | Check CDL service status, implement fallback            |

### Logging Requirements

**Request Logging**:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_12345",
  "operation": "searchAccounts",
  "criteria": {
    "companyName": "Milwaukee Tool",
    "state": "WI"
  },
  "responseTime": 245,
  "resultCount": 5,
  "cacheHit": false
}
```

**Error Logging**:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_12345",
  "operation": "lookupByCustomerNumber",
  "error": {
    "code": "CUSTOMER_NOT_FOUND",
    "message": "Customer CUST999 not found",
    "statusCode": 404
  },
  "customerNumber": "CUST999"
}
```

---

## Support and Maintenance

### Support Contacts

| Role                 | Contact                         | Availability   |
| -------------------- | ------------------------------- | -------------- |
| CDL Service Team     | <EMAIL>   | 24/7           |
| OnePoint Development | <EMAIL>  | Business hours |
| Infrastructure       | <EMAIL> | 24/7           |

### Maintenance Windows

- **Regular Maintenance**: Sundays 2:00-4:00 AM CST
- **Emergency Maintenance**: As needed with 2-hour notice
- **Planned Upgrades**: Monthly, first Saturday 10:00 PM - 2:00 AM CST

### Documentation Updates

- **API Changes**: 30-day advance notice
- **Breaking Changes**: 90-day advance notice
- **Documentation**: Updated within 5 business days of changes

**Last Updated**: January 2024
**Document Version**: 1.0
**Integration Version**: 2.1
try {
const results = await cdlService.searchByPhone('(*************');
if (results.accounts.length > 0) {
console.log(`Found account: ${results.accounts[0].companyName}`);
}
} catch (error) {
console.error('Phone search failed');
}

// Example 4: Advanced search with multiple criteria
try {
const results = await cdlService.searchAccounts(
{
companyName: 'Milwaukee',
address: {
state: 'WI',
city: 'Brookfield'
},
accountType: 'Commercial'
},
{
fuzzyMatch: true,
maxResults: 25,
sortBy: 'lastActivity'
}
);

console.log(`Found ${results.pagination.totalResults} matching accounts`);
} catch (error) {
console.error('Search failed:', error);
}

```

```

```

```
