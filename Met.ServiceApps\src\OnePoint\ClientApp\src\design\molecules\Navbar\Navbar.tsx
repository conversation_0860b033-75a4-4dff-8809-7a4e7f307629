'use client';

import { RolePicker } from '@/auth';
import { ChevronUpIcon, maxWidthBP, UserCircleIcon } from '@/design/atoms';
import { PageAreaBox } from '@/design/organisms';
import { setSelectedBranch } from '@/features/branch';
import { setSelectedHub } from '@/features/hub';
import {
  clearOrderGroups,
  clearSelectedProducts,
  clearServiceCart,
  OrderGroupState
} from '@/features/serviceOrder';
import { useAppDispatch } from '@/store/hooks';
import { clearSession, updateSession } from '@/store/local';
import { useMsal } from '@azure/msal-react';
import AppBar from '@mui/material/AppBar';
import Badge from '@mui/material/Badge';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Toolbar from '@mui/material/Toolbar';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTranslation } from 'next-export-i18n';
import Image from 'next/legacy/image';
import { useRouter } from 'next/navigation';
import React, { MouseEvent } from 'react';
import { NavbarMenuOptionCode } from './models';
interface Props {
  savedOrderGroups: OrderGroupState[];
  closeNotification?: () => void;
}

export const Navbar = (props: Props) => {
  const { instance } = useMsal();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const Mobile = useMediaQuery(`(max-width:${maxWidthBP.xs}px)`);
  const [anchorElUser, setAnchorElUser] = React.useState<null | HTMLElement>(
    null
  );
  const logOut = (): Promise<void> => instance.logout();
  const router = useRouter();
  const menuOptions = [
    {
      code: NavbarMenuOptionCode.SavedOrders,
      label: t('features.order.savedOrders'),
      disabled: props.savedOrderGroups.length === 0
    },
    {
      code: NavbarMenuOptionCode.Logout,
      label: t('features.home.logout'),
      disabled: false
    }
  ];

  const displaySavedOrders = () => router.push('/serviceOrder/savedorders');

  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleMenuItemClick = (
    e: MouseEvent,
    menuCode: NavbarMenuOptionCode
  ) => {
    switch (menuCode) {
      case NavbarMenuOptionCode.Logout:
        clearSession();
        logOut();
        break;
      case NavbarMenuOptionCode.SavedOrders:
        displaySavedOrders();
        break;
      default:
        break;
    }
  };

  const MenuItemInfo = ({
    label,
    code
  }: {
    label: string;
    code: NavbarMenuOptionCode;
  }): React.ReactNode => {
    const displayText = <Typography textAlign="center">{label}</Typography>;
    switch (code) {
      case NavbarMenuOptionCode.SavedOrders:
        return (
          <>
            {displayText}
            <Badge
              badgeContent={props.savedOrderGroups.length > 0 ? ' ' : 0}
              sx={{
                '& .MuiBadge-badge': {
                  backgroundColor: 'brandVariables.crimperRed',
                  top: '-4px',
                  border: '2px solid',
                  borderRadius: '16px',
                  borderColor: 'background.default'
                },
                '&:hover': {
                  borderColor: 'inherit'
                },
                ml: '12px'
              }}
            ></Badge>
          </>
        );
      default:
        return displayText;
    }
  };

  const handleChoseRole = () => {
    handleCloseUserMenu();
    dispatch(clearOrderGroups());
    dispatch(clearSelectedProducts());
    dispatch(clearServiceCart());
    updateSession({ selectedBranch: undefined, selectedHub: undefined });
    dispatch(setSelectedBranch(undefined));
    dispatch(setSelectedHub(undefined));
    if (props.closeNotification) {
      props.closeNotification();
    }
  };

  return (
    <>
      <AppBar
        sx={{
          bgcolor: 'common.white',
          borderBottomWidth: '1px',
          borderBottomStyle: 'solid',
          borderBottomColor: 'neutral.400',
          boxShadow: 1,
          maxWidth: maxWidthBP,
          zIndex: 11
        }}
        position="fixed"
      >
        <PageAreaBox pageAreaBoxName="Navbar">
          <Toolbar
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
              maxWidth: { ...maxWidthBP, xl: '1460px' }
            }}
          >
            <Box
              sx={{
                width: '64px'
              }}
            ></Box>
            <Image
              className="relative dark:drop-shadow-[0_0_0.3rem_#ffffff70] dark:invert"
              src="/onepoint-logo.svg"
              alt="OnePoint Logo"
              width={Mobile ? 128 : 180}
              height={Mobile ? 36 : 48}
              priority
            />
            <Box>
              <Tooltip title={t('design.molecules.navbar.openSettings')}>
                <IconButton
                  data-testid="navOptionsUser"
                  onClick={handleOpenUserMenu}
                  sx={{
                    borderRadius: '4px',
                    p: '0',
                    '&:hover': { bgcolor: 'shades.white' }
                  }}
                >
                  <Badge
                    badgeContent={props.savedOrderGroups.length > 0 ? ' ' : 0}
                    sx={{
                      '& .MuiBadge-badge': {
                        backgroundColor: 'brandVariables.crimperRed',
                        border: '2px solid',
                        borderColor: 'common.white',
                        top: '10px'
                      }
                    }}
                  >
                    <UserCircleIcon
                      sx={{
                        width: { xs: '24px', sm: '40px' },
                        height: { xs: '24px', sm: '40px' },
                        color: 'neutral.800'
                      }}
                      viewBox="0 0 32 32"
                    />
                  </Badge>
                  <ChevronUpIcon
                    width="16"
                    height="10"
                    viewBox="-10 0 32 10"
                    sx={{
                      color: 'neutral.800'
                    }}
                  />
                </IconButton>
              </Tooltip>
              <Menu
                sx={{ mt: '45px' }}
                id="menu-appbar"
                data-testid="menu-appbar-testId"
                anchorEl={anchorElUser}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
              >
                {menuOptions.map((option) => (
                  <MenuItem
                    key={option.code}
                    disabled={option.disabled}
                    onClick={(e) => handleMenuItemClick(e, option.code)}
                    data-testid={`settingMenuItem-${option.code}`}
                  >
                    <MenuItemInfo label={option.label} code={option.code} />
                  </MenuItem>
                ))}
                <RolePicker
                  onChange={handleChoseRole}
                  data-testid="navbarRolePicker"
                />
              </Menu>
            </Box>
          </Toolbar>
        </PageAreaBox>
      </AppBar>
      {/* We need this because the navbar has position: fixed. 
          The margin will ‘fill’ the space behind the fixed navbar */}
      <Box
        sx={{
          mt: '65px'
        }}
      ></Box>
    </>
  );
};
