import { renderWithProviders } from '@/../__test-utils__/renderWithProviders';
import { SelectDefaultOption } from '@/design/molecules';
import { AccountSearch, AccountSearchSchemaType } from '@/features/search';
import '@testing-library/jest-dom';
import {
  fireEvent,
  getByRole,
  renderHook,
  screen,
  waitFor
} from '@testing-library/react';
import UserEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';

jest.mock('next-export-i18n', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

describe('AccountSearch Component', () => {
  it('renderWithProviderss the component correctly', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);
    expect(screen.getByTestId('accountSearchInput-testId')).toBeInTheDocument();
    expect(
      screen.getByTestId('select-stateOrProvince-testId')
    ).toBeInTheDocument();
  });

  it('calls onSearchTextChange when account search text changes', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, getValues, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);
    const input = screen
      .getByTestId('accountSearchInput-testId')
      .querySelector('input') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'new search text' } });
    const values = getValues();
    expect(values.accountSearchText).toBe('new search text');
  });

  it('calls onStateChange when state or province selection changes', async () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, getValues, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);

    const select = getByRole(
      screen.getByTestId('select-stateOrProvince-testId'),
      'combobox'
    );

    await UserEvent.click(select);
    await waitFor(() => UserEvent.click(screen.getByText('Alabama')));

    expect(mockProps.onStateChange).toHaveBeenCalledTimes(1);

    const values = getValues();
    expect(values.stateOrProvince).toBe('AL');
  });

  it('clears account search text when close icon is clicked', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, getValues, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);

    const input = screen
      .getByTestId('accountSearchInput-testId')
      .querySelector('input') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'new search text' } });
    let values = getValues();
    expect(values.accountSearchText).toBe('new search text');

    const closeButton = screen.getByTestId('closeIconBtn-testId');
    fireEvent.click(closeButton);

    values = getValues();
    expect(values.accountSearchText).toBe('');
  });

  it('calls handleSearchIcon when search icon is clicked', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: 'search text',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);
    const searchIcon = screen.getByTestId('searchIcon-testId');
    fireEvent.click(searchIcon);
    expect(mockProps.onSearch).toHaveBeenCalledTimes(1);
  });

  it('displays the clear button when account search text is not empty', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: 'search text',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);
    expect(screen.getByTestId('closeIconBtn-testId')).toBeInTheDocument();
  });

  it('does not display the clear button when account search text is empty', () => {
    const defaultValues: AccountSearchSchemaType = {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    };
    const { result } = renderHook(() =>
      useForm<AccountSearchSchemaType>({ defaultValues })
    );
    const { control, register, resetField } = result.current;

    const mockProps = {
      isFetching: false,
      accountSearchText: '',
      onSearch: jest.fn(),
      handleContinueAsGuestClick: jest.fn(),
      onStateChange: jest.fn(),
      register: register,
      resetField: resetField,
      control: control
    };
    renderWithProviders(<AccountSearch {...mockProps} />);
    expect(screen.queryByTestId('closeIconBtn-testId')).not.toBeInTheDocument();
  });
});
