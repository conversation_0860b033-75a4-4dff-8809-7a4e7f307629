import { AccountCard, SiteItem } from '@/features/account';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';

const mockedSite: SiteItem = {
  id: '***************',
  siteNumber: '*********',
  siteName: 'FERGUSON ENTERPRISES 0738',
  addressLine1: '134-07 NORTHERN BLVD',
  addressLine2: 'Somewhere',
  city: 'FLUSHING',
  state: 'NY',
  postalCode: '11354',
  country: 'United States',
  serviceProgram: 'Red Tote',
  paymentTerms: 'Credit Card',
  parentAccount: {
    id: '',
    accountNumber: '73932',
    accountDescription: 'FERGUSON ENTERPRISES INC'
  }
};

describe('AccountCard', () => {
  it('renders account information correctly', () => {
    render(<AccountCard site={mockedSite} />);

    expect(screen.getByTestId('siteName')).toHaveTextContent(
      'FERGUSON ENTERPRISES 0738'
    );
    expect(screen.getByTestId('siteAddress')).toHaveTextContent(
      '134-07 NORTHERN BLVD, Somewhere, FLUSHING, NY 11354'
    );
  });
});
