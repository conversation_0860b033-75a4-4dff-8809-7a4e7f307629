'use client';

import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import Link from 'next/link';

export default function NotFoundPage() {
  const { t } = useTranslation();

  return (
    <Grid
      container
      spacing={0}
      direction="column"
      alignItems="center"
      justifyContent="center"
      sx={{ minHeight: '100vh' }}
    >
      <Grid item xs={3} textAlign="center">
        <Typography variant="h3">{t('common.pageNotFound')}</Typography>
        <Link data-testid="backToHomeLink" href="/">
          {t('common.backToHome')}
        </Link>
      </Grid>
    </Grid>
  );
}
