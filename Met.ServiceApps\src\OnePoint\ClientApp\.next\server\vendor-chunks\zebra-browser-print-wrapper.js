"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zebra-browser-print-wrapper";
exports.ids = ["vendor-chunks/zebra-browser-print-wrapper"];
exports.modules = {

/***/ "(ssr)/./node_modules/zebra-browser-print-wrapper/lib/constants.js":
/*!*******************************************************************!*\
  !*** ./node_modules/zebra-browser-print-wrapper/lib/constants.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.API_URL = void 0;\nexports.API_URL = \"http://localhost:9100/\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvemVicmEtYnJvd3Nlci1wcmludC13cmFwcGVyL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELGVBQWUsR0FBRyxLQUFLO0FBQ3ZCQSxlQUFlLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbmVwb2ludC13ZWIvLi9ub2RlX21vZHVsZXMvemVicmEtYnJvd3Nlci1wcmludC13cmFwcGVyL2xpYi9jb25zdGFudHMuanM/ZmZkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLkFQSV9VUkwgPSB2b2lkIDA7XHJcbmV4cG9ydHMuQVBJX1VSTCA9ICdodHRwOi8vbG9jYWxob3N0OjkxMDAvJztcclxuIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiQVBJX1VSTCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zebra-browser-print-wrapper/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/zebra-browser-print-wrapper/lib/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/zebra-browser-print-wrapper/lib/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __awaiter = (void 0) && (void 0).__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (void 0) && (void 0).__generator || function(thisArg, body) {\n    var _ = {\n        label: 0,\n        sent: function() {\n            if (t[0] & 1) throw t[1];\n            return t[1];\n        },\n        trys: [],\n        ops: []\n    }, f, y, t, g;\n    return g = {\n        next: verb(0),\n        \"throw\": verb(1),\n        \"return\": verb(2)\n    }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() {\n        return this;\n    }), g;\n    function verb(n) {\n        return function(v) {\n            return step([\n                n,\n                v\n            ]);\n        };\n    }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while(_)try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [\n                op[0] & 2,\n                t.value\n            ];\n            switch(op[0]){\n                case 0:\n                case 1:\n                    t = op;\n                    break;\n                case 4:\n                    _.label++;\n                    return {\n                        value: op[1],\n                        done: false\n                    };\n                case 5:\n                    _.label++;\n                    y = op[1];\n                    op = [\n                        0\n                    ];\n                    continue;\n                case 7:\n                    op = _.ops.pop();\n                    _.trys.pop();\n                    continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n                        _ = 0;\n                        continue;\n                    }\n                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n                        _.label = op[1];\n                        break;\n                    }\n                    if (op[0] === 6 && _.label < t[1]) {\n                        _.label = t[1];\n                        t = op;\n                        break;\n                    }\n                    if (t && _.label < t[2]) {\n                        _.label = t[2];\n                        _.ops.push(op);\n                        break;\n                    }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop();\n                    continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) {\n            op = [\n                6,\n                e\n            ];\n            y = 0;\n        } finally{\n            f = t = 0;\n        }\n        if (op[0] & 5) throw op[1];\n        return {\n            value: op[0] ? op[1] : void 0,\n            done: true\n        };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/zebra-browser-print-wrapper/lib/constants.js\");\nvar ZebraBrowserPrintWrapper = /** @class */ function() {\n    function ZebraBrowserPrintWrapper() {\n        var _this = this;\n        this.device = {};\n        this.getAvailablePrinters = function() {\n            return __awaiter(_this, void 0, void 0, function() {\n                var config, endpoint, res, data, error_1;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            config = {\n                                method: \"GET\",\n                                headers: {\n                                    \"Content-Type\": \"text/plain;charset=UTF-8\"\n                                }\n                            };\n                            endpoint = constants_1.API_URL + \"available\";\n                            _a.label = 1;\n                        case 1:\n                            _a.trys.push([\n                                1,\n                                4,\n                                ,\n                                5\n                            ]);\n                            return [\n                                4 /*yield*/ ,\n                                fetch(endpoint, config)\n                            ];\n                        case 2:\n                            res = _a.sent();\n                            return [\n                                4 /*yield*/ ,\n                                res.json()\n                            ];\n                        case 3:\n                            data = _a.sent();\n                            if (data && data !== undefined && data.printer && data.printer !== undefined && data.printer.length > 0) {\n                                return [\n                                    2 /*return*/ ,\n                                    data.printer\n                                ];\n                            }\n                            return [\n                                2 /*return*/ ,\n                                new Error(\"No printers available\")\n                            ];\n                        case 4:\n                            error_1 = _a.sent();\n                            throw new Error(error_1);\n                        case 5:\n                            return [\n                                2 /*return*/ \n                            ];\n                    }\n                });\n            });\n        };\n        this.getDefaultPrinter = function() {\n            return __awaiter(_this, void 0, void 0, function() {\n                var config, endpoint, res, data, deviceRaw, name_1, deviceType, connection, uid, provider, manufacturer, error_2;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            config = {\n                                method: \"GET\",\n                                headers: {\n                                    \"Content-Type\": \"text/plain;charset=UTF-8\"\n                                }\n                            };\n                            endpoint = constants_1.API_URL + \"default\";\n                            _a.label = 1;\n                        case 1:\n                            _a.trys.push([\n                                1,\n                                4,\n                                ,\n                                5\n                            ]);\n                            return [\n                                4 /*yield*/ ,\n                                fetch(endpoint, config)\n                            ];\n                        case 2:\n                            res = _a.sent();\n                            return [\n                                4 /*yield*/ ,\n                                res.text()\n                            ];\n                        case 3:\n                            data = _a.sent();\n                            if (data && data !== undefined && typeof data !== \"object\" && data.split(\"\\n\t\").length === 7) {\n                                deviceRaw = data.split(\"\\n\t\");\n                                name_1 = this.cleanUpString(deviceRaw[1]);\n                                deviceType = this.cleanUpString(deviceRaw[2]);\n                                connection = this.cleanUpString(deviceRaw[3]);\n                                uid = this.cleanUpString(deviceRaw[4]);\n                                provider = this.cleanUpString(deviceRaw[5]);\n                                manufacturer = this.cleanUpString(deviceRaw[6]);\n                                return [\n                                    2 /*return*/ ,\n                                    {\n                                        connection: connection,\n                                        deviceType: deviceType,\n                                        manufacturer: manufacturer,\n                                        name: name_1,\n                                        provider: provider,\n                                        uid: uid,\n                                        version: 0\n                                    }\n                                ];\n                            }\n                            throw new Error(\"There's no default printer\");\n                        case 4:\n                            error_2 = _a.sent();\n                            throw new Error(error_2);\n                        case 5:\n                            return [\n                                2 /*return*/ \n                            ];\n                    }\n                });\n            });\n        };\n        this.setPrinter = function(device) {\n            _this.device = device;\n        };\n        this.getPrinter = function() {\n            return _this.device;\n        };\n        this.cleanUpString = function(str) {\n            var arr = str.split(\":\");\n            var result = arr[1].trim();\n            return result;\n        };\n        this.checkPrinterStatus = function() {\n            return __awaiter(_this, void 0, void 0, function() {\n                var result, errors, isReadyToPrint, is_error, media, head, pause;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            return [\n                                4 /*yield*/ ,\n                                this.write(\"~HQES\")\n                            ];\n                        case 1:\n                            _a.sent();\n                            return [\n                                4 /*yield*/ ,\n                                this.read()\n                            ];\n                        case 2:\n                            result = _a.sent();\n                            errors = [];\n                            isReadyToPrint = false;\n                            is_error = result.charAt(70);\n                            media = result.charAt(88);\n                            head = result.charAt(87);\n                            pause = result.charAt(84);\n                            isReadyToPrint = is_error === \"0\";\n                            switch(media){\n                                case \"1\":\n                                    errors.push(\"Paper out\");\n                                    break;\n                                case \"2\":\n                                    errors.push(\"Ribbon Out\");\n                                    break;\n                                case \"4\":\n                                    errors.push(\"Media Door Open\");\n                                    break;\n                                case \"8\":\n                                    errors.push(\"Cutter Fault\");\n                                    break;\n                                default:\n                                    break;\n                            }\n                            switch(head){\n                                case \"1\":\n                                    errors.push(\"Printhead Overheating\");\n                                    break;\n                                case \"2\":\n                                    errors.push(\"Motor Overheating\");\n                                    break;\n                                case \"4\":\n                                    errors.push(\"Printhead Fault\");\n                                    break;\n                                case \"8\":\n                                    errors.push(\"Incorrect Printhead\");\n                                    break;\n                                default:\n                                    break;\n                            }\n                            if (pause === \"1\") errors.push(\"Printer Paused\");\n                            if (!isReadyToPrint && errors.length === 0) errors.push(\"Error: Unknown Error\");\n                            return [\n                                2 /*return*/ ,\n                                {\n                                    isReadyToPrint: isReadyToPrint,\n                                    errors: errors.join()\n                                }\n                            ];\n                    }\n                });\n            });\n        };\n        this.write = function(data) {\n            return __awaiter(_this, void 0, void 0, function() {\n                var endpoint, myData, config, error_3;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            _a.trys.push([\n                                0,\n                                2,\n                                ,\n                                3\n                            ]);\n                            endpoint = constants_1.API_URL + \"write\";\n                            myData = {\n                                device: this.device,\n                                data: data\n                            };\n                            config = {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"text/plain;charset=UTF-8\"\n                                },\n                                body: JSON.stringify(myData)\n                            };\n                            return [\n                                4 /*yield*/ ,\n                                fetch(endpoint, config)\n                            ];\n                        case 1:\n                            _a.sent();\n                            return [\n                                3 /*break*/ ,\n                                3\n                            ];\n                        case 2:\n                            error_3 = _a.sent();\n                            throw new Error(error_3);\n                        case 3:\n                            return [\n                                2 /*return*/ \n                            ];\n                    }\n                });\n            });\n        };\n        this.read = function() {\n            return __awaiter(_this, void 0, void 0, function() {\n                var endpoint, myData, config, res, data, error_4;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            _a.trys.push([\n                                0,\n                                3,\n                                ,\n                                4\n                            ]);\n                            endpoint = constants_1.API_URL + \"read\";\n                            myData = {\n                                device: this.device\n                            };\n                            config = {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"text/plain;charset=UTF-8\"\n                                },\n                                body: JSON.stringify(myData)\n                            };\n                            return [\n                                4 /*yield*/ ,\n                                fetch(endpoint, config)\n                            ];\n                        case 1:\n                            res = _a.sent();\n                            return [\n                                4 /*yield*/ ,\n                                res.text()\n                            ];\n                        case 2:\n                            data = _a.sent();\n                            return [\n                                2 /*return*/ ,\n                                data\n                            ];\n                        case 3:\n                            error_4 = _a.sent();\n                            throw new Error(error_4);\n                        case 4:\n                            return [\n                                2 /*return*/ \n                            ];\n                    }\n                });\n            });\n        };\n        this.print = function(text) {\n            return __awaiter(_this, void 0, void 0, function() {\n                var error_5;\n                return __generator(this, function(_a) {\n                    switch(_a.label){\n                        case 0:\n                            _a.trys.push([\n                                0,\n                                2,\n                                ,\n                                3\n                            ]);\n                            return [\n                                4 /*yield*/ ,\n                                this.write(text)\n                            ];\n                        case 1:\n                            _a.sent();\n                            return [\n                                3 /*break*/ ,\n                                3\n                            ];\n                        case 2:\n                            error_5 = _a.sent();\n                            throw new Error(error_5);\n                        case 3:\n                            return [\n                                2 /*return*/ \n                            ];\n                    }\n                });\n            });\n        };\n    }\n    return ZebraBrowserPrintWrapper;\n}();\nexports[\"default\"] = ZebraBrowserPrintWrapper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zebra-browser-print-wrapper/lib/index.js\n");

/***/ })

};
;