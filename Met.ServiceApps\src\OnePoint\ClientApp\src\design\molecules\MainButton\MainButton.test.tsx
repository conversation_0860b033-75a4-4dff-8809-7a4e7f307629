import { CustomerRecordIcon } from '@/design/atoms';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { MainButton } from './MainButton';

describe('MainButton tests', () => {
  it('Should display passed props', async () => {
    render(
      <MainButton
        testId="MainButton-testid"
        description="test description"
        icon={<CustomerRecordIcon data-testid="customerRecordIcon-testid" />}
        onButtonClick={() => {
          return;
        }}
      />
    );

    expect(screen.getByText('test description')).toBeInTheDocument();
    expect(screen.getByTestId('customerRecordIcon-testid')).toBeInTheDocument();
  });
});
