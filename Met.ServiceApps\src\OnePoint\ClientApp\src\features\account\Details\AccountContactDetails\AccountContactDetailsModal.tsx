import {
  CheckCircleIcon,
  CloseIcon,
  CustomerIcon,
  QuestionCircleOutlineIcon,
  RemoveIcon,
  SearchIconV2
} from '@/design/atoms';
import { Button, TextField, Tooltip } from '@/design/molecules';
import { Modal } from '@/design/organisms';
import {
  clearSelectedCustomer,
  onIsLoadingChange,
  onProfileConfirmed,
  onSelectProfileClick,
  onShowContactDetailsModalChange,
  selectedAccountPageState,
  SiteItem
} from '@/features/account';
import { CustomerCard, setSelectedCustomer } from '@/features/customer';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { Box, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

interface Props {
  site?: SiteItem;
}

export const AccountContactDetailsModal = ({ site }: Props) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { control, getValues } = useForm<{
    dropoffParty: string;
  }>({
    defaultValues: {
      dropoffParty: ''
    }
  });

  const {
    showContactDetailsModal,
    customerProfileSelected,
    redirectToPagePath
  } = useAppSelector(selectedAccountPageState);
  const serviceOrderContactDetailsLabel = t(
    'features.account.details.contactDetailsModalTitle'
  );
  const responsiblePartyLabel = t('features.account.details.responsibleParty');
  const noUserSelectedLabel = t('features.account.details.noUserSelected');
  const selectProfileLabel = t('features.account.details.selectProfile');
  const dropOffPartyLabel = t('features.account.details.dropOffParty');
  const dropOffPartyPlaceHolder = t(
    'features.account.details.dropOffPartyPlaceHolder'
  );
  const selectDifferentProfileLabel = t(
    'features.account.details.differentProfile'
  );
  const responsiblePartyLabelTooltip = t(
    'features.account.details.responsiblePartyTooltip'
  );

  const onNextBtnClick = () => {
    const dropoffPartyForm = getValues();
    // enable loading icon
    dispatch(onIsLoadingChange(true));
    // set site data to show it in repair flow
    dispatch(
      onProfileConfirmed({ site, dropOffParty: dropoffPartyForm.dropoffParty })
    );
    // set repair page customer selected
    dispatch(setSelectedCustomer(customerProfileSelected));
    // disable loading icon
    dispatch(onIsLoadingChange(false));

    if (redirectToPagePath) {
      router.push(redirectToPagePath);
      dispatch(onShowContactDetailsModalChange(false));
    }
  };

  const onCloseBtnClick = () => {
    dispatch(onShowContactDetailsModalChange(false));
  };

  const onSelectProfileBtnClick = () => {
    dispatch(onSelectProfileClick(true));
  };

  const onRemoveProfileSelectedClick = () => {
    dispatch(clearSelectedCustomer());
  };

  return (
    <Modal
      open={showContactDetailsModal}
      width="760px"
      modalName="ContactDetails"
      sx={{
        '.MuiPaper-root': {
          borderRadius: '4px'
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: '16px 20px'
        }}
      >
        <Typography
          variant="h5"
          color="neutral.800"
          sx={{ fontWeight: '600', mr: 1 }}
          data-testid="serviceOrderContactDetailsLabel-testId"
        >
          {serviceOrderContactDetailsLabel}
        </Typography>
        <IconButton
          data-testid="closeIconBtn-testId"
          onClick={onCloseBtnClick}
          sx={{
            color: 'neutral.800'
          }}
        >
          <CloseIcon
            style={{
              width: '24px',
              height: '24px',
              color: 'neutral.800'
            }}
            viewBox="0 0 24 24"
            data-testid="closeBtn-testId"
          />
        </IconButton>
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          backgroundColor: 'common.white',
          bgcolor: 'common.white',
          width: '100%',
          px: '24px',
          gap: '24px'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              color="neutral.800"
              data-testid="responsiblePartyLabel-testId"
              sx={{ fontWeight: '600', mr: 1 }}
            >
              {responsiblePartyLabel}
            </Typography>

            <Tooltip placement="right" title={responsiblePartyLabelTooltip}>
              <Box
                sx={{
                  alignItems: 'center',
                  display: 'flex'
                }}
              >
                <QuestionCircleOutlineIcon
                  sx={{
                    width: '18px',
                    height: '18px'
                  }}
                  viewBox="0 0 24 24"
                  aria-hidden={undefined}
                />
              </Box>
            </Tooltip>
          </Box>

          {customerProfileSelected ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px'
              }}
            >
              {/* Profile selected */}
              <Box
                sx={{
                  display: 'flex',
                  gap: '16px',
                  border: '2px solid',
                  borderColor: 'primary.500',
                  borderRadius: '4px',
                  alignItems: 'center',
                  p: '0 16px 16px 16px'
                }}
              >
                <CheckCircleIcon
                  width="24"
                  height="24"
                  viewBox="0 0 16 16"
                  data-testid={`checkIcon-testId`}
                />
                <CustomerCard
                  customer={customerProfileSelected}
                  iconSx={{
                    width: '40px',
                    height: '40px'
                  }}
                  showOnlyCustomerInfo
                  containerSxProps={{
                    alignItems: 'center',
                    p: 0
                  }}
                  textVariant="p1"
                  titleVariant="p2"
                />
                <IconButton
                  onClick={onRemoveProfileSelectedClick}
                  data-testid="removeProfile-testId"
                  sx={{ marginLeft: 'auto', color: 'neutral.800' }}
                >
                  <RemoveIcon
                    style={{
                      width: '24px',
                      height: '24px'
                    }}
                    viewBox="0 0 16 18"
                  />
                </IconButton>
              </Box>
              <Button
                variant="secondary"
                startIcon={<SearchIconV2 viewBox="0 0 16 17" />}
                onClick={onSelectProfileBtnClick}
                data-testid="selectDifferentProfileBtn-testId"
                sx={{
                  width: 'fit-content',
                  fontSize: '16px'
                }}
              >
                {selectDifferentProfileLabel}
              </Button>
            </Box>
          ) : (
            <Box
              sx={{
                border: '2px dashed',
                borderColor: 'neutral.400',
                backgroundColor: 'neutral.50',
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                p: '24px 36px',
                alignItems: 'center',
                gap: '16px'
              }}
            >
              {/* Select profile */}
              <CustomerIcon
                data-testid="customerIcon-testId"
                viewBox="0 0 56 56"
                sx={{
                  width: '48px',
                  height: '48px',
                  color: 'neutral.200'
                }}
              />
              <Typography
                variant="h6"
                color="neutral.500"
                sx={{ fontWeight: '600', mr: 1 }}
                data-testid="noUserSelectedLabel-testId"
              >
                {noUserSelectedLabel}
              </Typography>

              <Button
                variant="primary"
                size="large"
                startIcon={<SearchIconV2 viewBox="0 0 16 17" />}
                onClick={onSelectProfileBtnClick}
                data-testid="selectProfileBtn-testId"
                sx={{
                  width: 'fit-content',
                  fontSize: '16px'
                }}
              >
                {selectProfileLabel}
              </Button>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              color="neutral.800"
              sx={{ fontWeight: '600', mr: 1 }}
              data-testid="dropOffPartyLabel-testId"
            >
              {dropOffPartyLabel}
            </Typography>
            <QuestionCircleOutlineIcon
              sx={{
                width: '18px',
                height: '18px'
              }}
              viewBox="0 0 24 24"
            />
          </Box>
          <TextField
            placeHolder={dropOffPartyPlaceHolder}
            form={{
              control,
              name: 'dropoffParty'
            }}
            maxCharacters={3}
            maxCharactersHelperText={true}
            data-testid="dropOffPartyInput-testId"
            sx={{
              backgroundColor: 'common.white',
              height: { xs: '64px' },
              borderRadius: '4px',
              '& .MuiInputBase-input': {
                fontSize: { xs: '14px', sm: '18px' }
              },
              '& .MuiInputBase-root': {
                px: { xs: '4px', sm: '6px', md: '10px' },
                height: { xs: '66px' }
              },
              '& .MuiIconButton-root': {
                px: { xs: '0px', md: '4px' }
              }
            }}
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            justifyContent: 'flex-end',
            gap: '16px'
          }}
        >
          <Button
            variant="tertiary"
            data-testid="cancelBtn-testId"
            onClick={onCloseBtnClick}
          >
            {t('common.cancel')}
          </Button>

          <Button
            variant="primary"
            onClick={onNextBtnClick}
            data-testid="nextBtn-testId"
            disabled={!customerProfileSelected}
          >
            {t('common.next')}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};
