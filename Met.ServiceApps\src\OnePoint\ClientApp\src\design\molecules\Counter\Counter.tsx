import PlusIcon from '@mui/icons-material/Add';
import MinusIcon from '@mui/icons-material/Remove';
import { Box, Button, TextField } from '@mui/material';
import { useState } from 'react';

interface Props {
  min?: number;
  max?: number;
  initialValue: number;
  value?: number;
  onUpdatedCounter: (value: number) => void;
  disabled?: boolean;
  step?: number;
}
const round = (value: number) => Number(value.toFixed(10));

export const Counter: React.FC<Props> = ({ step = 1, ...props }: Props) => {
  const [value, setValue] = useState(props.value ?? props.initialValue);
  const currentValue = props.value ?? value;

  const updateCounter = (delta: number) => {
    if (props.disabled) return;
    const newValue = round(currentValue + delta);
    setValue(newValue);
    props.onUpdatedCounter(newValue);
  };

  const handleIncrement = () => {
    const newValue = round(currentValue + step);
    if (props.max !== undefined && newValue > props.max) return;
    updateCounter(step);
  };
  const handleDecrement = () => {
    const newValue = round(currentValue - step);
    if (props.min !== undefined && newValue < props.min) return;
    updateCounter(-step);
  };

  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const result = Number(e.target.value);

    if (isNaN(result)) setValue(value);
    else setValue(result);

    props.onUpdatedCounter(result);
  };

  return (
    <Box
      sx={{
        opacity: props.disabled ? 0.5 : 1,
        pointerEvents: props.disabled ? 'none' : 'auto'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          orientation: 'row',
          border: '2px solid',
          borderRadius: '4px',
          borderColor: 'neutral.300'
        }}
      >
        <Button
          data-testid="counterDecrementButton-testId"
          color="primary"
          onClick={handleDecrement}
          sx={{
            borderRight: '2px solid',
            borderRadius: '4px 0 0 4px',
            borderColor: 'neutral.300',
            justifySelf: 'center',
            minWidth: '48px',
            height: '48px',
            backgroundColor: 'neutral.100',
            boxSizing: 'border-box',
            padding: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          disabled={
            props.disabled ||
            (props.min !== undefined && currentValue <= props.min)
          }
        >
          <MinusIcon />
        </Button>
        <Box
          sx={{
            justifySelf: 'center',
            padding: '0px 15px',
            alignSelf: 'center',
            textAlign: 'center',
            minWidth: '48px'
          }}
        >
          <TextField
            variant="standard"
            margin="none"
            sx={{ width: '48px', input: { textAlign: 'center' } }}
            InputProps={{
              disableUnderline: true
            }}
            onChange={handleOnChange}
            value={props.value !== undefined ? props.value : value}
            data-testid="counter-textfield-testid"
          ></TextField>
        </Box>
        <Button
          data-testid="counterIncrementButton-testId"
          color="primary"
          onClick={handleIncrement}
          sx={{
            borderLeft: '2px solid',
            borderColor: 'neutral.300',
            borderRadius: '0 4px 4px 0',
            minWidth: '48px',
            height: '48px',
            backgroundColor: 'neutral.100',
            boxSizing: 'border-box',
            padding: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          disabled={
            props.disabled ||
            (props.max !== undefined && currentValue >= props.max)
          }
        >
          <PlusIcon />
        </Button>
      </Box>
    </Box>
  );
};
