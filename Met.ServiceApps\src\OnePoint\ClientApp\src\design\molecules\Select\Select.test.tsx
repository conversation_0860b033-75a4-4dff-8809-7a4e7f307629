import '@testing-library/jest-dom';
import { act, render, screen } from '@testing-library/react';
import 'jest-canvas-mock';
import { Select } from './Select';

describe('Select component', () => {
  it('Renders Select component successfully', async () => {
    render(
      <Select
        name="testSelect"
        value={''}
        required={true}
        error={false}
        items={[{ id: '1', value: 'test' }]}
        labelText={'Test Select'}
      />
    );

    await act(async () => {
      expect(
        screen.getByTestId('select-testSelect-testId')
      ).toBeInTheDocument();
    });
  });
});
