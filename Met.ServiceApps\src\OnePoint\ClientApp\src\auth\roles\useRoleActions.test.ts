import { renderHook } from '@testing-library/react';
import { useRoleContext } from './RoleProvider';
import { Role } from './role';
import { useRoleActions } from './useRoleActions';

jest.mock('./RoleProvider');

const testCases = [
  { role: Role.BranchManager, expected: true },
  { role: Role.BranchAssociate, expected: true },
  { role: Role.CX, expected: false },
  { role: Role.JSS, expected: false },
  { role: Role.DigitalCare, expected: false },
  { role: Role.ProductMarketing, expected: false }
];

const mockCurrentRole = (role: Role) => {
  (useRoleContext as jest.Mock).mockReturnValue({
    currentRole: role
  });
};

describe('useRoleActions', () => {
  describe('hasBranchSelection', () => {
    testCases.forEach(({ role, expected }) => {
      it(`should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { hasBranchSelection } = renderHook(() => useRoleActions()).result
          .current;

        expect(hasBranchSelection).toBe(expected);
      });
    });
  });
  describe('canStartRepair', () => {
    [...testCases, { role: Role.Hub, expected: true }].forEach(
      ({ role, expected }) => {
        it(`should return ${expected} for ${role}`, () => {
          mockCurrentRole(role);

          const { canStartRepair } = renderHook(() => useRoleActions()).result
            .current;

          expect(canStartRepair).toBe(expected);
        });
      }
    );
  });
  describe('canStartSale', () => {
    testCases.forEach(({ role, expected }) => {
      it(`should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canStartSale } = renderHook(() => useRoleActions()).result
          .current;

        expect(canStartSale).toBe(expected);
      });
    });
  });
  describe('canCreateCustomer', () => {
    const testCasesCreateCustomer = testCases.map((testCase) =>
      testCase.role === Role.Hub ? { ...testCase, expected: true } : testCase
    );

    testCasesCreateCustomer.forEach(({ role, expected }) => {
      it(`should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canCreateCustomer } = renderHook(() => useRoleActions()).result
          .current;

        expect(canCreateCustomer).toBe(expected);
      });
    });
  });
  describe('canViewServiceHistory', () => {
    const testCasesServiceHistory = testCases.map((testCase) =>
      testCase.role === Role.Hub ? { ...testCase, expected: true } : testCase
    );

    testCasesServiceHistory.forEach(({ role, expected }) => {
      it(`should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canViewServiceHistory } = renderHook(() => useRoleActions())
          .result.current;

        expect(canViewServiceHistory).toBe(expected);
      });
    });
  });

  describe('canCreateServiceRequest', () => {
    const cases = [
      { role: Role.BranchManager, expected: false },
      { role: Role.BranchAssociate, expected: false },
      { role: Role.Hub, expected: true }
    ];

    cases.forEach(({ role, expected }) => {
      it(`Should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canCreateServiceRequest } = renderHook(() => useRoleActions())
          .result.current;

        expect(canCreateServiceRequest).toBe(expected);
      });
    });
  });

  describe('canSearchServiceRequest', () => {
    const cases = [
      { role: Role.BranchManager, expected: false },
      { role: Role.BranchAssociate, expected: false },
      { role: Role.Hub, expected: true }
    ];

    cases.forEach(({ role, expected }) => {
      it(`Should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canSearchServiceRequest } = renderHook(() => useRoleActions())
          .result.current;

        expect(canSearchServiceRequest).toBe(expected);
      });
    });
  });

  describe('canViewAccountLookup', () => {
    const cases = [
      { role: Role.BranchManager, expected: false },
      { role: Role.BranchAssociate, expected: false },
      { role: Role.Hub, expected: true }
    ];

    cases.forEach(({ role, expected }) => {
      it(`Should return ${expected} for ${role}`, () => {
        mockCurrentRole(role);

        const { canViewAccountLookup } = renderHook(() => useRoleActions())
          .result.current;

        expect(canViewAccountLookup).toBe(expected);
      });
    });
  });

  describe('canSearchCustomer', () => {
    const cases = Object.values(Role).filter((role) => role !== Role.Hub);

    cases.forEach((role) => {
      it(`Should return true for ${role}`, () => {
        mockCurrentRole(role);

        const { canSearchCustomer } = renderHook(() => useRoleActions()).result
          .current;

        expect(canSearchCustomer).toBe(true);
      });
    });
  });
});
