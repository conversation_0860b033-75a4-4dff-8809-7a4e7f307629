# OnePoint API Documentation

> **API Overview**
> 
> This document provides comprehensive documentation for the OnePoint API endpoints used in the Milwaukee Tool service application.
> 
> **Base URL:** https://onepoint-test.milwaukeetool.com/api  
> **API Version:** v1.0  
> **Last Updated:** January 2024

## Table of Contents

1. [Authentication](#authentication)
2. [Rate Limiting](#rate-limiting)
3. [Pricing Endpoints](#pricing-endpoints)
4. [Warranty Endpoints](#warranty-endpoints)
5. [Product Endpoints](#product-endpoints)
6. [Service Order Endpoints](#service-order-endpoints)
7. [Repair Details Endpoints](#repair-details-endpoints)
8. [Payment Endpoints](#payment-endpoints)
9. [Error Handling](#error-handling)
10. [Data Types](#data-types)
11. [SDK Examples](#sdk-examples)

## Authentication

All API endpoints require authentication using Bearer tokens:

```
Authorization: Bearer {your-jwt-token}
```

### Required Headers

| Header | Value | Description |
|--------|-------|-------------|
| Authorization | Bearer {token} | JWT authentication token |
| Content-Type | application/json | For POST/PUT requests |
| Accept | application/json | Expected response format |

## Rate Limiting

> **Important:** Rate limiting information
> 
> - **Rate Limit:** 1000 requests per hour per API key
> - **Burst Limit:** 100 requests per minute
> 
> Rate limit information is returned in response headers:
> - X-RateLimit-Limit: Total requests allowed per hour
> - X-RateLimit-Remaining: Requests remaining in current window
> - X-RateLimit-Reset: Time when rate limit resets

## Pricing Endpoints

### Get Service Pricing

Retrieves pricing information for service items based on specified criteria.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/Pricing/servicepricing`

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| PriceType | integer | ✓ | Type of pricing (1 = Service, 2 = Retail, etc.) |
| CustomerType | integer | ✓ | Customer classification (1 = Retail, 2 = Commercial, etc.) |
| Currency | integer | ✓ | Currency code (1 = USD, 2 = CAD, etc.) |
| SvcCategory | string | ✗ | Service category filter |
| ItemCodes | array | ✓ | Array of item codes to get pricing for |

#### Example Request

```http
GET /api/Pricing/servicepricing?PriceType=1&CustomerType=2&Currency=1&SvcCategory=&ItemCodes%5B0%5D=271120
```

#### Response Format

```json
{
  "items": [
    {
      "itemCode": "271120",
      "price": 45.99,
      "currency": "USD",
      "priceType": 1,
      "customerType": 2,
      "effectiveDate": "2024-01-01T00:00:00Z",
      "expirationDate": "2024-12-31T23:59:59Z"
    }
  ],
  "success": true,
  "message": null
}
```

## Warranty Endpoints

### Check Warranty Status

Validates warranty status for a specific product SKU.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/warranty/checkWarranty`

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| skuAlias | string | ✓ | Product SKU alias to check warranty for |

#### Example Request

```http
GET /api/warranty/checkWarranty?skuAlias=2711-20
```

#### Response Format

```json
{
  "skuAlias": "2711-20",
  "isUnderWarranty": true,
  "warrantyType": "Limited",
  "warrantyPeriod": "5 years",
  "purchaseDate": "2023-06-15T00:00:00Z",
  "expirationDate": "2028-06-15T00:00:00Z",
  "warrantyDescription": "5 year limited warranty",
  "success": true
}
```

## Product Endpoints

### Get Product Problems

Retrieves available problem categories for a specific product.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/products/{sku}/problems`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| sku | string | ✓ | Product SKU |

#### Example Request

```http
GET /api/products/271120/problems
```

#### Response Format

```json
{
  "problems": [
    {
      "id": 1,
      "code": "POWER",
      "description": "Power Issues",
      "category": "Electrical"
    },
    {
      "id": 2,
      "code": "MECHANICAL",
      "description": "Mechanical Problems",
      "category": "Hardware"
    }
  ],
  "success": true
}
```

### Get Product Inclusions

Retrieves included items/accessories for a specific product.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/products/{sku}/include`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| sku | string | ✓ | Product SKU |

#### Example Request

```http
GET /api/products/271120/include
```

#### Response Format

```json
{
  "inclusions": [
    {
      "id": 1,
      "sku": "271120-ACC1",
      "description": "Battery Charger",
      "quantity": 1,
      "isOptional": false
    },
    {
      "id": 2,
      "sku": "271120-ACC2", 
      "description": "Carrying Case",
      "quantity": 1,
      "isOptional": true
    }
  ],
  "success": true
}
```

## Service Order Endpoints

### Calculate Service Order Estimate Pricing

Calculates pricing for a service order estimate.

**Endpoint Details:**
- **Method:** POST
- **Endpoint:** `/api/serviceOrderEstimate/{estimateId}/pricing`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| estimateId | string (GUID) | ✓ | Service order estimate ID |

#### Request Body

```json
{
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "serviceType": "repair"
    }
  ],
  "customerType": 2,
  "currency": 1,
  "taxExempt": false
}
```

#### Example Request

```http
POST /api/serviceOrderEstimate/b4a54aed-9e9b-429a-a087-ca267ed3e959/pricing
```

### Create Real-time Service Order Group

Creates a new real-time service order group.

**Endpoint Details:**
- **Method:** POST
- **Endpoint:** `/api/serviceOrders/groups/realtime`

#### Request Body

```json
{
  "customerId": "12345",
  "branchId": "BR001",
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "serviceType": "repair",
      "problemCodes": ["POWER"],
      "symptoms": ["No power"]
    }
  ],
  "priority": "normal"
}
```

### Calculate Service Order Charges

Calculates charges for service order items.

**Endpoint Details:**
- **Method:** POST
- **Endpoint:** `/api/serviceOrders/charges`

#### Request Body

```json
{
  "orderId": "SO123456",
  "items": [
    {
      "sku": "271120",
      "quantity": 1,
      "laborHours": 2.5,
      "partsCost": 25.99
    }
  ],
  "customerType": 2,
  "discountPercent": 0
}
```

### Get Service Order Taxes

Retrieves tax calculations for service orders.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/serviceOrders/taxes`

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| orderId | string | ✓ | Service order ID |
| zipCode | string | ✗ | Customer zip code for tax calculation |
| stateCode | string | ✗ | Customer state code |

#### Example Request

```http
GET /api/serviceOrders/taxes?orderId=SO123456&zipCode=53202&stateCode=WI
```

## Repair Details Endpoints

### Get Symptom Codes by Area

Retrieves symptom codes for a specific symptom area.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/repairDetails/symptomAreas/{areaId}/symptomCodes`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| areaId | integer | ✓ | Symptom area ID |

#### Example Request

```http
GET /api/repairDetails/symptomAreas/4/symptomCodes
```

#### Response Format

```json
{
  "symptomCodes": [
    {
      "id": 1,
      "code": "SYM001",
      "description": "Motor not starting",
      "areaId": 4,
      "severity": "high"
    },
    {
      "id": 2,
      "code": "SYM002",
      "description": "Intermittent operation",
      "areaId": 4,
      "severity": "medium"
    }
  ],
  "success": true
}
```

## Payment Endpoints

### Get Payment Terminals

Retrieves available payment terminals for processing transactions.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/Payments/terminals`

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| branchId | string | ✗ | Filter by branch ID |
| status | string | ✗ | Filter by terminal status (active, inactive) |

#### Example Request

```http
GET /api/Payments/terminals?branchId=BR001&status=active
```

#### Response Format

```json
{
  "terminals": [
    {
      "id": "TERM001",
      "name": "Terminal 1 - Front Desk",
      "branchId": "BR001",
      "status": "active",
      "type": "credit_card",
      "capabilities": ["chip", "contactless", "magnetic_stripe"],
      "lastHeartbeat": "2024-01-15T10:30:00Z"
    }
  ],
  "success": true
}
```

## Error Handling

### HTTP Status Codes

| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| 200 | OK | Successful GET requests |
| 201 | Created | Successful POST requests that create resources |
| 400 | Bad Request | Invalid parameters, malformed JSON |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found (invalid SKU, order ID, etc.) |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server-side errors |

### Common Error Codes

| Error Code | Description | Resolution |
|------------|-------------|------------|
| INVALID_SKU | Product SKU not found | Verify SKU exists in catalog |
| PRICING_UNAVAILABLE | Pricing not available for parameters | Check customer type and currency |
| WARRANTY_EXPIRED | Product warranty has expired | Inform customer of warranty status |
| INVALID_CUSTOMER_TYPE | Customer type not recognized | Use valid customer type codes |
| INSUFFICIENT_INVENTORY | Not enough inventory for request | Check available quantities |
| PAYMENT_TERMINAL_OFFLINE | Payment terminal unavailable | Use alternative terminal or retry |

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "errorCode": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Data Types

### Price Types

| Value | Description |
|-------|-------------|
| 1 | Service Pricing |
| 2 | Retail Pricing |
| 3 | Wholesale Pricing |

### Customer Types

| Value | Description |
|-------|-------------|
| 1 | Retail Customer |
| 2 | Commercial Customer |
| 3 | Dealer |
| 4 | Internal |

### Currency Codes

| Value | Description |
|-------|-------------|
| 1 | USD |
| 2 | CAD |
| 3 | EUR |

### Service Types

| Value | Description |
|-------|-------------|
| repair | Standard repair service |
| replace | Product replacement |
| maintenance | Preventive maintenance |
| diagnostic | Diagnostic service only |

## SDK Examples

### TypeScript/JavaScript Implementation

```typescript
// Service for OnePoint API integration
class OnePointApiService {
  private baseUrl = 'https://onepoint-test.milwaukeetool.com/api';
  private authToken: string;

  constructor(authToken: string) {
    this.authToken = authToken;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Get service pricing
  async getServicePricing(params: {
    priceType: number;
    customerType: number;
    currency: number;
    itemCodes: string[];
    svcCategory?: string;
  }) {
    const queryParams = new URLSearchParams({
      PriceType: params.priceType.toString(),
      CustomerType: params.customerType.toString(),
      Currency: params.currency.toString(),
      SvcCategory: params.svcCategory || '',
    });

    params.itemCodes.forEach((code, index) => {
      queryParams.append(`ItemCodes[${index}]`, code);
    });

    return this.request(`/Pricing/servicepricing?${queryParams}`);
  }

  // Check warranty
  async checkWarranty(skuAlias: string) {
    return this.request(`/warranty/checkWarranty?skuAlias=${skuAlias}`);
  }

  // Get product problems
  async getProductProblems(sku: string) {
    return this.request(`/products/${sku}/problems`);
  }
}
```

### Usage Example

```typescript
const apiService = new OnePointApiService('your-jwt-token');

// Get pricing for multiple items
const pricing = await apiService.getServicePricing({
  priceType: 1,
  customerType: 2,
  currency: 1,
  itemCodes: ['271120', '271121'],
});

// Check warranty status
const warranty = await apiService.checkWarranty('2711-20');

// Get product problems
const problems = await apiService.getProductProblems('271120');
```

## Testing and Development

> **Environment Information**
>
> **Test Environment**
> - Base URL: https://onepoint-test.milwaukeetool.com/api
> - Test Data: Use test SKUs and customer IDs provided by Milwaukee Tool
> - Rate Limits: Reduced rate limits in test environment
>
> **Production Environment**
> - Base URL: https://onepoint.milwaukeetool.com/api
> - Authentication: Production JWT tokens required
> - Monitoring: Full logging and monitoring enabled

## Support and Contact

> **⚠️ Support Information**
>
> For API support and questions:
> - **Technical Support:** Contact Milwaukee Tool IT Support
> - **Documentation Updates:** Submit requests through internal channels
> - **Bug Reports:** Use internal issue tracking system

---

**Document Information:**
- **Last Updated:** January 2024
- **API Version:** v1.0
- **Document Version:** 1.0

### Get Replacement Reasons

Retrieves available replacement reason codes.

**Endpoint Details:**
- **Method:** GET
- **Endpoint:** `/api/ReplacementReason`

#### Example Request

```http
GET /api/ReplacementReason
```

#### Response Format

```json
{
  "reasons": [
    {
      "id": 1,
      "code": "DEFECTIVE",
      "description": "Defective Product",
      "category": "Quality"
    },
    {
      "id": 2,
      "code": "DAMAGED",
      "description": "Shipping Damage",
      "category": "Logistics"
    }
  ],
  "success": true
}
```
