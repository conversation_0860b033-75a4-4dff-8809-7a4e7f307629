import { Button } from '@/design/molecules';
import { selectBranch } from '@/features/branch';
import {
  SignalRUpdate,
  TerminalModal,
  useGetTerminalsQuery,
  useStartTerminalTransactionMutation,
  useVerifyTerminalTransactionMutation
} from '@/features/payments';
import {
  closePaymentDrawer,
  CreateSalesOrderPaymentRequest,
  CreateSalesOrderResponse,
  onPONumberChange,
  PaymentSelect,
  salesOrderCreationView,
  selectedSalesOrderCreationState,
  setCashPaymentCompleted,
  setCheckoutCompletedUpdate,
  setCurrentView,
  setInviteToPayCreated,
  setSalesOrder,
  setSelectedPaymentMethod,
  setTaxExemptCertBlobPath,
  useCreateSalesOrderInviteToPayMutation,
  useCreateSalesOrderMutation,
  useCreateSalesOrderPaymentMutation
} from '@/features/salesOrder';
import {
  BlobContainer,
  isPurchaseOrderTerms,
  repairPaymentSchema,
  RepairPaymentSchemaType,
  selectedServiceOrderState,
  useSubmitFile
} from '@/features/serviceOrder';
import { applicationContextState } from '@/store/applicationContextSlice';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { getCurrencyType, getPriceAsString, PaymentMethodCode } from '@/util';
import { zodResolver } from '@hookform/resolvers/zod';
import { Backdrop, Box, Drawer, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

export const SalesOrderPaymentDrawer = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const {
    formState: { errors, isValid, isSubmitting },
    control,
    handleSubmit,
    reset,
    watch
  } = useForm<RepairPaymentSchemaType>({
    resolver: zodResolver(repairPaymentSchema),
    mode: 'onChange',
    defaultValues: {
      poNumbers: [
        {
          orderId: '',
          poNumber: undefined,
          accountAuthorizationNumber: undefined
        }
      ]
    }
  });
  const [openTerminalModal, setOpenTerminalModal] = useState(false);
  const [ccTransactionId, setCcTransactionId] = useState<string | undefined>(
    undefined
  );
  const { currentSite } = useAppSelector(applicationContextState);
  const selectedBranch = useAppSelector(selectBranch);
  const {
    isPaymentDrawerOpen,
    selectedProducts,
    checkoutCompletedUpdate,
    taxAmount,
    purchaseOrderNumber
  } = useAppSelector(selectedSalesOrderCreationState);
  const { taxExemptCertificateFile } = useAppSelector(
    selectedServiceOrderState
  );
  const { selectedCustomer, salesOrderCreationProfile } = useAppSelector(
    (state) => state.customer
  );
  const { data } = useGetTerminalsQuery();
  const [startPaymentTransaction] = useStartTerminalTransactionMutation();
  const [createSalesOrderMutation] = useCreateSalesOrderMutation();
  const [createSalesOrderPaymentMutation] =
    useCreateSalesOrderPaymentMutation();
  const [verifyPaymentTransaction] = useVerifyTerminalTransactionMutation();
  const [createSalesOrderInviteToPayMutation] =
    useCreateSalesOrderInviteToPayMutation();
  const { handleSubmit: submitSalesExemptForm } = useSubmitFile();

  const orderSubtotalAmount = selectedProducts.reduce(
    (total, currentProduct) =>
      total + currentProduct.price! * currentProduct.quantity!,
    0
  );
  const orderTaxAmount = taxAmount ?? 0;
  const orderTotalAmount = Number(
    (orderSubtotalAmount + orderTaxAmount).toFixed(2)
  );
  const currencyType = getCurrencyType(selectedBranch?.countryCode);

  const paymentMethods = useMemo(
    () =>
      isPurchaseOrderTerms(currentSite?.paymentTerms)
        ? [
            {
              id: PaymentMethodCode.PoNumber.toString(),
              value: t('common.poNumber')
            }
          ]
        : [
            {
              id: PaymentMethodCode.Cash.toString(),
              value: t('common.cash')
            },
            {
              id: PaymentMethodCode.CreditCard.toString(),
              value: t('common.creditCard')
            }
          ],
    [t, currentSite?.paymentTerms]
  );

  const handleClose = useCallback(() => {
    reset({
      paymentMethod: undefined,
      poNumbers: [
        {
          orderId: '',
          poNumber: undefined,
          accountAuthorizationNumber: undefined
        }
      ],
      recipientEmailAddress: undefined
    });
    dispatch(closePaymentDrawer());
    setExpandDrawer(false);
  }, [dispatch, reset]);

  useEffect(() => {
    if (checkoutCompletedUpdate) {
      dispatch(setCurrentView(salesOrderCreationView.PaymentCompleted));
      handleClose();
    }
  }, [checkoutCompletedUpdate, dispatch, handleClose]);

  const createSalesOrder = async (): Promise<{
    response: CreateSalesOrderResponse;
    accountNumber: string;
  }> => {
    const accountNumber = salesOrderCreationProfile?.accountNumber ?? '';

    const createSalesOrderResponse = await createSalesOrderMutation({
      locationId: selectedBranch?.id ?? 0,
      accountNumber: accountNumber,
      customerId: salesOrderCreationProfile?.isGuest
        ? null
        : selectedCustomer!.userId,
      items: selectedProducts.map((selectedProduct) => ({
        sku: selectedProduct.product.sku,
        quantity: selectedProduct.quantity,
        price: selectedProduct.price ?? 0,
        promoCode: selectedProduct.pceCode
      })),
      taxExemptCertificateBlobPath: await (async () => {
        if (taxExemptCertificateFile) {
          return await submitSalesExemptForm(
            BlobContainer.TaxExemptCertificate,
            taxExemptCertificateFile
          );
        }
      })()
    }).unwrap();

    await dispatch(setSalesOrder({ id: createSalesOrderResponse.id }));
    await dispatch(
      setTaxExemptCertBlobPath(
        createSalesOrderResponse.taxExemptCertificateBlobPath
      )
    );

    return {
      accountNumber,
      response: createSalesOrderResponse
    };
  };

  const handlePayNowClick = async (data: RepairPaymentSchemaType) => {
    const cashRequestPart = (): Partial<CreateSalesOrderPaymentRequest> => {
      const parsedCashReceived = parseFloat(
        data.cashReceived?.replace('$', '') ?? ''
      );

      return {
        cashCollected: parsedCashReceived,
        cashReturned: parsedCashReceived - orderTotalAmount
      };
    };

    const purchaseOrderRequestPart =
      (): Partial<CreateSalesOrderPaymentRequest> => ({
        accountAuthorizationNumber:
          data.poNumbers?.[0].accountAuthorizationNumber ?? '',
        purchaseOrderNumber: data.poNumbers?.[0].poNumber ?? ''
      });

    try {
      const paymentMethodId = parseInt(data.paymentMethod);
      dispatch(setSelectedPaymentMethod(paymentMethodId));

      const parsedCashReceived = parseFloat(
        data.cashReceived?.replace('$', '') ?? ''
      );

      const { response } = await createSalesOrder();

      if (data.paymentMethod === PaymentMethodCode.PayInvite.toString()) {
        await createSalesOrderInviteToPayMutation({
          email: data.recipientEmailAddress!,
          salesOrderNumber: response.orderNumber,
          totalAmount: orderTotalAmount
        });

        dispatch(
          setInviteToPayCreated({
            email: data.recipientEmailAddress!,
            salesOrderNumber: response.orderNumber,
            processed: false
          })
        );
      } else {
        await createSalesOrderPaymentMutation({
          paymentMethodId,
          salesOrderNumber: response.orderNumber,
          totalPrice: orderTotalAmount,
          cashCollected: 0,
          cashReturned: 0,
          ...(data.paymentMethod === PaymentMethodCode.Cash.toString()
            ? cashRequestPart()
            : {}),
          ...(data.paymentMethod === PaymentMethodCode.PoNumber.toString()
            ? purchaseOrderRequestPart()
            : {})
        });

        dispatch(
          setCashPaymentCompleted({
            id: response.id,
            cashReceived: parsedCashReceived,
            salesOrderNumber: response.orderNumber
          })
        );
        dispatch(onPONumberChange(data.poNumbers?.[0].poNumber));
      }

      dispatch(setCurrentView(salesOrderCreationView.PaymentCompleted));
    } catch (error) {
      console.error(`Error while submitting payment: ${error}`);
    }
  };

  const submitPayWithSquare = async (terminalId: string) => {
    try {
      dispatch(setSelectedPaymentMethod(PaymentMethodCode.CreditCard));

      const { response, accountNumber } = await createSalesOrder();

      const startResponse = await startPaymentTransaction({
        squareTerminalId: terminalId,
        locationId: selectedBranch?.oracleId,
        amount: orderTotalAmount,
        accountNumber: accountNumber,
        userId: selectedCustomer?.userId ?? '',
        currencyCode: response.currencyCode,
        salesOrderNumber: response.orderNumber
      }).unwrap();

      setCcTransactionId(startResponse.transactionId);
    } catch (error) {
      console.error(`Error while submitting payment: ${error}`);
    }
  };

  const handleVerifyCheckout = async (ccTransactionId: string) => {
    const verifyResponse = await verifyPaymentTransaction({
      transactionId: ccTransactionId
    }).unwrap();

    return verifyResponse;
  };

  const handleCheckoutCompleted = (update: SignalRUpdate) => {
    dispatch(setCheckoutCompletedUpdate(update));
  };

  const handleTerminalModalChange = (value: boolean) => {
    setOpenTerminalModal(value);
  };

  const handleTerminalSelect = (terminalId: string) => {
    const currentData = watch();
    reset({
      ...currentData,
      creditCard: terminalId
    });
  };

  const filterTerminalLocations = () => {
    if (data) {
      const currentBranch = data.locations.find(
        (location) =>
          location.locationId.toString() === selectedBranch?.oracleId
      );

      if (currentBranch) {
        const terminalItems = currentBranch.terminals.map((terminal) => ({
          id: terminal.id,
          terminalId: terminal.terminalId,
          deviceCodeId: terminal.deviceCodeId,
          deviceId: terminal.deviceId,
          accessCode: terminal.accessCode,
          terminalName: terminal.terminalName
        }));

        return terminalItems;
      }
    }

    return [];
  };

  const [expandDrawer, setExpandDrawer] = useState(false);

  const paymentMethod = watch('paymentMethod');
  const isCashPaymentMethod =
    paymentMethod === PaymentMethodCode.Cash.toString();

  const cashReceived = watch('cashReceived');
  const parsedCashReceived = parseFloat(cashReceived?.replace('$', '') ?? '');
  const changeDue =
    isNaN(parsedCashReceived) || !isCashPaymentMethod
      ? -1
      : parsedCashReceived - (orderTotalAmount ?? 0);

  const disablePayNowButton =
    !isValid || isSubmitting || (isCashPaymentMethod && changeDue < 0);

  return (
    <>
      <Backdrop
        open={isPaymentDrawerOpen}
        sx={{ zIndex: (theme) => theme.zIndex.drawer - 1 }}
      />
      <Drawer
        anchor="bottom"
        open={isPaymentDrawerOpen}
        onClose={handleClose}
        data-testid="mainDrawer-testId"
      >
        <Box
          sx={{
            padding: '10px 30px',
            backgroundColor: 'common.white'
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              my: 1
            }}
          >
            <Typography
              variant="caption"
              color="primary.main"
              data-testid="subTotalLabel-testId"
            >
              {t('features.order.subTotal')}
              {t('common.colonChar')}
            </Typography>
            <Typography
              variant="subtitle4"
              fontWeight={600}
              data-testid="subTotalValue-testId"
              sx={{
                paddingBottom: '10px'
              }}
            >
              {getPriceAsString(orderSubtotalAmount, currencyType)}
            </Typography>
            <Typography
              variant="caption"
              color="primary.main"
              data-testid="salesTaxLabel-testId"
            >
              {t('features.order.salesTax')}
              {t('common.colonChar')}
            </Typography>
            <Typography
              variant="subtitle4"
              fontWeight={600}
              data-testid="salesTaxValue-testId"
              sx={{
                paddingBottom: '10px'
              }}
            >
              {getPriceAsString(orderTaxAmount, currencyType)}
            </Typography>
            <Typography
              variant="caption"
              color="primary.main"
              data-testid="amountDueLabel-testId"
            >
              {t('features.order.totalAmountDue')}
              {t('common.colonChar')}
            </Typography>
            <Typography
              variant="subtitle4"
              fontWeight={600}
              data-testid="amountDueValue-testId"
              sx={{
                paddingBottom: '10px'
              }}
            >
              {getPriceAsString(orderTotalAmount, currencyType)}
            </Typography>
            {isCashPaymentMethod && changeDue >= 0 && (
              <Typography
                variant="caption"
                color="primary.main"
                data-testid="amountDueLabel-testId"
              >
                {t('features.order.changeAmountDue')}
                {t('common.colonChar')}
              </Typography>
            )}
            {isCashPaymentMethod && changeDue >= 0 && (
              <Typography
                variant="subtitle4"
                fontWeight={600}
                data-testid="changeDue-testId"
                sx={{
                  paddingBottom: '10px'
                }}
              >
                {getPriceAsString(changeDue!, currencyType)}
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              p: '32px',
              height: expandDrawer ? '350px' : 'inherit'
            }}
          >
            <Box sx={{ height: expandDrawer ? '350px' : 'inherit' }}>
              <PaymentSelect
                data-testid="paymentSelect-testId"
                control={control}
                reset={reset}
                watch={watch}
                errors={errors}
                emailAddress={selectedCustomer?.emailAddress}
                paymentMethods={paymentMethods}
                poNumber={purchaseOrderNumber}
                isNationalAccount={currentSite?.isNationalAccount}
              />
              {paymentMethod === PaymentMethodCode.CreditCard.toString() ? (
                <Button
                  variant="primary"
                  size="large"
                  disabled={isSubmitting}
                  onClick={() => {
                    setOpenTerminalModal(true);
                  }}
                  data-testid="payWithSquare-testId"
                  sx={{
                    width: '100%'
                  }}
                >
                  {t('features.order.payments.payWithSquare')}
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="large"
                  disabled={disablePayNowButton}
                  onClick={handleSubmit(handlePayNowClick)}
                  data-testid="payNow-testId"
                  sx={{
                    width: '100%'
                  }}
                >
                  {t('common.payNow')}
                </Button>
              )}
            </Box>
          </Box>
          <TerminalModal
            open={openTerminalModal}
            onChange={handleTerminalModalChange}
            name={'Select terminal'}
            terminals={filterTerminalLocations()}
            onSelectTerminal={handleTerminalSelect}
            data-testid="terminalModal"
            submitPayWithSquare={submitPayWithSquare}
            onCheckoutCompleted={handleCheckoutCompleted}
            isSalesOrderPayment={true}
            ccTransactionId={ccTransactionId}
            onVerifyTransaction={handleVerifyCheckout}
          />
        </Box>
      </Drawer>
    </>
  );
};
