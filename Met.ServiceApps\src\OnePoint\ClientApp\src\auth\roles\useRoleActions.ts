import { Role, useRoleContext } from '@/auth';
import { useMemo } from 'react';

export interface RoleActions {
  hasBranchSelection: boolean;
  hasHubSelection: boolean;
  canStartRepair: boolean;
  canStartRealtimeRepair: boolean;
  canStartSale: boolean;
  canCreateCustomer: boolean;
  canViewServiceHistory: boolean;
  canViewEngagements: boolean;
  canSearchCustomer: boolean;
  canCreateServiceRequest: boolean;
  canSearchServiceRequest: boolean;
  canViewAccountLookup: boolean;
  canStartToteOrder: boolean;
}

export const useRoleActions = (): RoleActions => {
  const { currentRole } = useRoleContext();

  const roleActions = useMemo(() => {
    const actions: Partial<RoleActions> = {};
    const allRoles: Role[] = Object.values(Role);
    const roleActionMap: Record<keyof RoleActions, Role[]> = {
      hasBranchSelection: [Role.BranchAssociate, Role.BranchManager],
      hasHubSelection: [Role.Hub],
      canStartRepair: [Role.BranchAssociate, Role.BranchManager, Role.Hub],
      canStartRealtimeRepair: [Role.BranchAssociate, Role.BranchManager],
      canStartSale: [Role.BranchAssociate, Role.BranchManager],
      canCreateCustomer: [Role.BranchAssociate, Role.BranchManager, Role.Hub],
      canViewServiceHistory: [
        Role.BranchAssociate,
        Role.BranchManager,
        Role.Hub
      ],
      canViewEngagements: [Role.BranchAssociate, Role.BranchManager],
      canSearchCustomer: allRoles.filter((role) => role !== Role.Hub),
      canCreateServiceRequest: [Role.Hub],
      canSearchServiceRequest: [Role.Hub],
      canViewAccountLookup: [Role.Hub],
      canStartToteOrder: [Role.BranchAssociate, Role.BranchManager, Role.Hub]
    };

    (Object.keys(roleActionMap) as (keyof RoleActions)[]).forEach((action) => {
      actions[action] = isRoleAllowed(currentRole, roleActionMap[action]);
    });

    return actions as RoleActions;
  }, [currentRole]);

  return roleActions;
};

const isRoleAllowed = (role: Role, allowedRoles: Role[]): boolean => {
  return allowedRoles.includes(role);
};
