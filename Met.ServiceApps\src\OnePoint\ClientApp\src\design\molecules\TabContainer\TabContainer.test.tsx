import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '@emotion/react';
import { createTheme } from '@mui/material/styles';
import { TabContainer } from './TabContainer';
import { TabItem } from './models';

const renderWithTheme = (component: React.ReactNode) => {
  return render(
    <ThemeProvider theme={createTheme()}>{component}</ThemeProvider>
  );
};

const mockTabs: TabItem[] = [
  { id: 0, label: 'Tab 1', content: 'Content 1', dataTestId: 'tab1-testId' },
  { id: 1, label: 'Tab 2', content: 'Content 2', dataTestId: 'tab2-testId' }
];

describe('TabContainer Component', () => {
  it('renders tabs section correctly', () => {
    const { getByTestId } = renderWithTheme(<TabContainer tabs={mockTabs} />);

    // Check if the tabs section is rendered
    const tabSection = getByTestId('tabSection-testId');
    expect(tabSection).toBeInTheDocument();
  });
});
