import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import React from 'react';

interface Props {
  description: string;
  icon: React.ReactElement;
  onButtonClick: () => void;
  testId: string;
}

export const MainButton = (props: Props) => {
  const { description, icon, onButtonClick, testId } = props;
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
        backgroundColor: 'common.white',
        flexDirection: { xs: 'row', sm: 'column' },
        ':hover': {
          border: '2px solid',
          borderColor: 'primary.main',
          boxShadow: '0px 12px 24px 0px rgba(0, 0, 0, 0.10)',
          padding: '23px'
        },
        padding: '24px',
        gap: '16px',
        flex: '1 0 0',
        borderRadius: '8px',
        border: '1px solid',
        borderColor: 'neutral.400',
        boxShadow: '0px 6px 12px 0px rgba(0, 0, 0, 0.10)',
        boxSizing: 'border-box',
        height: { xs: '72px', sm: '183px' }
      }}
      onClick={onButtonClick}
      data-testid={testId}
    >
      {icon}
      <Typography
        sx={{
          color: 'primary.main',
          ml: 0.5
        }}
        variant="h6"
        fontWeight={600}
      >
        {description}
      </Typography>
    </Box>
  );
};
