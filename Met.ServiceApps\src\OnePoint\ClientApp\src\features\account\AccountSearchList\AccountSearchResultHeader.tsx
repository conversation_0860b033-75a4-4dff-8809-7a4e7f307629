import { AccountSearchItemWrapper, SiteItem } from '@/features/account';
import { Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';

interface Props {
  sites: SiteItem[];
}

export const AccountSearchResultHeader = (props: Props) => {
  const { t } = useTranslation();
  const totalSites = props.sites.length ?? 0;
  return (
    <AccountSearchItemWrapper
      sx={{
        border: '1px solid',
        borderBottom: 'unset',
        borderColor: 'neutral.400',
        px: '16px',
        py: '16px',
        borderRadius: 'unset',
        boxShadow: 'none'
      }}
    >
      <Typography
        data-testid="loadingTextNumber-testId"
        variant="p3"
        color="neutral.500"
        fontWeight={700}
      >
        {totalSites}
        &nbsp;
      </Typography>
      <Typography
        data-testid="loadingText-testId"
        variant="p3"
        color="neutral.500"
      >
        {t('features.search.recordsFound')}
      </Typography>
    </AccountSearchItemWrapper>
  );
};
