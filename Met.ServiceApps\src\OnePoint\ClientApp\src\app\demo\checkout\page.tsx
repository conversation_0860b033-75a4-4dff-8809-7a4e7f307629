'use client';

/* eslint-disable react/jsx-no-literals */
import { <PERSON>ton, NavbarContainer } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { useSignalR } from '@/signalr';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import { useEffect, useState } from 'react';

const signalrMethod = 'RefreshTerminalCheckout';

export default function SignalRPage() {
  const [content, setContent] = useState<string | null>(null);
  const theme = useTheme();

  const {
    isConnected,
    addListener,
    connect,
    disconnect,
    error: signalrError
  } = useSignalR();

  useEffect(() => {
    const startListening = async () => {
      if (isConnected) {
        addListener(signalrMethod, (update: unknown) => {
          setContent(JSON.stringify(update, null, 2));
        });
      }
    };

    startListening();

    () => {
      disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConnected]);

  return (
    <>
      <NavbarContainer />
      <PageAreaBox pageAreaBoxName="SignalRPage">
        <div style={{ padding: '10px' }}>
          <Typography
            component="span"
            variant="h3"
            sx={{ fontWeight: 'bold', color: theme.palette.neutral[800] }}
          >
            SignalR
          </Typography>
        </div>
        <br />
        <section
          style={{
            padding: '10px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            gap: '10px'
          }}
        >
          <Typography
            component="span"
            variant="p3"
            sx={{ color: theme.palette.neutral[800] }}
          >
            <span
              style={{
                fontSize: '1.5rem',
                color: isConnected ? '#00FF0B' : 'yellow'
              }}
            >
              •
            </span>
            {isConnected ? 'Connected to the SignalR Hub' : 'Connecting...'}
          </Typography>

          <Button
            data-testid="junk"
            size="medium"
            variant="secondary"
            onClick={() => connect()}
          >
            Connect
          </Button>

          <Button
            data-testid="junk"
            size="medium"
            variant="secondary"
            onClick={() => disconnect()}
          >
            Disconnect
          </Button>

          {signalrError && (
            <Typography
              component="span"
              variant="p1"
              sx={{ color: 'error.main' }}
            >
              Error while connecting to the SignalR hub.
            </Typography>
          )}
          {content && (
            <>
              <hr />
              <Typography
                component="span"
                variant="p3"
                sx={{ color: theme.palette.neutral[800] }}
              >
                Message received:
              </Typography>
              <pre>{content}</pre>
            </>
          )}
        </section>
      </PageAreaBox>
    </>
  );
}
