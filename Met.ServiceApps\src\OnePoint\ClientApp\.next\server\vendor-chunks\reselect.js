"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/reselect";
exports.ids = ["vendor-chunks/reselect"];
exports.modules = {

/***/ "(ssr)/./node_modules/reselect/dist/reselect.mjs":
/*!*************************************************!*\
  !*** ./node_modules/reselect/dist/reselect.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSelector: () => (/* binding */ createSelector),\n/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),\n/* harmony export */   createStructuredSelector: () => (/* binding */ createStructuredSelector),\n/* harmony export */   lruMemoize: () => (/* binding */ lruMemoize),\n/* harmony export */   referenceEqualityCheck: () => (/* binding */ referenceEqualityCheck),\n/* harmony export */   setGlobalDevModeChecks: () => (/* binding */ setGlobalDevModeChecks),\n/* harmony export */   unstable_autotrackMemoize: () => (/* binding */ autotrackMemoize),\n/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)\n/* harmony export */ });\n// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc)=>{\n    let isInputSameAsOutput = false;\n    try {\n        const emptyObject = {};\n        if (resultFunc(emptyObject) === emptyObject) isInputSameAsOutput = true;\n    } catch  {}\n    if (isInputSameAsOutput) {\n        let stack = void 0;\n        try {\n            throw new Error();\n        } catch (e) {\n            ;\n            ({ stack } = e);\n        }\n        console.warn(\"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\", {\n            stack\n        });\n    }\n};\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs)=>{\n    const { memoize, memoizeOptions } = options;\n    const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n    const createAnEmptyObject = memoize(()=>({}), ...memoizeOptions);\n    const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n    if (!areInputSelectorResultsEqual) {\n        let stack = void 0;\n        try {\n            throw new Error();\n        } catch (e) {\n            ;\n            ({ stack } = e);\n        }\n        console.warn(\"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\", {\n            arguments: inputSelectorArgs,\n            firstInputs: inputSelectorResults,\n            secondInputs: inputSelectorResultsCopy,\n            stack\n        });\n    }\n};\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n    inputStabilityCheck: \"once\",\n    identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks)=>{\n    Object.assign(globalDevModeChecks, devModeChecks);\n};\n// src/utils.ts\nvar NOT_FOUND = \"NOT_FOUND\";\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n    if (typeof func !== \"function\") {\n        throw new TypeError(errorMessage);\n    }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n    if (typeof object !== \"object\") {\n        throw new TypeError(errorMessage);\n    }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n    if (!array.every((item)=>typeof item === \"function\")) {\n        const itemTypes = array.map((item)=>typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item).join(\", \");\n        throw new TypeError(`${errorMessage}[${itemTypes}]`);\n    }\n}\nvar ensureIsArray = (item)=>{\n    return Array.isArray(item) ? item : [\n        item\n    ];\n};\nfunction getDependencies(createSelectorArgs) {\n    const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n    assertIsArrayOfFunctions(dependencies, `createSelector expects all input-selectors to be functions, but received the following types: `);\n    return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n    const inputSelectorResults = [];\n    const { length } = dependencies;\n    for(let i = 0; i < length; i++){\n        inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n    }\n    return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks)=>{\n    const { identityFunctionCheck, inputStabilityCheck } = {\n        ...globalDevModeChecks,\n        ...devModeChecks\n    };\n    return {\n        identityFunctionCheck: {\n            shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n            run: runIdentityFunctionCheck\n        },\n        inputStabilityCheck: {\n            shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n            run: runInputStabilityCheck\n        }\n    };\n};\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n    constructor(initialValue, isEqual = tripleEq){\n        this.revision = $REVISION;\n        this._isEqual = tripleEq;\n        this._value = this._lastValue = initialValue;\n        this._isEqual = isEqual;\n    }\n    // Whenever a storage value is read, it'll add itself to the current tracker if\n    // one exists, entangling its state with that cache.\n    get value() {\n        CURRENT_TRACKER?.add(this);\n        return this._value;\n    }\n    // Whenever a storage value is updated, we bump the global revision clock,\n    // assign the revision for this storage to the new value, _and_ we schedule a\n    // rerender. This is important, and it's what makes autotracking  _pull_\n    // based. We don't actively tell the caches which depend on the storage that\n    // anything has happened. Instead, we recompute the caches when needed.\n    set value(newValue) {\n        if (this.value === newValue) return;\n        this._value = newValue;\n        this.revision = ++$REVISION;\n    }\n};\nfunction tripleEq(a, b) {\n    return a === b;\n}\nvar TrackingCache = class {\n    constructor(fn){\n        this._cachedRevision = -1;\n        this._deps = [];\n        this.hits = 0;\n        this.fn = fn;\n    }\n    clear() {\n        this._cachedValue = void 0;\n        this._cachedRevision = -1;\n        this._deps = [];\n        this.hits = 0;\n    }\n    get value() {\n        if (this.revision > this._cachedRevision) {\n            const { fn } = this;\n            const currentTracker = /* @__PURE__ */ new Set();\n            const prevTracker = CURRENT_TRACKER;\n            CURRENT_TRACKER = currentTracker;\n            this._cachedValue = fn();\n            CURRENT_TRACKER = prevTracker;\n            this.hits++;\n            this._deps = Array.from(currentTracker);\n            this._cachedRevision = this.revision;\n        }\n        CURRENT_TRACKER?.add(this);\n        return this._cachedValue;\n    }\n    get revision() {\n        return Math.max(...this._deps.map((d)=>d.revision), 0);\n    }\n};\nfunction getValue(cell) {\n    if (!(cell instanceof Cell)) {\n        console.warn(\"Not a valid cell! \", cell);\n    }\n    return cell.value;\n}\nfunction setValue(storage, value) {\n    if (!(storage instanceof Cell)) {\n        throw new TypeError(\"setValue must be passed a tracked store created with `createStorage`.\");\n    }\n    storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n    return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n    assertIsFunction(fn, \"the first parameter to `createCache` must be a function\");\n    return new TrackingCache(fn);\n}\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b)=>false;\nfunction createTag() {\n    return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n    setValue(tag, value);\n}\nvar consumeCollection = (node)=>{\n    let tag = node.collectionTag;\n    if (tag === null) {\n        tag = node.collectionTag = createTag();\n    }\n    getValue(tag);\n};\nvar dirtyCollection = (node)=>{\n    const tag = node.collectionTag;\n    if (tag !== null) {\n        dirtyTag(tag, null);\n    }\n};\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n    constructor(value){\n        this.proxy = new Proxy(this, objectProxyHandler);\n        this.tag = createTag();\n        this.tags = {};\n        this.children = {};\n        this.collectionTag = null;\n        this.id = nextId++;\n        this.value = value;\n        this.value = value;\n        this.tag.value = value;\n    }\n};\nvar objectProxyHandler = {\n    get (node, key) {\n        function calculateResult() {\n            const { value } = node;\n            const childValue = Reflect.get(value, key);\n            if (typeof key === \"symbol\") {\n                return childValue;\n            }\n            if (key in proto) {\n                return childValue;\n            }\n            if (typeof childValue === \"object\" && childValue !== null) {\n                let childNode = node.children[key];\n                if (childNode === void 0) {\n                    childNode = node.children[key] = createNode(childValue);\n                }\n                if (childNode.tag) {\n                    getValue(childNode.tag);\n                }\n                return childNode.proxy;\n            } else {\n                let tag = node.tags[key];\n                if (tag === void 0) {\n                    tag = node.tags[key] = createTag();\n                    tag.value = childValue;\n                }\n                getValue(tag);\n                return childValue;\n            }\n        }\n        const res = calculateResult();\n        return res;\n    },\n    ownKeys (node) {\n        consumeCollection(node);\n        return Reflect.ownKeys(node.value);\n    },\n    getOwnPropertyDescriptor (node, prop) {\n        return Reflect.getOwnPropertyDescriptor(node.value, prop);\n    },\n    has (node, prop) {\n        return Reflect.has(node.value, prop);\n    }\n};\nvar ArrayTreeNode = class {\n    constructor(value){\n        this.proxy = new Proxy([\n            this\n        ], arrayProxyHandler);\n        this.tag = createTag();\n        this.tags = {};\n        this.children = {};\n        this.collectionTag = null;\n        this.id = nextId++;\n        this.value = value;\n        this.value = value;\n        this.tag.value = value;\n    }\n};\nvar arrayProxyHandler = {\n    get ([node], key) {\n        if (key === \"length\") {\n            consumeCollection(node);\n        }\n        return objectProxyHandler.get(node, key);\n    },\n    ownKeys ([node]) {\n        return objectProxyHandler.ownKeys(node);\n    },\n    getOwnPropertyDescriptor ([node], prop) {\n        return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n    },\n    has ([node], prop) {\n        return objectProxyHandler.has(node, prop);\n    }\n};\nfunction createNode(value) {\n    if (Array.isArray(value)) {\n        return new ArrayTreeNode(value);\n    }\n    return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n    const { value, tags, children } = node;\n    node.value = newValue;\n    if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n        dirtyCollection(node);\n    } else {\n        if (value !== newValue) {\n            let oldKeysSize = 0;\n            let newKeysSize = 0;\n            let anyKeysAdded = false;\n            for(const _key in value){\n                oldKeysSize++;\n            }\n            for(const key in newValue){\n                newKeysSize++;\n                if (!(key in value)) {\n                    anyKeysAdded = true;\n                    break;\n                }\n            }\n            const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n            if (isDifferent) {\n                dirtyCollection(node);\n            }\n        }\n    }\n    for(const key in tags){\n        const childValue = value[key];\n        const newChildValue = newValue[key];\n        if (childValue !== newChildValue) {\n            dirtyCollection(node);\n            dirtyTag(tags[key], newChildValue);\n        }\n        if (typeof newChildValue === \"object\" && newChildValue !== null) {\n            delete tags[key];\n        }\n    }\n    for(const key in children){\n        const childNode = children[key];\n        const newChildValue = newValue[key];\n        const childValue = childNode.value;\n        if (childValue === newChildValue) {\n            continue;\n        } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n            updateNode(childNode, newChildValue);\n        } else {\n            deleteNode(childNode);\n            delete children[key];\n        }\n    }\n}\nfunction deleteNode(node) {\n    if (node.tag) {\n        dirtyTag(node.tag, null);\n    }\n    dirtyCollection(node);\n    for(const key in node.tags){\n        dirtyTag(node.tags[key], null);\n    }\n    for(const key in node.children){\n        deleteNode(node.children[key]);\n    }\n}\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n    let entry;\n    return {\n        get (key) {\n            if (entry && equals(entry.key, key)) {\n                return entry.value;\n            }\n            return NOT_FOUND;\n        },\n        put (key, value) {\n            entry = {\n                key,\n                value\n            };\n        },\n        getEntries () {\n            return entry ? [\n                entry\n            ] : [];\n        },\n        clear () {\n            entry = void 0;\n        }\n    };\n}\nfunction createLruCache(maxSize, equals) {\n    let entries = [];\n    function get(key) {\n        const cacheIndex = entries.findIndex((entry)=>equals(key, entry.key));\n        if (cacheIndex > -1) {\n            const entry = entries[cacheIndex];\n            if (cacheIndex > 0) {\n                entries.splice(cacheIndex, 1);\n                entries.unshift(entry);\n            }\n            return entry.value;\n        }\n        return NOT_FOUND;\n    }\n    function put(key, value) {\n        if (get(key) === NOT_FOUND) {\n            entries.unshift({\n                key,\n                value\n            });\n            if (entries.length > maxSize) {\n                entries.pop();\n            }\n        }\n    }\n    function getEntries() {\n        return entries;\n    }\n    function clear() {\n        entries = [];\n    }\n    return {\n        get,\n        put,\n        getEntries,\n        clear\n    };\n}\nvar referenceEqualityCheck = (a, b)=>a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n    return function areArgumentsShallowlyEqual(prev, next) {\n        if (prev === null || next === null || prev.length !== next.length) {\n            return false;\n        }\n        const { length } = prev;\n        for(let i = 0; i < length; i++){\n            if (!equalityCheck(prev[i], next[i])) {\n                return false;\n            }\n        }\n        return true;\n    };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n    const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : {\n        equalityCheck: equalityCheckOrOptions\n    };\n    const { equalityCheck = referenceEqualityCheck, maxSize = 1, resultEqualityCheck } = providedOptions;\n    const comparator = createCacheKeyComparator(equalityCheck);\n    let resultsCount = 0;\n    const cache = maxSize === 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n    function memoized() {\n        let value = cache.get(arguments);\n        if (value === NOT_FOUND) {\n            value = func.apply(null, arguments);\n            resultsCount++;\n            if (resultEqualityCheck) {\n                const entries = cache.getEntries();\n                const matchingEntry = entries.find((entry)=>resultEqualityCheck(entry.value, value));\n                if (matchingEntry) {\n                    value = matchingEntry.value;\n                    resultsCount !== 0 && resultsCount--;\n                }\n            }\n            cache.put(arguments, value);\n        }\n        return value;\n    }\n    memoized.clearCache = ()=>{\n        cache.clear();\n        memoized.resetResultsCount();\n    };\n    memoized.resultsCount = ()=>resultsCount;\n    memoized.resetResultsCount = ()=>{\n        resultsCount = 0;\n    };\n    return memoized;\n}\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n    const node = createNode([]);\n    let lastArgs = null;\n    const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n    const cache = createCache(()=>{\n        const res = func.apply(null, node.proxy);\n        return res;\n    });\n    function memoized() {\n        if (!shallowEqual(lastArgs, arguments)) {\n            updateNode(node, arguments);\n            lastArgs = arguments;\n        }\n        return cache.value;\n    }\n    memoized.clearCache = ()=>{\n        return cache.clear();\n    };\n    return memoized;\n}\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n    constructor(value){\n        this.value = value;\n    }\n    deref() {\n        return this.value;\n    }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n    return {\n        s: UNTERMINATED,\n        v: void 0,\n        o: null,\n        p: null\n    };\n}\nfunction weakMapMemoize(func, options = {}) {\n    let fnNode = createCacheNode();\n    const { resultEqualityCheck } = options;\n    let lastResult;\n    let resultsCount = 0;\n    function memoized() {\n        let cacheNode = fnNode;\n        const { length } = arguments;\n        for(let i = 0, l = length; i < l; i++){\n            const arg = arguments[i];\n            if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n                let objectCache = cacheNode.o;\n                if (objectCache === null) {\n                    cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n                }\n                const objectNode = objectCache.get(arg);\n                if (objectNode === void 0) {\n                    cacheNode = createCacheNode();\n                    objectCache.set(arg, cacheNode);\n                } else {\n                    cacheNode = objectNode;\n                }\n            } else {\n                let primitiveCache = cacheNode.p;\n                if (primitiveCache === null) {\n                    cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n                }\n                const primitiveNode = primitiveCache.get(arg);\n                if (primitiveNode === void 0) {\n                    cacheNode = createCacheNode();\n                    primitiveCache.set(arg, cacheNode);\n                } else {\n                    cacheNode = primitiveNode;\n                }\n            }\n        }\n        const terminatedNode = cacheNode;\n        let result;\n        if (cacheNode.s === TERMINATED) {\n            result = cacheNode.v;\n        } else {\n            result = func.apply(null, arguments);\n            resultsCount++;\n        }\n        terminatedNode.s = TERMINATED;\n        if (resultEqualityCheck) {\n            const lastResultValue = lastResult?.deref() ?? lastResult;\n            if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n                result = lastResultValue;\n                resultsCount !== 0 && resultsCount--;\n            }\n            const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n            lastResult = needsWeakRef ? new Ref(result) : result;\n        }\n        terminatedNode.v = result;\n        return result;\n    }\n    memoized.clearCache = ()=>{\n        fnNode = createCacheNode();\n        memoized.resetResultsCount();\n    };\n    memoized.resultsCount = ()=>resultsCount;\n    memoized.resetResultsCount = ()=>{\n        resultsCount = 0;\n    };\n    return memoized;\n}\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n    const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n        memoize: memoizeOrOptions,\n        memoizeOptions: memoizeOptionsFromArgs\n    } : memoizeOrOptions;\n    const createSelector2 = (...createSelectorArgs)=>{\n        let recomputations = 0;\n        let dependencyRecomputations = 0;\n        let lastResult;\n        let directlyPassedOptions = {};\n        let resultFunc = createSelectorArgs.pop();\n        if (typeof resultFunc === \"object\") {\n            directlyPassedOptions = resultFunc;\n            resultFunc = createSelectorArgs.pop();\n        }\n        assertIsFunction(resultFunc, `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`);\n        const combinedOptions = {\n            ...createSelectorCreatorOptions,\n            ...directlyPassedOptions\n        };\n        const { memoize, memoizeOptions = [], argsMemoize = weakMapMemoize, argsMemoizeOptions = [], devModeChecks = {} } = combinedOptions;\n        const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n        const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n        const dependencies = getDependencies(createSelectorArgs);\n        const memoizedResultFunc = memoize(function recomputationWrapper() {\n            recomputations++;\n            return resultFunc.apply(null, arguments);\n        }, ...finalMemoizeOptions);\n        let firstRun = true;\n        const selector = argsMemoize(function dependenciesChecker() {\n            dependencyRecomputations++;\n            const inputSelectorResults = collectInputSelectorResults(dependencies, arguments);\n            if (true) {\n                const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n                if (identityFunctionCheck.shouldRun) {\n                    identityFunctionCheck.run(resultFunc);\n                }\n                if (inputStabilityCheck.shouldRun) {\n                    const inputSelectorResultsCopy = collectInputSelectorResults(dependencies, arguments);\n                    inputStabilityCheck.run({\n                        inputSelectorResults,\n                        inputSelectorResultsCopy\n                    }, {\n                        memoize,\n                        memoizeOptions: finalMemoizeOptions\n                    }, arguments);\n                }\n                if (firstRun) firstRun = false;\n            }\n            lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n            return lastResult;\n        }, ...finalArgsMemoizeOptions);\n        return Object.assign(selector, {\n            resultFunc,\n            memoizedResultFunc,\n            dependencies,\n            dependencyRecomputations: ()=>dependencyRecomputations,\n            resetDependencyRecomputations: ()=>{\n                dependencyRecomputations = 0;\n            },\n            lastResult: ()=>lastResult,\n            recomputations: ()=>recomputations,\n            resetRecomputations: ()=>{\n                recomputations = 0;\n            },\n            memoize,\n            argsMemoize\n        });\n    };\n    return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n// src/createStructuredSelector.ts\nvar createStructuredSelector = (inputSelectorsObject, selectorCreator = createSelector)=>{\n    assertIsObject(inputSelectorsObject, `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`);\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map((key)=>inputSelectorsObject[key]);\n    const structuredSelector = selectorCreator(dependencies, (...inputSelectorResults)=>{\n        return inputSelectorResults.reduce((composition, value, index)=>{\n            composition[inputSelectorKeys[index]] = value;\n            return composition;\n        }, {});\n    });\n    return structuredSelector;\n};\n //# sourceMappingURL=reselect.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzZWxlY3QvZGlzdC9yZXNlbGVjdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSw2Q0FBNkM7QUFDN0MsSUFBSUEsMkJBQTJCLENBQUNDO0lBQzlCLElBQUlDLHNCQUFzQjtJQUMxQixJQUFJO1FBQ0YsTUFBTUMsY0FBYyxDQUFDO1FBQ3JCLElBQUlGLFdBQVdFLGlCQUFpQkEsYUFDOUJELHNCQUFzQjtJQUMxQixFQUFFLE9BQU0sQ0FDUjtJQUNBLElBQUlBLHFCQUFxQjtRQUN2QixJQUFJRSxRQUFRLEtBQUs7UUFDakIsSUFBSTtZQUNGLE1BQU0sSUFBSUM7UUFDWixFQUFFLE9BQU9DLEdBQUc7O1lBRVQsR0FBRUYsS0FBSyxFQUFFLEdBQUdFLENBQUFBO1FBQ2Y7UUFDQUMsUUFBUUMsSUFBSSxDQUNWLG1UQUNBO1lBQUVKO1FBQU07SUFFWjtBQUNGO0FBRUEsMkNBQTJDO0FBQzNDLElBQUlLLHlCQUF5QixDQUFDQyw0QkFBNEJDLFNBQVNDO0lBQ2pFLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxjQUFjLEVBQUUsR0FBR0g7SUFDcEMsTUFBTSxFQUFFSSxvQkFBb0IsRUFBRUMsd0JBQXdCLEVBQUUsR0FBR047SUFDM0QsTUFBTU8sc0JBQXNCSixRQUFRLElBQU8sRUFBQyxPQUFPQztJQUNuRCxNQUFNSSwrQkFBK0JELG9CQUFvQkUsS0FBSyxDQUFDLE1BQU1KLDBCQUEwQkUsb0JBQW9CRSxLQUFLLENBQUMsTUFBTUg7SUFDL0gsSUFBSSxDQUFDRSw4QkFBOEI7UUFDakMsSUFBSWQsUUFBUSxLQUFLO1FBQ2pCLElBQUk7WUFDRixNQUFNLElBQUlDO1FBQ1osRUFBRSxPQUFPQyxHQUFHOztZQUVULEdBQUVGLEtBQUssRUFBRSxHQUFHRSxDQUFBQTtRQUNmO1FBQ0FDLFFBQVFDLElBQUksQ0FDVix5VEFDQTtZQUNFWSxXQUFXUjtZQUNYUyxhQUFhTjtZQUNiTyxjQUFjTjtZQUNkWjtRQUNGO0lBRUo7QUFDRjtBQUVBLDhDQUE4QztBQUM5QyxJQUFJbUIsc0JBQXNCO0lBQ3hCQyxxQkFBcUI7SUFDckJDLHVCQUF1QjtBQUN6QjtBQUNBLElBQUlDLHlCQUF5QixDQUFDQztJQUM1QkMsT0FBT0MsTUFBTSxDQUFDTixxQkFBcUJJO0FBQ3JDO0FBRUEsZUFBZTtBQUNmLElBQUlHLFlBQVk7QUFDaEIsU0FBU0MsaUJBQWlCQyxJQUFJLEVBQUVDLGVBQWUsQ0FBQyxzQ0FBc0MsRUFBRSxPQUFPRCxLQUFLLENBQUM7SUFDbkcsSUFBSSxPQUFPQSxTQUFTLFlBQVk7UUFDOUIsTUFBTSxJQUFJRSxVQUFVRDtJQUN0QjtBQUNGO0FBQ0EsU0FBU0UsZUFBZUMsTUFBTSxFQUFFSCxlQUFlLENBQUMscUNBQXFDLEVBQUUsT0FBT0csT0FBTyxDQUFDO0lBQ3BHLElBQUksT0FBT0EsV0FBVyxVQUFVO1FBQzlCLE1BQU0sSUFBSUYsVUFBVUQ7SUFDdEI7QUFDRjtBQUNBLFNBQVNJLHlCQUF5QkMsS0FBSyxFQUFFTCxlQUFlLENBQUMsMEVBQTBFLENBQUM7SUFDbEksSUFBSSxDQUFDSyxNQUFNQyxLQUFLLENBQUMsQ0FBQ0MsT0FBUyxPQUFPQSxTQUFTLGFBQWE7UUFDdEQsTUFBTUMsWUFBWUgsTUFBTUksR0FBRyxDQUN6QixDQUFDRixPQUFTLE9BQU9BLFNBQVMsYUFBYSxDQUFDLFNBQVMsRUFBRUEsS0FBS0csSUFBSSxJQUFJLFVBQVUsRUFBRSxDQUFDLEdBQUcsT0FBT0gsTUFDdkZJLElBQUksQ0FBQztRQUNQLE1BQU0sSUFBSVYsVUFBVSxDQUFDLEVBQUVELGFBQWEsQ0FBQyxFQUFFUSxVQUFVLENBQUMsQ0FBQztJQUNyRDtBQUNGO0FBQ0EsSUFBSUksZ0JBQWdCLENBQUNMO0lBQ25CLE9BQU9NLE1BQU1DLE9BQU8sQ0FBQ1AsUUFBUUEsT0FBTztRQUFDQTtLQUFLO0FBQzVDO0FBQ0EsU0FBU1EsZ0JBQWdCQyxrQkFBa0I7SUFDekMsTUFBTUMsZUFBZUosTUFBTUMsT0FBTyxDQUFDRSxrQkFBa0IsQ0FBQyxFQUFFLElBQUlBLGtCQUFrQixDQUFDLEVBQUUsR0FBR0E7SUFDcEZaLHlCQUNFYSxjQUNBLENBQUMsOEZBQThGLENBQUM7SUFFbEcsT0FBT0E7QUFDVDtBQUNBLFNBQVNDLDRCQUE0QkQsWUFBWSxFQUFFdEMsaUJBQWlCO0lBQ2xFLE1BQU1HLHVCQUF1QixFQUFFO0lBQy9CLE1BQU0sRUFBRXFDLE1BQU0sRUFBRSxHQUFHRjtJQUNuQixJQUFLLElBQUlHLElBQUksR0FBR0EsSUFBSUQsUUFBUUMsSUFBSztRQUMvQnRDLHFCQUFxQnVDLElBQUksQ0FBQ0osWUFBWSxDQUFDRyxFQUFFLENBQUNsQyxLQUFLLENBQUMsTUFBTVA7SUFDeEQ7SUFDQSxPQUFPRztBQUNUO0FBQ0EsSUFBSXdDLGdDQUFnQyxDQUFDQyxVQUFVN0I7SUFDN0MsTUFBTSxFQUFFRixxQkFBcUIsRUFBRUQsbUJBQW1CLEVBQUUsR0FBRztRQUNyRCxHQUFHRCxtQkFBbUI7UUFDdEIsR0FBR0ksYUFBYTtJQUNsQjtJQUNBLE9BQU87UUFDTEYsdUJBQXVCO1lBQ3JCZ0MsV0FBV2hDLDBCQUEwQixZQUFZQSwwQkFBMEIsVUFBVStCO1lBQ3JGRSxLQUFLMUQ7UUFDUDtRQUNBd0IscUJBQXFCO1lBQ25CaUMsV0FBV2pDLHdCQUF3QixZQUFZQSx3QkFBd0IsVUFBVWdDO1lBQ2pGRSxLQUFLakQ7UUFDUDtJQUNGO0FBQ0Y7QUFFQSx1Q0FBdUM7QUFDdkMsSUFBSWtELFlBQVk7QUFDaEIsSUFBSUMsa0JBQWtCO0FBQ3RCLElBQUlDLE9BQU87SUFLVEMsWUFBWUMsWUFBWSxFQUFFQyxVQUFVQyxRQUFRLENBQUU7YUFKOUNDLFdBQVdQO2FBR1hRLFdBQVdGO1FBRVQsSUFBSSxDQUFDRyxNQUFNLEdBQUcsSUFBSSxDQUFDQyxVQUFVLEdBQUdOO1FBQ2hDLElBQUksQ0FBQ0ksUUFBUSxHQUFHSDtJQUNsQjtJQUNBLCtFQUErRTtJQUMvRSxvREFBb0Q7SUFDcEQsSUFBSU0sUUFBUTtRQUNWVixpQkFBaUJXLElBQUksSUFBSTtRQUN6QixPQUFPLElBQUksQ0FBQ0gsTUFBTTtJQUNwQjtJQUNBLDBFQUEwRTtJQUMxRSw2RUFBNkU7SUFDN0Usd0VBQXdFO0lBQ3hFLDRFQUE0RTtJQUM1RSx1RUFBdUU7SUFDdkUsSUFBSUUsTUFBTUUsUUFBUSxFQUFFO1FBQ2xCLElBQUksSUFBSSxDQUFDRixLQUFLLEtBQUtFLFVBQ2pCO1FBQ0YsSUFBSSxDQUFDSixNQUFNLEdBQUdJO1FBQ2QsSUFBSSxDQUFDTixRQUFRLEdBQUcsRUFBRVA7SUFDcEI7QUFDRjtBQUNBLFNBQVNNLFNBQVNRLENBQUMsRUFBRUMsQ0FBQztJQUNwQixPQUFPRCxNQUFNQztBQUNmO0FBQ0EsSUFBSUMsZ0JBQWdCO0lBTWxCYixZQUFZYyxFQUFFLENBQUU7YUFKaEJDLGtCQUFrQixDQUFDO2FBQ25CQyxRQUFRLEVBQUU7YUFDVkMsT0FBTztRQUdMLElBQUksQ0FBQ0gsRUFBRSxHQUFHQTtJQUNaO0lBQ0FJLFFBQVE7UUFDTixJQUFJLENBQUNDLFlBQVksR0FBRyxLQUFLO1FBQ3pCLElBQUksQ0FBQ0osZUFBZSxHQUFHLENBQUM7UUFDeEIsSUFBSSxDQUFDQyxLQUFLLEdBQUcsRUFBRTtRQUNmLElBQUksQ0FBQ0MsSUFBSSxHQUFHO0lBQ2Q7SUFDQSxJQUFJVCxRQUFRO1FBQ1YsSUFBSSxJQUFJLENBQUNKLFFBQVEsR0FBRyxJQUFJLENBQUNXLGVBQWUsRUFBRTtZQUN4QyxNQUFNLEVBQUVELEVBQUUsRUFBRSxHQUFHLElBQUk7WUFDbkIsTUFBTU0saUJBQWlCLGFBQWEsR0FBRyxJQUFJQztZQUMzQyxNQUFNQyxjQUFjeEI7WUFDcEJBLGtCQUFrQnNCO1lBQ2xCLElBQUksQ0FBQ0QsWUFBWSxHQUFHTDtZQUNwQmhCLGtCQUFrQndCO1lBQ2xCLElBQUksQ0FBQ0wsSUFBSTtZQUNULElBQUksQ0FBQ0QsS0FBSyxHQUFHaEMsTUFBTXVDLElBQUksQ0FBQ0g7WUFDeEIsSUFBSSxDQUFDTCxlQUFlLEdBQUcsSUFBSSxDQUFDWCxRQUFRO1FBQ3RDO1FBQ0FOLGlCQUFpQlcsSUFBSSxJQUFJO1FBQ3pCLE9BQU8sSUFBSSxDQUFDVSxZQUFZO0lBQzFCO0lBQ0EsSUFBSWYsV0FBVztRQUNiLE9BQU9vQixLQUFLQyxHQUFHLElBQUksSUFBSSxDQUFDVCxLQUFLLENBQUNwQyxHQUFHLENBQUMsQ0FBQzhDLElBQU1BLEVBQUV0QixRQUFRLEdBQUc7SUFDeEQ7QUFDRjtBQUNBLFNBQVN1QixTQUFTQyxJQUFJO0lBQ3BCLElBQUksQ0FBRUEsQ0FBQUEsZ0JBQWdCN0IsSUFBRyxHQUFJO1FBQzNCdEQsUUFBUUMsSUFBSSxDQUFDLHNCQUFzQmtGO0lBQ3JDO0lBQ0EsT0FBT0EsS0FBS3BCLEtBQUs7QUFDbkI7QUFDQSxTQUFTcUIsU0FBU0MsT0FBTyxFQUFFdEIsS0FBSztJQUM5QixJQUFJLENBQUVzQixDQUFBQSxtQkFBbUIvQixJQUFHLEdBQUk7UUFDOUIsTUFBTSxJQUFJM0IsVUFDUjtJQUVKO0lBQ0EwRCxRQUFRdEIsS0FBSyxHQUFHc0IsUUFBUXZCLFVBQVUsR0FBR0M7QUFDdkM7QUFDQSxTQUFTdUIsV0FBVzlCLFlBQVksRUFBRUMsVUFBVUMsUUFBUTtJQUNsRCxPQUFPLElBQUlKLEtBQUtFLGNBQWNDO0FBQ2hDO0FBQ0EsU0FBUzhCLFlBQVlsQixFQUFFO0lBQ3JCN0MsaUJBQ0U2QyxJQUNBO0lBRUYsT0FBTyxJQUFJRCxjQUFjQztBQUMzQjtBQUVBLG1DQUFtQztBQUNuQyxJQUFJbUIsVUFBVSxDQUFDdEIsR0FBR0MsSUFBTTtBQUN4QixTQUFTc0I7SUFDUCxPQUFPSCxXQUFXLE1BQU1FO0FBQzFCO0FBQ0EsU0FBU0UsU0FBU0MsR0FBRyxFQUFFNUIsS0FBSztJQUMxQnFCLFNBQVNPLEtBQUs1QjtBQUNoQjtBQUNBLElBQUk2QixvQkFBb0IsQ0FBQ0M7SUFDdkIsSUFBSUYsTUFBTUUsS0FBS0MsYUFBYTtJQUM1QixJQUFJSCxRQUFRLE1BQU07UUFDaEJBLE1BQU1FLEtBQUtDLGFBQWEsR0FBR0w7SUFDN0I7SUFDQVAsU0FBU1M7QUFDWDtBQUNBLElBQUlJLGtCQUFrQixDQUFDRjtJQUNyQixNQUFNRixNQUFNRSxLQUFLQyxhQUFhO0lBQzlCLElBQUlILFFBQVEsTUFBTTtRQUNoQkQsU0FBU0MsS0FBSztJQUNoQjtBQUNGO0FBRUEsZ0NBQWdDO0FBQ2hDLElBQUlLLG9CQUFvQkM7QUFDeEIsSUFBSUMsU0FBUztBQUNiLElBQUlDLFFBQVE5RSxPQUFPK0UsY0FBYyxDQUFDLENBQUM7QUFDbkMsSUFBSUMsaUJBQWlCO0lBQ25COUMsWUFBWVEsS0FBSyxDQUFFO2FBS25CdUMsUUFBUSxJQUFJQyxNQUFNLElBQUksRUFBRUM7YUFDeEJiLE1BQU1GO2FBQ05nQixPQUFPLENBQUM7YUFDUkMsV0FBVyxDQUFDO2FBQ1paLGdCQUFnQjthQUNoQmEsS0FBS1Q7UUFUSCxJQUFJLENBQUNuQyxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDQSxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDNEIsR0FBRyxDQUFDNUIsS0FBSyxHQUFHQTtJQUNuQjtBQU9GO0FBQ0EsSUFBSXlDLHFCQUFxQjtJQUN2QkksS0FBSWYsSUFBSSxFQUFFZ0IsR0FBRztRQUNYLFNBQVNDO1lBQ1AsTUFBTSxFQUFFL0MsS0FBSyxFQUFFLEdBQUc4QjtZQUNsQixNQUFNa0IsYUFBYUMsUUFBUUosR0FBRyxDQUFDN0MsT0FBTzhDO1lBQ3RDLElBQUksT0FBT0EsUUFBUSxVQUFVO2dCQUMzQixPQUFPRTtZQUNUO1lBQ0EsSUFBSUYsT0FBT1YsT0FBTztnQkFDaEIsT0FBT1k7WUFDVDtZQUNBLElBQUksT0FBT0EsZUFBZSxZQUFZQSxlQUFlLE1BQU07Z0JBQ3pELElBQUlFLFlBQVlwQixLQUFLYSxRQUFRLENBQUNHLElBQUk7Z0JBQ2xDLElBQUlJLGNBQWMsS0FBSyxHQUFHO29CQUN4QkEsWUFBWXBCLEtBQUthLFFBQVEsQ0FBQ0csSUFBSSxHQUFHSyxXQUFXSDtnQkFDOUM7Z0JBQ0EsSUFBSUUsVUFBVXRCLEdBQUcsRUFBRTtvQkFDakJULFNBQVMrQixVQUFVdEIsR0FBRztnQkFDeEI7Z0JBQ0EsT0FBT3NCLFVBQVVYLEtBQUs7WUFDeEIsT0FBTztnQkFDTCxJQUFJWCxNQUFNRSxLQUFLWSxJQUFJLENBQUNJLElBQUk7Z0JBQ3hCLElBQUlsQixRQUFRLEtBQUssR0FBRztvQkFDbEJBLE1BQU1FLEtBQUtZLElBQUksQ0FBQ0ksSUFBSSxHQUFHcEI7b0JBQ3ZCRSxJQUFJNUIsS0FBSyxHQUFHZ0Q7Z0JBQ2Q7Z0JBQ0E3QixTQUFTUztnQkFDVCxPQUFPb0I7WUFDVDtRQUNGO1FBQ0EsTUFBTUksTUFBTUw7UUFDWixPQUFPSztJQUNUO0lBQ0FDLFNBQVF2QixJQUFJO1FBQ1ZELGtCQUFrQkM7UUFDbEIsT0FBT21CLFFBQVFJLE9BQU8sQ0FBQ3ZCLEtBQUs5QixLQUFLO0lBQ25DO0lBQ0FzRCwwQkFBeUJ4QixJQUFJLEVBQUV5QixJQUFJO1FBQ2pDLE9BQU9OLFFBQVFLLHdCQUF3QixDQUFDeEIsS0FBSzlCLEtBQUssRUFBRXVEO0lBQ3REO0lBQ0FDLEtBQUkxQixJQUFJLEVBQUV5QixJQUFJO1FBQ1osT0FBT04sUUFBUU8sR0FBRyxDQUFDMUIsS0FBSzlCLEtBQUssRUFBRXVEO0lBQ2pDO0FBQ0Y7QUFDQSxJQUFJRSxnQkFBZ0I7SUFDbEJqRSxZQUFZUSxLQUFLLENBQUU7YUFLbkJ1QyxRQUFRLElBQUlDLE1BQU07WUFBQyxJQUFJO1NBQUMsRUFBRWtCO2FBQzFCOUIsTUFBTUY7YUFDTmdCLE9BQU8sQ0FBQzthQUNSQyxXQUFXLENBQUM7YUFDWlosZ0JBQWdCO2FBQ2hCYSxLQUFLVDtRQVRILElBQUksQ0FBQ25DLEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUNBLEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUM0QixHQUFHLENBQUM1QixLQUFLLEdBQUdBO0lBQ25CO0FBT0Y7QUFDQSxJQUFJMEQsb0JBQW9CO0lBQ3RCYixLQUFJLENBQUNmLEtBQUssRUFBRWdCLEdBQUc7UUFDYixJQUFJQSxRQUFRLFVBQVU7WUFDcEJqQixrQkFBa0JDO1FBQ3BCO1FBQ0EsT0FBT1csbUJBQW1CSSxHQUFHLENBQUNmLE1BQU1nQjtJQUN0QztJQUNBTyxTQUFRLENBQUN2QixLQUFLO1FBQ1osT0FBT1csbUJBQW1CWSxPQUFPLENBQUN2QjtJQUNwQztJQUNBd0IsMEJBQXlCLENBQUN4QixLQUFLLEVBQUV5QixJQUFJO1FBQ25DLE9BQU9kLG1CQUFtQmEsd0JBQXdCLENBQUN4QixNQUFNeUI7SUFDM0Q7SUFDQUMsS0FBSSxDQUFDMUIsS0FBSyxFQUFFeUIsSUFBSTtRQUNkLE9BQU9kLG1CQUFtQmUsR0FBRyxDQUFDMUIsTUFBTXlCO0lBQ3RDO0FBQ0Y7QUFDQSxTQUFTSixXQUFXbkQsS0FBSztJQUN2QixJQUFJeEIsTUFBTUMsT0FBTyxDQUFDdUIsUUFBUTtRQUN4QixPQUFPLElBQUl5RCxjQUFjekQ7SUFDM0I7SUFDQSxPQUFPLElBQUlzQyxlQUFldEM7QUFDNUI7QUFDQSxTQUFTMkQsV0FBVzdCLElBQUksRUFBRTVCLFFBQVE7SUFDaEMsTUFBTSxFQUFFRixLQUFLLEVBQUUwQyxJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHYjtJQUNsQ0EsS0FBSzlCLEtBQUssR0FBR0U7SUFDYixJQUFJMUIsTUFBTUMsT0FBTyxDQUFDdUIsVUFBVXhCLE1BQU1DLE9BQU8sQ0FBQ3lCLGFBQWFGLE1BQU1sQixNQUFNLEtBQUtvQixTQUFTcEIsTUFBTSxFQUFFO1FBQ3ZGa0QsZ0JBQWdCRjtJQUNsQixPQUFPO1FBQ0wsSUFBSTlCLFVBQVVFLFVBQVU7WUFDdEIsSUFBSTBELGNBQWM7WUFDbEIsSUFBSUMsY0FBYztZQUNsQixJQUFJQyxlQUFlO1lBQ25CLElBQUssTUFBTUMsUUFBUS9ELE1BQU87Z0JBQ3hCNEQ7WUFDRjtZQUNBLElBQUssTUFBTWQsT0FBTzVDLFNBQVU7Z0JBQzFCMkQ7Z0JBQ0EsSUFBSSxDQUFFZixDQUFBQSxPQUFPOUMsS0FBSSxHQUFJO29CQUNuQjhELGVBQWU7b0JBQ2Y7Z0JBQ0Y7WUFDRjtZQUNBLE1BQU1FLGNBQWNGLGdCQUFnQkYsZ0JBQWdCQztZQUNwRCxJQUFJRyxhQUFhO2dCQUNmaEMsZ0JBQWdCRjtZQUNsQjtRQUNGO0lBQ0Y7SUFDQSxJQUFLLE1BQU1nQixPQUFPSixLQUFNO1FBQ3RCLE1BQU1NLGFBQWFoRCxLQUFLLENBQUM4QyxJQUFJO1FBQzdCLE1BQU1tQixnQkFBZ0IvRCxRQUFRLENBQUM0QyxJQUFJO1FBQ25DLElBQUlFLGVBQWVpQixlQUFlO1lBQ2hDakMsZ0JBQWdCRjtZQUNoQkgsU0FBU2UsSUFBSSxDQUFDSSxJQUFJLEVBQUVtQjtRQUN0QjtRQUNBLElBQUksT0FBT0Esa0JBQWtCLFlBQVlBLGtCQUFrQixNQUFNO1lBQy9ELE9BQU92QixJQUFJLENBQUNJLElBQUk7UUFDbEI7SUFDRjtJQUNBLElBQUssTUFBTUEsT0FBT0gsU0FBVTtRQUMxQixNQUFNTyxZQUFZUCxRQUFRLENBQUNHLElBQUk7UUFDL0IsTUFBTW1CLGdCQUFnQi9ELFFBQVEsQ0FBQzRDLElBQUk7UUFDbkMsTUFBTUUsYUFBYUUsVUFBVWxELEtBQUs7UUFDbEMsSUFBSWdELGVBQWVpQixlQUFlO1lBQ2hDO1FBQ0YsT0FBTyxJQUFJLE9BQU9BLGtCQUFrQixZQUFZQSxrQkFBa0IsTUFBTTtZQUN0RU4sV0FBV1QsV0FBV2U7UUFDeEIsT0FBTztZQUNMQyxXQUFXaEI7WUFDWCxPQUFPUCxRQUFRLENBQUNHLElBQUk7UUFDdEI7SUFDRjtBQUNGO0FBQ0EsU0FBU29CLFdBQVdwQyxJQUFJO0lBQ3RCLElBQUlBLEtBQUtGLEdBQUcsRUFBRTtRQUNaRCxTQUFTRyxLQUFLRixHQUFHLEVBQUU7SUFDckI7SUFDQUksZ0JBQWdCRjtJQUNoQixJQUFLLE1BQU1nQixPQUFPaEIsS0FBS1ksSUFBSSxDQUFFO1FBQzNCZixTQUFTRyxLQUFLWSxJQUFJLENBQUNJLElBQUksRUFBRTtJQUMzQjtJQUNBLElBQUssTUFBTUEsT0FBT2hCLEtBQUthLFFBQVEsQ0FBRTtRQUMvQnVCLFdBQVdwQyxLQUFLYSxRQUFRLENBQUNHLElBQUk7SUFDL0I7QUFDRjtBQUVBLG9CQUFvQjtBQUNwQixTQUFTcUIscUJBQXFCQyxNQUFNO0lBQ2xDLElBQUlDO0lBQ0osT0FBTztRQUNMeEIsS0FBSUMsR0FBRztZQUNMLElBQUl1QixTQUFTRCxPQUFPQyxNQUFNdkIsR0FBRyxFQUFFQSxNQUFNO2dCQUNuQyxPQUFPdUIsTUFBTXJFLEtBQUs7WUFDcEI7WUFDQSxPQUFPeEM7UUFDVDtRQUNBOEcsS0FBSXhCLEdBQUcsRUFBRTlDLEtBQUs7WUFDWnFFLFFBQVE7Z0JBQUV2QjtnQkFBSzlDO1lBQU07UUFDdkI7UUFDQXVFO1lBQ0UsT0FBT0YsUUFBUTtnQkFBQ0E7YUFBTSxHQUFHLEVBQUU7UUFDN0I7UUFDQTNEO1lBQ0UyRCxRQUFRLEtBQUs7UUFDZjtJQUNGO0FBQ0Y7QUFDQSxTQUFTRyxlQUFlQyxPQUFPLEVBQUVMLE1BQU07SUFDckMsSUFBSU0sVUFBVSxFQUFFO0lBQ2hCLFNBQVM3QixJQUFJQyxHQUFHO1FBQ2QsTUFBTTZCLGFBQWFELFFBQVFFLFNBQVMsQ0FBQyxDQUFDUCxRQUFVRCxPQUFPdEIsS0FBS3VCLE1BQU12QixHQUFHO1FBQ3JFLElBQUk2QixhQUFhLENBQUMsR0FBRztZQUNuQixNQUFNTixRQUFRSyxPQUFPLENBQUNDLFdBQVc7WUFDakMsSUFBSUEsYUFBYSxHQUFHO2dCQUNsQkQsUUFBUUcsTUFBTSxDQUFDRixZQUFZO2dCQUMzQkQsUUFBUUksT0FBTyxDQUFDVDtZQUNsQjtZQUNBLE9BQU9BLE1BQU1yRSxLQUFLO1FBQ3BCO1FBQ0EsT0FBT3hDO0lBQ1Q7SUFDQSxTQUFTOEcsSUFBSXhCLEdBQUcsRUFBRTlDLEtBQUs7UUFDckIsSUFBSTZDLElBQUlDLFNBQVN0RixXQUFXO1lBQzFCa0gsUUFBUUksT0FBTyxDQUFDO2dCQUFFaEM7Z0JBQUs5QztZQUFNO1lBQzdCLElBQUkwRSxRQUFRNUYsTUFBTSxHQUFHMkYsU0FBUztnQkFDNUJDLFFBQVFLLEdBQUc7WUFDYjtRQUNGO0lBQ0Y7SUFDQSxTQUFTUjtRQUNQLE9BQU9HO0lBQ1Q7SUFDQSxTQUFTaEU7UUFDUGdFLFVBQVUsRUFBRTtJQUNkO0lBQ0EsT0FBTztRQUFFN0I7UUFBS3lCO1FBQUtDO1FBQVk3RDtJQUFNO0FBQ3ZDO0FBQ0EsSUFBSXNFLHlCQUF5QixDQUFDN0UsR0FBR0MsSUFBTUQsTUFBTUM7QUFDN0MsU0FBUzZFLHlCQUF5QkMsYUFBYTtJQUM3QyxPQUFPLFNBQVNDLDJCQUEyQkMsSUFBSSxFQUFFQyxJQUFJO1FBQ25ELElBQUlELFNBQVMsUUFBUUMsU0FBUyxRQUFRRCxLQUFLdEcsTUFBTSxLQUFLdUcsS0FBS3ZHLE1BQU0sRUFBRTtZQUNqRSxPQUFPO1FBQ1Q7UUFDQSxNQUFNLEVBQUVBLE1BQU0sRUFBRSxHQUFHc0c7UUFDbkIsSUFBSyxJQUFJckcsSUFBSSxHQUFHQSxJQUFJRCxRQUFRQyxJQUFLO1lBQy9CLElBQUksQ0FBQ21HLGNBQWNFLElBQUksQ0FBQ3JHLEVBQUUsRUFBRXNHLElBQUksQ0FBQ3RHLEVBQUUsR0FBRztnQkFDcEMsT0FBTztZQUNUO1FBQ0Y7UUFDQSxPQUFPO0lBQ1Q7QUFDRjtBQUNBLFNBQVN1RyxXQUFXNUgsSUFBSSxFQUFFNkgsc0JBQXNCO0lBQzlDLE1BQU1DLGtCQUFrQixPQUFPRCwyQkFBMkIsV0FBV0EseUJBQXlCO1FBQUVMLGVBQWVLO0lBQXVCO0lBQ3RJLE1BQU0sRUFDSkwsZ0JBQWdCRixzQkFBc0IsRUFDdENQLFVBQVUsQ0FBQyxFQUNYZ0IsbUJBQW1CLEVBQ3BCLEdBQUdEO0lBQ0osTUFBTUUsYUFBYVQseUJBQXlCQztJQUM1QyxJQUFJUyxlQUFlO0lBQ25CLE1BQU1DLFFBQVFuQixZQUFZLElBQUlOLHFCQUFxQnVCLGNBQWNsQixlQUFlQyxTQUFTaUI7SUFDekYsU0FBU0c7UUFDUCxJQUFJN0YsUUFBUTRGLE1BQU0vQyxHQUFHLENBQUMvRjtRQUN0QixJQUFJa0QsVUFBVXhDLFdBQVc7WUFDdkJ3QyxRQUFRdEMsS0FBS2IsS0FBSyxDQUFDLE1BQU1DO1lBQ3pCNkk7WUFDQSxJQUFJRixxQkFBcUI7Z0JBQ3ZCLE1BQU1mLFVBQVVrQixNQUFNckIsVUFBVTtnQkFDaEMsTUFBTXVCLGdCQUFnQnBCLFFBQVFxQixJQUFJLENBQ2hDLENBQUMxQixRQUFVb0Isb0JBQW9CcEIsTUFBTXJFLEtBQUssRUFBRUE7Z0JBRTlDLElBQUk4RixlQUFlO29CQUNqQjlGLFFBQVE4RixjQUFjOUYsS0FBSztvQkFDM0IyRixpQkFBaUIsS0FBS0E7Z0JBQ3hCO1lBQ0Y7WUFDQUMsTUFBTXRCLEdBQUcsQ0FBQ3hILFdBQVdrRDtRQUN2QjtRQUNBLE9BQU9BO0lBQ1Q7SUFDQTZGLFNBQVNHLFVBQVUsR0FBRztRQUNwQkosTUFBTWxGLEtBQUs7UUFDWG1GLFNBQVNJLGlCQUFpQjtJQUM1QjtJQUNBSixTQUFTRixZQUFZLEdBQUcsSUFBTUE7SUFDOUJFLFNBQVNJLGlCQUFpQixHQUFHO1FBQzNCTixlQUFlO0lBQ2pCO0lBQ0EsT0FBT0U7QUFDVDtBQUVBLDJDQUEyQztBQUMzQyxTQUFTSyxpQkFBaUJ4SSxJQUFJO0lBQzVCLE1BQU1vRSxPQUFPcUIsV0FDWCxFQUFFO0lBRUosSUFBSWdELFdBQVc7SUFDZixNQUFNQyxlQUFlbkIseUJBQXlCRDtJQUM5QyxNQUFNWSxRQUFRcEUsWUFBWTtRQUN4QixNQUFNNEIsTUFBTTFGLEtBQUtiLEtBQUssQ0FBQyxNQUFNaUYsS0FBS1MsS0FBSztRQUN2QyxPQUFPYTtJQUNUO0lBQ0EsU0FBU3lDO1FBQ1AsSUFBSSxDQUFDTyxhQUFhRCxVQUFVckosWUFBWTtZQUN0QzZHLFdBQVc3QixNQUFNaEY7WUFDakJxSixXQUFXcko7UUFDYjtRQUNBLE9BQU84SSxNQUFNNUYsS0FBSztJQUNwQjtJQUNBNkYsU0FBU0csVUFBVSxHQUFHO1FBQ3BCLE9BQU9KLE1BQU1sRixLQUFLO0lBQ3BCO0lBQ0EsT0FBT21GO0FBQ1Q7QUFFQSx3QkFBd0I7QUFDeEIsSUFBSVEsWUFBWTtJQUNkN0csWUFBWVEsS0FBSyxDQUFFO1FBQ2pCLElBQUksQ0FBQ0EsS0FBSyxHQUFHQTtJQUNmO0lBQ0FzRyxRQUFRO1FBQ04sT0FBTyxJQUFJLENBQUN0RyxLQUFLO0lBQ25CO0FBQ0Y7QUFDQSxJQUFJdUcsTUFBTSxPQUFPQyxZQUFZLGNBQWNBLFVBQVVIO0FBQ3JELElBQUlJLGVBQWU7QUFDbkIsSUFBSUMsYUFBYTtBQUNqQixTQUFTQztJQUNQLE9BQU87UUFDTEMsR0FBR0g7UUFDSEksR0FBRyxLQUFLO1FBQ1JDLEdBQUc7UUFDSEMsR0FBRztJQUNMO0FBQ0Y7QUFDQSxTQUFTQyxlQUFldEosSUFBSSxFQUFFckIsVUFBVSxDQUFDLENBQUM7SUFDeEMsSUFBSTRLLFNBQVNOO0lBQ2IsTUFBTSxFQUFFbEIsbUJBQW1CLEVBQUUsR0FBR3BKO0lBQ2hDLElBQUk2SztJQUNKLElBQUl2QixlQUFlO0lBQ25CLFNBQVNFO1FBQ1AsSUFBSXNCLFlBQVlGO1FBQ2hCLE1BQU0sRUFBRW5JLE1BQU0sRUFBRSxHQUFHaEM7UUFDbkIsSUFBSyxJQUFJaUMsSUFBSSxHQUFHcUksSUFBSXRJLFFBQVFDLElBQUlxSSxHQUFHckksSUFBSztZQUN0QyxNQUFNc0ksTUFBTXZLLFNBQVMsQ0FBQ2lDLEVBQUU7WUFDeEIsSUFBSSxPQUFPc0ksUUFBUSxjQUFjLE9BQU9BLFFBQVEsWUFBWUEsUUFBUSxNQUFNO2dCQUN4RSxJQUFJQyxjQUFjSCxVQUFVTCxDQUFDO2dCQUM3QixJQUFJUSxnQkFBZ0IsTUFBTTtvQkFDeEJILFVBQVVMLENBQUMsR0FBR1EsY0FBYyxhQUFhLEdBQUcsSUFBSUM7Z0JBQ2xEO2dCQUNBLE1BQU1DLGFBQWFGLFlBQVl6RSxHQUFHLENBQUN3RTtnQkFDbkMsSUFBSUcsZUFBZSxLQUFLLEdBQUc7b0JBQ3pCTCxZQUFZUjtvQkFDWlcsWUFBWUcsR0FBRyxDQUFDSixLQUFLRjtnQkFDdkIsT0FBTztvQkFDTEEsWUFBWUs7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMLElBQUlFLGlCQUFpQlAsVUFBVUosQ0FBQztnQkFDaEMsSUFBSVcsbUJBQW1CLE1BQU07b0JBQzNCUCxVQUFVSixDQUFDLEdBQUdXLGlCQUFpQixhQUFhLEdBQUcsSUFBSUM7Z0JBQ3JEO2dCQUNBLE1BQU1DLGdCQUFnQkYsZUFBZTdFLEdBQUcsQ0FBQ3dFO2dCQUN6QyxJQUFJTyxrQkFBa0IsS0FBSyxHQUFHO29CQUM1QlQsWUFBWVI7b0JBQ1plLGVBQWVELEdBQUcsQ0FBQ0osS0FBS0Y7Z0JBQzFCLE9BQU87b0JBQ0xBLFlBQVlTO2dCQUNkO1lBQ0Y7UUFDRjtRQUNBLE1BQU1DLGlCQUFpQlY7UUFDdkIsSUFBSVc7UUFDSixJQUFJWCxVQUFVUCxDQUFDLEtBQUtGLFlBQVk7WUFDOUJvQixTQUFTWCxVQUFVTixDQUFDO1FBQ3RCLE9BQU87WUFDTGlCLFNBQVNwSyxLQUFLYixLQUFLLENBQUMsTUFBTUM7WUFDMUI2STtRQUNGO1FBQ0FrQyxlQUFlakIsQ0FBQyxHQUFHRjtRQUNuQixJQUFJakIscUJBQXFCO1lBQ3ZCLE1BQU1zQyxrQkFBa0JiLFlBQVlaLFdBQVdZO1lBQy9DLElBQUlhLG1CQUFtQixRQUFRdEMsb0JBQW9Cc0MsaUJBQWlCRCxTQUFTO2dCQUMzRUEsU0FBU0M7Z0JBQ1RwQyxpQkFBaUIsS0FBS0E7WUFDeEI7WUFDQSxNQUFNcUMsZUFBZSxPQUFPRixXQUFXLFlBQVlBLFdBQVcsUUFBUSxPQUFPQSxXQUFXO1lBQ3hGWixhQUFhYyxlQUFlLElBQUl6QixJQUFJdUIsVUFBVUE7UUFDaEQ7UUFDQUQsZUFBZWhCLENBQUMsR0FBR2lCO1FBQ25CLE9BQU9BO0lBQ1Q7SUFDQWpDLFNBQVNHLFVBQVUsR0FBRztRQUNwQmlCLFNBQVNOO1FBQ1RkLFNBQVNJLGlCQUFpQjtJQUM1QjtJQUNBSixTQUFTRixZQUFZLEdBQUcsSUFBTUE7SUFDOUJFLFNBQVNJLGlCQUFpQixHQUFHO1FBQzNCTixlQUFlO0lBQ2pCO0lBQ0EsT0FBT0U7QUFDVDtBQUVBLCtCQUErQjtBQUMvQixTQUFTb0Msc0JBQXNCQyxnQkFBZ0IsRUFBRSxHQUFHQyxzQkFBc0I7SUFDeEUsTUFBTUMsK0JBQStCLE9BQU9GLHFCQUFxQixhQUFhO1FBQzVFM0wsU0FBUzJMO1FBQ1QxTCxnQkFBZ0IyTDtJQUNsQixJQUFJRDtJQUNKLE1BQU1HLGtCQUFrQixDQUFDLEdBQUcxSjtRQUMxQixJQUFJMkosaUJBQWlCO1FBQ3JCLElBQUlDLDJCQUEyQjtRQUMvQixJQUFJckI7UUFDSixJQUFJc0Isd0JBQXdCLENBQUM7UUFDN0IsSUFBSTdNLGFBQWFnRCxtQkFBbUJvRyxHQUFHO1FBQ3ZDLElBQUksT0FBT3BKLGVBQWUsVUFBVTtZQUNsQzZNLHdCQUF3QjdNO1lBQ3hCQSxhQUFhZ0QsbUJBQW1Cb0csR0FBRztRQUNyQztRQUNBdEgsaUJBQ0U5QixZQUNBLENBQUMsMkVBQTJFLEVBQUUsT0FBT0EsV0FBVyxDQUFDLENBQUM7UUFFcEcsTUFBTThNLGtCQUFrQjtZQUN0QixHQUFHTCw0QkFBNEI7WUFDL0IsR0FBR0kscUJBQXFCO1FBQzFCO1FBQ0EsTUFBTSxFQUNKak0sT0FBTyxFQUNQQyxpQkFBaUIsRUFBRSxFQUNuQmtNLGNBQWMxQixjQUFjLEVBQzVCMkIscUJBQXFCLEVBQUUsRUFDdkJ0TCxnQkFBZ0IsQ0FBQyxDQUFDLEVBQ25CLEdBQUdvTDtRQUNKLE1BQU1HLHNCQUFzQnJLLGNBQWMvQjtRQUMxQyxNQUFNcU0sMEJBQTBCdEssY0FBY29LO1FBQzlDLE1BQU0vSixlQUFlRixnQkFBZ0JDO1FBQ3JDLE1BQU1tSyxxQkFBcUJ2TSxRQUFRLFNBQVN3TTtZQUMxQ1Q7WUFDQSxPQUFPM00sV0FBV2tCLEtBQUssQ0FDckIsTUFDQUM7UUFFSixNQUFNOEw7UUFDTixJQUFJMUosV0FBVztRQUNmLE1BQU04SixXQUFXTixZQUFZLFNBQVNPO1lBQ3BDVjtZQUNBLE1BQU05TCx1QkFBdUJvQyw0QkFDM0JELGNBQ0E5QjtZQUVGLElBQUlvTSxJQUFxQyxFQUFFO2dCQUN6QyxNQUFNLEVBQUUvTCxxQkFBcUIsRUFBRUQsbUJBQW1CLEVBQUUsR0FBRytCLDhCQUE4QkMsVUFBVTdCO2dCQUMvRixJQUFJRixzQkFBc0JnQyxTQUFTLEVBQUU7b0JBQ25DaEMsc0JBQXNCaUMsR0FBRyxDQUN2QnpEO2dCQUVKO2dCQUNBLElBQUl1QixvQkFBb0JpQyxTQUFTLEVBQUU7b0JBQ2pDLE1BQU16QywyQkFBMkJtQyw0QkFDL0JELGNBQ0E5QjtvQkFFRkksb0JBQW9Ca0MsR0FBRyxDQUNyQjt3QkFBRTNDO3dCQUFzQkM7b0JBQXlCLEdBQ2pEO3dCQUFFSDt3QkFBU0MsZ0JBQWdCb007b0JBQW9CLEdBQy9DOUw7Z0JBRUo7Z0JBQ0EsSUFBSW9DLFVBQ0ZBLFdBQVc7WUFDZjtZQUNBZ0ksYUFBYTRCLG1CQUFtQmpNLEtBQUssQ0FBQyxNQUFNSjtZQUM1QyxPQUFPeUs7UUFDVCxNQUFNMkI7UUFDTixPQUFPdkwsT0FBT0MsTUFBTSxDQUFDeUwsVUFBVTtZQUM3QnJOO1lBQ0FtTjtZQUNBbEs7WUFDQTJKLDBCQUEwQixJQUFNQTtZQUNoQ1ksK0JBQStCO2dCQUM3QlosMkJBQTJCO1lBQzdCO1lBQ0FyQixZQUFZLElBQU1BO1lBQ2xCb0IsZ0JBQWdCLElBQU1BO1lBQ3RCYyxxQkFBcUI7Z0JBQ25CZCxpQkFBaUI7WUFDbkI7WUFDQS9MO1lBQ0FtTTtRQUNGO0lBQ0Y7SUFDQSxPQUFPTDtBQUNUO0FBQ0EsSUFBSWdCLGlCQUFpQixhQUFhLEdBQUdwQixzQkFBc0JqQjtBQUUzRCxrQ0FBa0M7QUFDbEMsSUFBSXNDLDJCQUEyQixDQUFDQyxzQkFBc0JDLGtCQUFrQkgsY0FBYztJQUNwRnhMLGVBQ0UwTCxzQkFDQSxDQUFDLHNIQUFzSCxFQUFFLE9BQU9BLHFCQUFxQixDQUFDO0lBRXhKLE1BQU1FLG9CQUFvQm5NLE9BQU9vTSxJQUFJLENBQUNIO0lBQ3RDLE1BQU0zSyxlQUFlNkssa0JBQWtCckwsR0FBRyxDQUFDLENBQUMwRSxNQUFReUcsb0JBQW9CLENBQUN6RyxJQUFJO0lBQzdFLE1BQU02RyxxQkFBcUJILGdCQUN6QjVLLGNBQ0EsQ0FBQyxHQUFHbkM7UUFDRixPQUFPQSxxQkFBcUJtTixNQUFNLENBQUMsQ0FBQ0MsYUFBYTdKLE9BQU84SjtZQUN0REQsV0FBVyxDQUFDSixpQkFBaUIsQ0FBQ0ssTUFBTSxDQUFDLEdBQUc5SjtZQUN4QyxPQUFPNko7UUFDVCxHQUFHLENBQUM7SUFDTjtJQUVGLE9BQU9GO0FBQ1Q7QUFVRSxDQUNGLHFDQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL29uZXBvaW50LXdlYi8uL25vZGVfbW9kdWxlcy9yZXNlbGVjdC9kaXN0L3Jlc2VsZWN0Lm1qcz80MDhmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9kZXZNb2RlQ2hlY2tzL2lkZW50aXR5RnVuY3Rpb25DaGVjay50c1xudmFyIHJ1bklkZW50aXR5RnVuY3Rpb25DaGVjayA9IChyZXN1bHRGdW5jKSA9PiB7XG4gIGxldCBpc0lucHV0U2FtZUFzT3V0cHV0ID0gZmFsc2U7XG4gIHRyeSB7XG4gICAgY29uc3QgZW1wdHlPYmplY3QgPSB7fTtcbiAgICBpZiAocmVzdWx0RnVuYyhlbXB0eU9iamVjdCkgPT09IGVtcHR5T2JqZWN0KVxuICAgICAgaXNJbnB1dFNhbWVBc091dHB1dCA9IHRydWU7XG4gIH0gY2F0Y2gge1xuICB9XG4gIGlmIChpc0lucHV0U2FtZUFzT3V0cHV0KSB7XG4gICAgbGV0IHN0YWNrID0gdm9pZCAwO1xuICAgIHRyeSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoKTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICA7XG4gICAgICAoeyBzdGFjayB9ID0gZSk7XG4gICAgfVxuICAgIGNvbnNvbGUud2FybihcbiAgICAgIFwiVGhlIHJlc3VsdCBmdW5jdGlvbiByZXR1cm5lZCBpdHMgb3duIGlucHV0cyB3aXRob3V0IG1vZGlmaWNhdGlvbi4gZS5nXFxuYGNyZWF0ZVNlbGVjdG9yKFtzdGF0ZSA9PiBzdGF0ZS50b2Rvc10sIHRvZG9zID0+IHRvZG9zKWBcXG5UaGlzIGNvdWxkIGxlYWQgdG8gaW5lZmZpY2llbnQgbWVtb2l6YXRpb24gYW5kIHVubmVjZXNzYXJ5IHJlLXJlbmRlcnMuXFxuRW5zdXJlIHRyYW5zZm9ybWF0aW9uIGxvZ2ljIGlzIGluIHRoZSByZXN1bHQgZnVuY3Rpb24sIGFuZCBleHRyYWN0aW9uIGxvZ2ljIGlzIGluIHRoZSBpbnB1dCBzZWxlY3RvcnMuXCIsXG4gICAgICB7IHN0YWNrIH1cbiAgICApO1xuICB9XG59O1xuXG4vLyBzcmMvZGV2TW9kZUNoZWNrcy9pbnB1dFN0YWJpbGl0eUNoZWNrLnRzXG52YXIgcnVuSW5wdXRTdGFiaWxpdHlDaGVjayA9IChpbnB1dFNlbGVjdG9yUmVzdWx0c09iamVjdCwgb3B0aW9ucywgaW5wdXRTZWxlY3RvckFyZ3MpID0+IHtcbiAgY29uc3QgeyBtZW1vaXplLCBtZW1vaXplT3B0aW9ucyB9ID0gb3B0aW9ucztcbiAgY29uc3QgeyBpbnB1dFNlbGVjdG9yUmVzdWx0cywgaW5wdXRTZWxlY3RvclJlc3VsdHNDb3B5IH0gPSBpbnB1dFNlbGVjdG9yUmVzdWx0c09iamVjdDtcbiAgY29uc3QgY3JlYXRlQW5FbXB0eU9iamVjdCA9IG1lbW9pemUoKCkgPT4gKHt9KSwgLi4ubWVtb2l6ZU9wdGlvbnMpO1xuICBjb25zdCBhcmVJbnB1dFNlbGVjdG9yUmVzdWx0c0VxdWFsID0gY3JlYXRlQW5FbXB0eU9iamVjdC5hcHBseShudWxsLCBpbnB1dFNlbGVjdG9yUmVzdWx0cykgPT09IGNyZWF0ZUFuRW1wdHlPYmplY3QuYXBwbHkobnVsbCwgaW5wdXRTZWxlY3RvclJlc3VsdHNDb3B5KTtcbiAgaWYgKCFhcmVJbnB1dFNlbGVjdG9yUmVzdWx0c0VxdWFsKSB7XG4gICAgbGV0IHN0YWNrID0gdm9pZCAwO1xuICAgIHRyeSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoKTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICA7XG4gICAgICAoeyBzdGFjayB9ID0gZSk7XG4gICAgfVxuICAgIGNvbnNvbGUud2FybihcbiAgICAgIFwiQW4gaW5wdXQgc2VsZWN0b3IgcmV0dXJuZWQgYSBkaWZmZXJlbnQgcmVzdWx0IHdoZW4gcGFzc2VkIHNhbWUgYXJndW1lbnRzLlxcblRoaXMgbWVhbnMgeW91ciBvdXRwdXQgc2VsZWN0b3Igd2lsbCBsaWtlbHkgcnVuIG1vcmUgZnJlcXVlbnRseSB0aGFuIGludGVuZGVkLlxcbkF2b2lkIHJldHVybmluZyBhIG5ldyByZWZlcmVuY2UgaW5zaWRlIHlvdXIgaW5wdXQgc2VsZWN0b3IsIGUuZy5cXG5gY3JlYXRlU2VsZWN0b3IoW3N0YXRlID0+IHN0YXRlLnRvZG9zLm1hcCh0b2RvID0+IHRvZG8uaWQpXSwgdG9kb0lkcyA9PiB0b2RvSWRzLmxlbmd0aClgXCIsXG4gICAgICB7XG4gICAgICAgIGFyZ3VtZW50czogaW5wdXRTZWxlY3RvckFyZ3MsXG4gICAgICAgIGZpcnN0SW5wdXRzOiBpbnB1dFNlbGVjdG9yUmVzdWx0cyxcbiAgICAgICAgc2Vjb25kSW5wdXRzOiBpbnB1dFNlbGVjdG9yUmVzdWx0c0NvcHksXG4gICAgICAgIHN0YWNrXG4gICAgICB9XG4gICAgKTtcbiAgfVxufTtcblxuLy8gc3JjL2Rldk1vZGVDaGVja3Mvc2V0R2xvYmFsRGV2TW9kZUNoZWNrcy50c1xudmFyIGdsb2JhbERldk1vZGVDaGVja3MgPSB7XG4gIGlucHV0U3RhYmlsaXR5Q2hlY2s6IFwib25jZVwiLFxuICBpZGVudGl0eUZ1bmN0aW9uQ2hlY2s6IFwib25jZVwiXG59O1xudmFyIHNldEdsb2JhbERldk1vZGVDaGVja3MgPSAoZGV2TW9kZUNoZWNrcykgPT4ge1xuICBPYmplY3QuYXNzaWduKGdsb2JhbERldk1vZGVDaGVja3MsIGRldk1vZGVDaGVja3MpO1xufTtcblxuLy8gc3JjL3V0aWxzLnRzXG52YXIgTk9UX0ZPVU5EID0gXCJOT1RfRk9VTkRcIjtcbmZ1bmN0aW9uIGFzc2VydElzRnVuY3Rpb24oZnVuYywgZXJyb3JNZXNzYWdlID0gYGV4cGVjdGVkIGEgZnVuY3Rpb24sIGluc3RlYWQgcmVjZWl2ZWQgJHt0eXBlb2YgZnVuY31gKSB7XG4gIGlmICh0eXBlb2YgZnVuYyAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihlcnJvck1lc3NhZ2UpO1xuICB9XG59XG5mdW5jdGlvbiBhc3NlcnRJc09iamVjdChvYmplY3QsIGVycm9yTWVzc2FnZSA9IGBleHBlY3RlZCBhbiBvYmplY3QsIGluc3RlYWQgcmVjZWl2ZWQgJHt0eXBlb2Ygb2JqZWN0fWApIHtcbiAgaWYgKHR5cGVvZiBvYmplY3QgIT09IFwib2JqZWN0XCIpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGVycm9yTWVzc2FnZSk7XG4gIH1cbn1cbmZ1bmN0aW9uIGFzc2VydElzQXJyYXlPZkZ1bmN0aW9ucyhhcnJheSwgZXJyb3JNZXNzYWdlID0gYGV4cGVjdGVkIGFsbCBpdGVtcyB0byBiZSBmdW5jdGlvbnMsIGluc3RlYWQgcmVjZWl2ZWQgdGhlIGZvbGxvd2luZyB0eXBlczogYCkge1xuICBpZiAoIWFycmF5LmV2ZXJ5KChpdGVtKSA9PiB0eXBlb2YgaXRlbSA9PT0gXCJmdW5jdGlvblwiKSkge1xuICAgIGNvbnN0IGl0ZW1UeXBlcyA9IGFycmF5Lm1hcChcbiAgICAgIChpdGVtKSA9PiB0eXBlb2YgaXRlbSA9PT0gXCJmdW5jdGlvblwiID8gYGZ1bmN0aW9uICR7aXRlbS5uYW1lIHx8IFwidW5uYW1lZFwifSgpYCA6IHR5cGVvZiBpdGVtXG4gICAgKS5qb2luKFwiLCBcIik7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgJHtlcnJvck1lc3NhZ2V9WyR7aXRlbVR5cGVzfV1gKTtcbiAgfVxufVxudmFyIGVuc3VyZUlzQXJyYXkgPSAoaXRlbSkgPT4ge1xuICByZXR1cm4gQXJyYXkuaXNBcnJheShpdGVtKSA/IGl0ZW0gOiBbaXRlbV07XG59O1xuZnVuY3Rpb24gZ2V0RGVwZW5kZW5jaWVzKGNyZWF0ZVNlbGVjdG9yQXJncykge1xuICBjb25zdCBkZXBlbmRlbmNpZXMgPSBBcnJheS5pc0FycmF5KGNyZWF0ZVNlbGVjdG9yQXJnc1swXSkgPyBjcmVhdGVTZWxlY3RvckFyZ3NbMF0gOiBjcmVhdGVTZWxlY3RvckFyZ3M7XG4gIGFzc2VydElzQXJyYXlPZkZ1bmN0aW9ucyhcbiAgICBkZXBlbmRlbmNpZXMsXG4gICAgYGNyZWF0ZVNlbGVjdG9yIGV4cGVjdHMgYWxsIGlucHV0LXNlbGVjdG9ycyB0byBiZSBmdW5jdGlvbnMsIGJ1dCByZWNlaXZlZCB0aGUgZm9sbG93aW5nIHR5cGVzOiBgXG4gICk7XG4gIHJldHVybiBkZXBlbmRlbmNpZXM7XG59XG5mdW5jdGlvbiBjb2xsZWN0SW5wdXRTZWxlY3RvclJlc3VsdHMoZGVwZW5kZW5jaWVzLCBpbnB1dFNlbGVjdG9yQXJncykge1xuICBjb25zdCBpbnB1dFNlbGVjdG9yUmVzdWx0cyA9IFtdO1xuICBjb25zdCB7IGxlbmd0aCB9ID0gZGVwZW5kZW5jaWVzO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgaW5wdXRTZWxlY3RvclJlc3VsdHMucHVzaChkZXBlbmRlbmNpZXNbaV0uYXBwbHkobnVsbCwgaW5wdXRTZWxlY3RvckFyZ3MpKTtcbiAgfVxuICByZXR1cm4gaW5wdXRTZWxlY3RvclJlc3VsdHM7XG59XG52YXIgZ2V0RGV2TW9kZUNoZWNrc0V4ZWN1dGlvbkluZm8gPSAoZmlyc3RSdW4sIGRldk1vZGVDaGVja3MpID0+IHtcbiAgY29uc3QgeyBpZGVudGl0eUZ1bmN0aW9uQ2hlY2ssIGlucHV0U3RhYmlsaXR5Q2hlY2sgfSA9IHtcbiAgICAuLi5nbG9iYWxEZXZNb2RlQ2hlY2tzLFxuICAgIC4uLmRldk1vZGVDaGVja3NcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBpZGVudGl0eUZ1bmN0aW9uQ2hlY2s6IHtcbiAgICAgIHNob3VsZFJ1bjogaWRlbnRpdHlGdW5jdGlvbkNoZWNrID09PSBcImFsd2F5c1wiIHx8IGlkZW50aXR5RnVuY3Rpb25DaGVjayA9PT0gXCJvbmNlXCIgJiYgZmlyc3RSdW4sXG4gICAgICBydW46IHJ1bklkZW50aXR5RnVuY3Rpb25DaGVja1xuICAgIH0sXG4gICAgaW5wdXRTdGFiaWxpdHlDaGVjazoge1xuICAgICAgc2hvdWxkUnVuOiBpbnB1dFN0YWJpbGl0eUNoZWNrID09PSBcImFsd2F5c1wiIHx8IGlucHV0U3RhYmlsaXR5Q2hlY2sgPT09IFwib25jZVwiICYmIGZpcnN0UnVuLFxuICAgICAgcnVuOiBydW5JbnB1dFN0YWJpbGl0eUNoZWNrXG4gICAgfVxuICB9O1xufTtcblxuLy8gc3JjL2F1dG90cmFja01lbW9pemUvYXV0b3RyYWNraW5nLnRzXG52YXIgJFJFVklTSU9OID0gMDtcbnZhciBDVVJSRU5UX1RSQUNLRVIgPSBudWxsO1xudmFyIENlbGwgPSBjbGFzcyB7XG4gIHJldmlzaW9uID0gJFJFVklTSU9OO1xuICBfdmFsdWU7XG4gIF9sYXN0VmFsdWU7XG4gIF9pc0VxdWFsID0gdHJpcGxlRXE7XG4gIGNvbnN0cnVjdG9yKGluaXRpYWxWYWx1ZSwgaXNFcXVhbCA9IHRyaXBsZUVxKSB7XG4gICAgdGhpcy5fdmFsdWUgPSB0aGlzLl9sYXN0VmFsdWUgPSBpbml0aWFsVmFsdWU7XG4gICAgdGhpcy5faXNFcXVhbCA9IGlzRXF1YWw7XG4gIH1cbiAgLy8gV2hlbmV2ZXIgYSBzdG9yYWdlIHZhbHVlIGlzIHJlYWQsIGl0J2xsIGFkZCBpdHNlbGYgdG8gdGhlIGN1cnJlbnQgdHJhY2tlciBpZlxuICAvLyBvbmUgZXhpc3RzLCBlbnRhbmdsaW5nIGl0cyBzdGF0ZSB3aXRoIHRoYXQgY2FjaGUuXG4gIGdldCB2YWx1ZSgpIHtcbiAgICBDVVJSRU5UX1RSQUNLRVI/LmFkZCh0aGlzKTtcbiAgICByZXR1cm4gdGhpcy5fdmFsdWU7XG4gIH1cbiAgLy8gV2hlbmV2ZXIgYSBzdG9yYWdlIHZhbHVlIGlzIHVwZGF0ZWQsIHdlIGJ1bXAgdGhlIGdsb2JhbCByZXZpc2lvbiBjbG9jayxcbiAgLy8gYXNzaWduIHRoZSByZXZpc2lvbiBmb3IgdGhpcyBzdG9yYWdlIHRvIHRoZSBuZXcgdmFsdWUsIF9hbmRfIHdlIHNjaGVkdWxlIGFcbiAgLy8gcmVyZW5kZXIuIFRoaXMgaXMgaW1wb3J0YW50LCBhbmQgaXQncyB3aGF0IG1ha2VzIGF1dG90cmFja2luZyAgX3B1bGxfXG4gIC8vIGJhc2VkLiBXZSBkb24ndCBhY3RpdmVseSB0ZWxsIHRoZSBjYWNoZXMgd2hpY2ggZGVwZW5kIG9uIHRoZSBzdG9yYWdlIHRoYXRcbiAgLy8gYW55dGhpbmcgaGFzIGhhcHBlbmVkLiBJbnN0ZWFkLCB3ZSByZWNvbXB1dGUgdGhlIGNhY2hlcyB3aGVuIG5lZWRlZC5cbiAgc2V0IHZhbHVlKG5ld1ZhbHVlKSB7XG4gICAgaWYgKHRoaXMudmFsdWUgPT09IG5ld1ZhbHVlKVxuICAgICAgcmV0dXJuO1xuICAgIHRoaXMuX3ZhbHVlID0gbmV3VmFsdWU7XG4gICAgdGhpcy5yZXZpc2lvbiA9ICsrJFJFVklTSU9OO1xuICB9XG59O1xuZnVuY3Rpb24gdHJpcGxlRXEoYSwgYikge1xuICByZXR1cm4gYSA9PT0gYjtcbn1cbnZhciBUcmFja2luZ0NhY2hlID0gY2xhc3Mge1xuICBfY2FjaGVkVmFsdWU7XG4gIF9jYWNoZWRSZXZpc2lvbiA9IC0xO1xuICBfZGVwcyA9IFtdO1xuICBoaXRzID0gMDtcbiAgZm47XG4gIGNvbnN0cnVjdG9yKGZuKSB7XG4gICAgdGhpcy5mbiA9IGZuO1xuICB9XG4gIGNsZWFyKCkge1xuICAgIHRoaXMuX2NhY2hlZFZhbHVlID0gdm9pZCAwO1xuICAgIHRoaXMuX2NhY2hlZFJldmlzaW9uID0gLTE7XG4gICAgdGhpcy5fZGVwcyA9IFtdO1xuICAgIHRoaXMuaGl0cyA9IDA7XG4gIH1cbiAgZ2V0IHZhbHVlKCkge1xuICAgIGlmICh0aGlzLnJldmlzaW9uID4gdGhpcy5fY2FjaGVkUmV2aXNpb24pIHtcbiAgICAgIGNvbnN0IHsgZm4gfSA9IHRoaXM7XG4gICAgICBjb25zdCBjdXJyZW50VHJhY2tlciA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gICAgICBjb25zdCBwcmV2VHJhY2tlciA9IENVUlJFTlRfVFJBQ0tFUjtcbiAgICAgIENVUlJFTlRfVFJBQ0tFUiA9IGN1cnJlbnRUcmFja2VyO1xuICAgICAgdGhpcy5fY2FjaGVkVmFsdWUgPSBmbigpO1xuICAgICAgQ1VSUkVOVF9UUkFDS0VSID0gcHJldlRyYWNrZXI7XG4gICAgICB0aGlzLmhpdHMrKztcbiAgICAgIHRoaXMuX2RlcHMgPSBBcnJheS5mcm9tKGN1cnJlbnRUcmFja2VyKTtcbiAgICAgIHRoaXMuX2NhY2hlZFJldmlzaW9uID0gdGhpcy5yZXZpc2lvbjtcbiAgICB9XG4gICAgQ1VSUkVOVF9UUkFDS0VSPy5hZGQodGhpcyk7XG4gICAgcmV0dXJuIHRoaXMuX2NhY2hlZFZhbHVlO1xuICB9XG4gIGdldCByZXZpc2lvbigpIHtcbiAgICByZXR1cm4gTWF0aC5tYXgoLi4udGhpcy5fZGVwcy5tYXAoKGQpID0+IGQucmV2aXNpb24pLCAwKTtcbiAgfVxufTtcbmZ1bmN0aW9uIGdldFZhbHVlKGNlbGwpIHtcbiAgaWYgKCEoY2VsbCBpbnN0YW5jZW9mIENlbGwpKSB7XG4gICAgY29uc29sZS53YXJuKFwiTm90IGEgdmFsaWQgY2VsbCEgXCIsIGNlbGwpO1xuICB9XG4gIHJldHVybiBjZWxsLnZhbHVlO1xufVxuZnVuY3Rpb24gc2V0VmFsdWUoc3RvcmFnZSwgdmFsdWUpIHtcbiAgaWYgKCEoc3RvcmFnZSBpbnN0YW5jZW9mIENlbGwpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIFwic2V0VmFsdWUgbXVzdCBiZSBwYXNzZWQgYSB0cmFja2VkIHN0b3JlIGNyZWF0ZWQgd2l0aCBgY3JlYXRlU3RvcmFnZWAuXCJcbiAgICApO1xuICB9XG4gIHN0b3JhZ2UudmFsdWUgPSBzdG9yYWdlLl9sYXN0VmFsdWUgPSB2YWx1ZTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUNlbGwoaW5pdGlhbFZhbHVlLCBpc0VxdWFsID0gdHJpcGxlRXEpIHtcbiAgcmV0dXJuIG5ldyBDZWxsKGluaXRpYWxWYWx1ZSwgaXNFcXVhbCk7XG59XG5mdW5jdGlvbiBjcmVhdGVDYWNoZShmbikge1xuICBhc3NlcnRJc0Z1bmN0aW9uKFxuICAgIGZuLFxuICAgIFwidGhlIGZpcnN0IHBhcmFtZXRlciB0byBgY3JlYXRlQ2FjaGVgIG11c3QgYmUgYSBmdW5jdGlvblwiXG4gICk7XG4gIHJldHVybiBuZXcgVHJhY2tpbmdDYWNoZShmbik7XG59XG5cbi8vIHNyYy9hdXRvdHJhY2tNZW1vaXplL3RyYWNraW5nLnRzXG52YXIgbmV2ZXJFcSA9IChhLCBiKSA9PiBmYWxzZTtcbmZ1bmN0aW9uIGNyZWF0ZVRhZygpIHtcbiAgcmV0dXJuIGNyZWF0ZUNlbGwobnVsbCwgbmV2ZXJFcSk7XG59XG5mdW5jdGlvbiBkaXJ0eVRhZyh0YWcsIHZhbHVlKSB7XG4gIHNldFZhbHVlKHRhZywgdmFsdWUpO1xufVxudmFyIGNvbnN1bWVDb2xsZWN0aW9uID0gKG5vZGUpID0+IHtcbiAgbGV0IHRhZyA9IG5vZGUuY29sbGVjdGlvblRhZztcbiAgaWYgKHRhZyA9PT0gbnVsbCkge1xuICAgIHRhZyA9IG5vZGUuY29sbGVjdGlvblRhZyA9IGNyZWF0ZVRhZygpO1xuICB9XG4gIGdldFZhbHVlKHRhZyk7XG59O1xudmFyIGRpcnR5Q29sbGVjdGlvbiA9IChub2RlKSA9PiB7XG4gIGNvbnN0IHRhZyA9IG5vZGUuY29sbGVjdGlvblRhZztcbiAgaWYgKHRhZyAhPT0gbnVsbCkge1xuICAgIGRpcnR5VGFnKHRhZywgbnVsbCk7XG4gIH1cbn07XG5cbi8vIHNyYy9hdXRvdHJhY2tNZW1vaXplL3Byb3h5LnRzXG52YXIgUkVEVVhfUFJPWFlfTEFCRUwgPSBTeW1ib2woKTtcbnZhciBuZXh0SWQgPSAwO1xudmFyIHByb3RvID0gT2JqZWN0LmdldFByb3RvdHlwZU9mKHt9KTtcbnZhciBPYmplY3RUcmVlTm9kZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IodmFsdWUpIHtcbiAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gICAgdGhpcy52YWx1ZSA9IHZhbHVlO1xuICAgIHRoaXMudGFnLnZhbHVlID0gdmFsdWU7XG4gIH1cbiAgcHJveHkgPSBuZXcgUHJveHkodGhpcywgb2JqZWN0UHJveHlIYW5kbGVyKTtcbiAgdGFnID0gY3JlYXRlVGFnKCk7XG4gIHRhZ3MgPSB7fTtcbiAgY2hpbGRyZW4gPSB7fTtcbiAgY29sbGVjdGlvblRhZyA9IG51bGw7XG4gIGlkID0gbmV4dElkKys7XG59O1xudmFyIG9iamVjdFByb3h5SGFuZGxlciA9IHtcbiAgZ2V0KG5vZGUsIGtleSkge1xuICAgIGZ1bmN0aW9uIGNhbGN1bGF0ZVJlc3VsdCgpIHtcbiAgICAgIGNvbnN0IHsgdmFsdWUgfSA9IG5vZGU7XG4gICAgICBjb25zdCBjaGlsZFZhbHVlID0gUmVmbGVjdC5nZXQodmFsdWUsIGtleSk7XG4gICAgICBpZiAodHlwZW9mIGtleSA9PT0gXCJzeW1ib2xcIikge1xuICAgICAgICByZXR1cm4gY2hpbGRWYWx1ZTtcbiAgICAgIH1cbiAgICAgIGlmIChrZXkgaW4gcHJvdG8pIHtcbiAgICAgICAgcmV0dXJuIGNoaWxkVmFsdWU7XG4gICAgICB9XG4gICAgICBpZiAodHlwZW9mIGNoaWxkVmFsdWUgPT09IFwib2JqZWN0XCIgJiYgY2hpbGRWYWx1ZSAhPT0gbnVsbCkge1xuICAgICAgICBsZXQgY2hpbGROb2RlID0gbm9kZS5jaGlsZHJlbltrZXldO1xuICAgICAgICBpZiAoY2hpbGROb2RlID09PSB2b2lkIDApIHtcbiAgICAgICAgICBjaGlsZE5vZGUgPSBub2RlLmNoaWxkcmVuW2tleV0gPSBjcmVhdGVOb2RlKGNoaWxkVmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjaGlsZE5vZGUudGFnKSB7XG4gICAgICAgICAgZ2V0VmFsdWUoY2hpbGROb2RlLnRhZyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNoaWxkTm9kZS5wcm94eTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGxldCB0YWcgPSBub2RlLnRhZ3Nba2V5XTtcbiAgICAgICAgaWYgKHRhZyA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgdGFnID0gbm9kZS50YWdzW2tleV0gPSBjcmVhdGVUYWcoKTtcbiAgICAgICAgICB0YWcudmFsdWUgPSBjaGlsZFZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIGdldFZhbHVlKHRhZyk7XG4gICAgICAgIHJldHVybiBjaGlsZFZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgICBjb25zdCByZXMgPSBjYWxjdWxhdGVSZXN1bHQoKTtcbiAgICByZXR1cm4gcmVzO1xuICB9LFxuICBvd25LZXlzKG5vZGUpIHtcbiAgICBjb25zdW1lQ29sbGVjdGlvbihub2RlKTtcbiAgICByZXR1cm4gUmVmbGVjdC5vd25LZXlzKG5vZGUudmFsdWUpO1xuICB9LFxuICBnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iobm9kZSwgcHJvcCkge1xuICAgIHJldHVybiBSZWZsZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihub2RlLnZhbHVlLCBwcm9wKTtcbiAgfSxcbiAgaGFzKG5vZGUsIHByb3ApIHtcbiAgICByZXR1cm4gUmVmbGVjdC5oYXMobm9kZS52YWx1ZSwgcHJvcCk7XG4gIH1cbn07XG52YXIgQXJyYXlUcmVlTm9kZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IodmFsdWUpIHtcbiAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gICAgdGhpcy52YWx1ZSA9IHZhbHVlO1xuICAgIHRoaXMudGFnLnZhbHVlID0gdmFsdWU7XG4gIH1cbiAgcHJveHkgPSBuZXcgUHJveHkoW3RoaXNdLCBhcnJheVByb3h5SGFuZGxlcik7XG4gIHRhZyA9IGNyZWF0ZVRhZygpO1xuICB0YWdzID0ge307XG4gIGNoaWxkcmVuID0ge307XG4gIGNvbGxlY3Rpb25UYWcgPSBudWxsO1xuICBpZCA9IG5leHRJZCsrO1xufTtcbnZhciBhcnJheVByb3h5SGFuZGxlciA9IHtcbiAgZ2V0KFtub2RlXSwga2V5KSB7XG4gICAgaWYgKGtleSA9PT0gXCJsZW5ndGhcIikge1xuICAgICAgY29uc3VtZUNvbGxlY3Rpb24obm9kZSk7XG4gICAgfVxuICAgIHJldHVybiBvYmplY3RQcm94eUhhbmRsZXIuZ2V0KG5vZGUsIGtleSk7XG4gIH0sXG4gIG93bktleXMoW25vZGVdKSB7XG4gICAgcmV0dXJuIG9iamVjdFByb3h5SGFuZGxlci5vd25LZXlzKG5vZGUpO1xuICB9LFxuICBnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoW25vZGVdLCBwcm9wKSB7XG4gICAgcmV0dXJuIG9iamVjdFByb3h5SGFuZGxlci5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iobm9kZSwgcHJvcCk7XG4gIH0sXG4gIGhhcyhbbm9kZV0sIHByb3ApIHtcbiAgICByZXR1cm4gb2JqZWN0UHJveHlIYW5kbGVyLmhhcyhub2RlLCBwcm9wKTtcbiAgfVxufTtcbmZ1bmN0aW9uIGNyZWF0ZU5vZGUodmFsdWUpIHtcbiAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgcmV0dXJuIG5ldyBBcnJheVRyZWVOb2RlKHZhbHVlKTtcbiAgfVxuICByZXR1cm4gbmV3IE9iamVjdFRyZWVOb2RlKHZhbHVlKTtcbn1cbmZ1bmN0aW9uIHVwZGF0ZU5vZGUobm9kZSwgbmV3VmFsdWUpIHtcbiAgY29uc3QgeyB2YWx1ZSwgdGFncywgY2hpbGRyZW4gfSA9IG5vZGU7XG4gIG5vZGUudmFsdWUgPSBuZXdWYWx1ZTtcbiAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpICYmIEFycmF5LmlzQXJyYXkobmV3VmFsdWUpICYmIHZhbHVlLmxlbmd0aCAhPT0gbmV3VmFsdWUubGVuZ3RoKSB7XG4gICAgZGlydHlDb2xsZWN0aW9uKG5vZGUpO1xuICB9IGVsc2Uge1xuICAgIGlmICh2YWx1ZSAhPT0gbmV3VmFsdWUpIHtcbiAgICAgIGxldCBvbGRLZXlzU2l6ZSA9IDA7XG4gICAgICBsZXQgbmV3S2V5c1NpemUgPSAwO1xuICAgICAgbGV0IGFueUtleXNBZGRlZCA9IGZhbHNlO1xuICAgICAgZm9yIChjb25zdCBfa2V5IGluIHZhbHVlKSB7XG4gICAgICAgIG9sZEtleXNTaXplKys7XG4gICAgICB9XG4gICAgICBmb3IgKGNvbnN0IGtleSBpbiBuZXdWYWx1ZSkge1xuICAgICAgICBuZXdLZXlzU2l6ZSsrO1xuICAgICAgICBpZiAoIShrZXkgaW4gdmFsdWUpKSB7XG4gICAgICAgICAgYW55S2V5c0FkZGVkID0gdHJ1ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgY29uc3QgaXNEaWZmZXJlbnQgPSBhbnlLZXlzQWRkZWQgfHwgb2xkS2V5c1NpemUgIT09IG5ld0tleXNTaXplO1xuICAgICAgaWYgKGlzRGlmZmVyZW50KSB7XG4gICAgICAgIGRpcnR5Q29sbGVjdGlvbihub2RlKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgZm9yIChjb25zdCBrZXkgaW4gdGFncykge1xuICAgIGNvbnN0IGNoaWxkVmFsdWUgPSB2YWx1ZVtrZXldO1xuICAgIGNvbnN0IG5ld0NoaWxkVmFsdWUgPSBuZXdWYWx1ZVtrZXldO1xuICAgIGlmIChjaGlsZFZhbHVlICE9PSBuZXdDaGlsZFZhbHVlKSB7XG4gICAgICBkaXJ0eUNvbGxlY3Rpb24obm9kZSk7XG4gICAgICBkaXJ0eVRhZyh0YWdzW2tleV0sIG5ld0NoaWxkVmFsdWUpO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIG5ld0NoaWxkVmFsdWUgPT09IFwib2JqZWN0XCIgJiYgbmV3Q2hpbGRWYWx1ZSAhPT0gbnVsbCkge1xuICAgICAgZGVsZXRlIHRhZ3Nba2V5XTtcbiAgICB9XG4gIH1cbiAgZm9yIChjb25zdCBrZXkgaW4gY2hpbGRyZW4pIHtcbiAgICBjb25zdCBjaGlsZE5vZGUgPSBjaGlsZHJlbltrZXldO1xuICAgIGNvbnN0IG5ld0NoaWxkVmFsdWUgPSBuZXdWYWx1ZVtrZXldO1xuICAgIGNvbnN0IGNoaWxkVmFsdWUgPSBjaGlsZE5vZGUudmFsdWU7XG4gICAgaWYgKGNoaWxkVmFsdWUgPT09IG5ld0NoaWxkVmFsdWUpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH0gZWxzZSBpZiAodHlwZW9mIG5ld0NoaWxkVmFsdWUgPT09IFwib2JqZWN0XCIgJiYgbmV3Q2hpbGRWYWx1ZSAhPT0gbnVsbCkge1xuICAgICAgdXBkYXRlTm9kZShjaGlsZE5vZGUsIG5ld0NoaWxkVmFsdWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICBkZWxldGVOb2RlKGNoaWxkTm9kZSk7XG4gICAgICBkZWxldGUgY2hpbGRyZW5ba2V5XTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIGRlbGV0ZU5vZGUobm9kZSkge1xuICBpZiAobm9kZS50YWcpIHtcbiAgICBkaXJ0eVRhZyhub2RlLnRhZywgbnVsbCk7XG4gIH1cbiAgZGlydHlDb2xsZWN0aW9uKG5vZGUpO1xuICBmb3IgKGNvbnN0IGtleSBpbiBub2RlLnRhZ3MpIHtcbiAgICBkaXJ0eVRhZyhub2RlLnRhZ3Nba2V5XSwgbnVsbCk7XG4gIH1cbiAgZm9yIChjb25zdCBrZXkgaW4gbm9kZS5jaGlsZHJlbikge1xuICAgIGRlbGV0ZU5vZGUobm9kZS5jaGlsZHJlbltrZXldKTtcbiAgfVxufVxuXG4vLyBzcmMvbHJ1TWVtb2l6ZS50c1xuZnVuY3Rpb24gY3JlYXRlU2luZ2xldG9uQ2FjaGUoZXF1YWxzKSB7XG4gIGxldCBlbnRyeTtcbiAgcmV0dXJuIHtcbiAgICBnZXQoa2V5KSB7XG4gICAgICBpZiAoZW50cnkgJiYgZXF1YWxzKGVudHJ5LmtleSwga2V5KSkge1xuICAgICAgICByZXR1cm4gZW50cnkudmFsdWU7XG4gICAgICB9XG4gICAgICByZXR1cm4gTk9UX0ZPVU5EO1xuICAgIH0sXG4gICAgcHV0KGtleSwgdmFsdWUpIHtcbiAgICAgIGVudHJ5ID0geyBrZXksIHZhbHVlIH07XG4gICAgfSxcbiAgICBnZXRFbnRyaWVzKCkge1xuICAgICAgcmV0dXJuIGVudHJ5ID8gW2VudHJ5XSA6IFtdO1xuICAgIH0sXG4gICAgY2xlYXIoKSB7XG4gICAgICBlbnRyeSA9IHZvaWQgMDtcbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiBjcmVhdGVMcnVDYWNoZShtYXhTaXplLCBlcXVhbHMpIHtcbiAgbGV0IGVudHJpZXMgPSBbXTtcbiAgZnVuY3Rpb24gZ2V0KGtleSkge1xuICAgIGNvbnN0IGNhY2hlSW5kZXggPSBlbnRyaWVzLmZpbmRJbmRleCgoZW50cnkpID0+IGVxdWFscyhrZXksIGVudHJ5LmtleSkpO1xuICAgIGlmIChjYWNoZUluZGV4ID4gLTEpIHtcbiAgICAgIGNvbnN0IGVudHJ5ID0gZW50cmllc1tjYWNoZUluZGV4XTtcbiAgICAgIGlmIChjYWNoZUluZGV4ID4gMCkge1xuICAgICAgICBlbnRyaWVzLnNwbGljZShjYWNoZUluZGV4LCAxKTtcbiAgICAgICAgZW50cmllcy51bnNoaWZ0KGVudHJ5KTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBlbnRyeS52YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIE5PVF9GT1VORDtcbiAgfVxuICBmdW5jdGlvbiBwdXQoa2V5LCB2YWx1ZSkge1xuICAgIGlmIChnZXQoa2V5KSA9PT0gTk9UX0ZPVU5EKSB7XG4gICAgICBlbnRyaWVzLnVuc2hpZnQoeyBrZXksIHZhbHVlIH0pO1xuICAgICAgaWYgKGVudHJpZXMubGVuZ3RoID4gbWF4U2l6ZSkge1xuICAgICAgICBlbnRyaWVzLnBvcCgpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBnZXRFbnRyaWVzKCkge1xuICAgIHJldHVybiBlbnRyaWVzO1xuICB9XG4gIGZ1bmN0aW9uIGNsZWFyKCkge1xuICAgIGVudHJpZXMgPSBbXTtcbiAgfVxuICByZXR1cm4geyBnZXQsIHB1dCwgZ2V0RW50cmllcywgY2xlYXIgfTtcbn1cbnZhciByZWZlcmVuY2VFcXVhbGl0eUNoZWNrID0gKGEsIGIpID0+IGEgPT09IGI7XG5mdW5jdGlvbiBjcmVhdGVDYWNoZUtleUNvbXBhcmF0b3IoZXF1YWxpdHlDaGVjaykge1xuICByZXR1cm4gZnVuY3Rpb24gYXJlQXJndW1lbnRzU2hhbGxvd2x5RXF1YWwocHJldiwgbmV4dCkge1xuICAgIGlmIChwcmV2ID09PSBudWxsIHx8IG5leHQgPT09IG51bGwgfHwgcHJldi5sZW5ndGggIT09IG5leHQubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IHsgbGVuZ3RoIH0gPSBwcmV2O1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmICghZXF1YWxpdHlDaGVjayhwcmV2W2ldLCBuZXh0W2ldKSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9O1xufVxuZnVuY3Rpb24gbHJ1TWVtb2l6ZShmdW5jLCBlcXVhbGl0eUNoZWNrT3JPcHRpb25zKSB7XG4gIGNvbnN0IHByb3ZpZGVkT3B0aW9ucyA9IHR5cGVvZiBlcXVhbGl0eUNoZWNrT3JPcHRpb25zID09PSBcIm9iamVjdFwiID8gZXF1YWxpdHlDaGVja09yT3B0aW9ucyA6IHsgZXF1YWxpdHlDaGVjazogZXF1YWxpdHlDaGVja09yT3B0aW9ucyB9O1xuICBjb25zdCB7XG4gICAgZXF1YWxpdHlDaGVjayA9IHJlZmVyZW5jZUVxdWFsaXR5Q2hlY2ssXG4gICAgbWF4U2l6ZSA9IDEsXG4gICAgcmVzdWx0RXF1YWxpdHlDaGVja1xuICB9ID0gcHJvdmlkZWRPcHRpb25zO1xuICBjb25zdCBjb21wYXJhdG9yID0gY3JlYXRlQ2FjaGVLZXlDb21wYXJhdG9yKGVxdWFsaXR5Q2hlY2spO1xuICBsZXQgcmVzdWx0c0NvdW50ID0gMDtcbiAgY29uc3QgY2FjaGUgPSBtYXhTaXplID09PSAxID8gY3JlYXRlU2luZ2xldG9uQ2FjaGUoY29tcGFyYXRvcikgOiBjcmVhdGVMcnVDYWNoZShtYXhTaXplLCBjb21wYXJhdG9yKTtcbiAgZnVuY3Rpb24gbWVtb2l6ZWQoKSB7XG4gICAgbGV0IHZhbHVlID0gY2FjaGUuZ2V0KGFyZ3VtZW50cyk7XG4gICAgaWYgKHZhbHVlID09PSBOT1RfRk9VTkQpIHtcbiAgICAgIHZhbHVlID0gZnVuYy5hcHBseShudWxsLCBhcmd1bWVudHMpO1xuICAgICAgcmVzdWx0c0NvdW50Kys7XG4gICAgICBpZiAocmVzdWx0RXF1YWxpdHlDaGVjaykge1xuICAgICAgICBjb25zdCBlbnRyaWVzID0gY2FjaGUuZ2V0RW50cmllcygpO1xuICAgICAgICBjb25zdCBtYXRjaGluZ0VudHJ5ID0gZW50cmllcy5maW5kKFxuICAgICAgICAgIChlbnRyeSkgPT4gcmVzdWx0RXF1YWxpdHlDaGVjayhlbnRyeS52YWx1ZSwgdmFsdWUpXG4gICAgICAgICk7XG4gICAgICAgIGlmIChtYXRjaGluZ0VudHJ5KSB7XG4gICAgICAgICAgdmFsdWUgPSBtYXRjaGluZ0VudHJ5LnZhbHVlO1xuICAgICAgICAgIHJlc3VsdHNDb3VudCAhPT0gMCAmJiByZXN1bHRzQ291bnQtLTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgY2FjaGUucHV0KGFyZ3VtZW50cywgdmFsdWUpO1xuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbiAgbWVtb2l6ZWQuY2xlYXJDYWNoZSA9ICgpID0+IHtcbiAgICBjYWNoZS5jbGVhcigpO1xuICAgIG1lbW9pemVkLnJlc2V0UmVzdWx0c0NvdW50KCk7XG4gIH07XG4gIG1lbW9pemVkLnJlc3VsdHNDb3VudCA9ICgpID0+IHJlc3VsdHNDb3VudDtcbiAgbWVtb2l6ZWQucmVzZXRSZXN1bHRzQ291bnQgPSAoKSA9PiB7XG4gICAgcmVzdWx0c0NvdW50ID0gMDtcbiAgfTtcbiAgcmV0dXJuIG1lbW9pemVkO1xufVxuXG4vLyBzcmMvYXV0b3RyYWNrTWVtb2l6ZS9hdXRvdHJhY2tNZW1vaXplLnRzXG5mdW5jdGlvbiBhdXRvdHJhY2tNZW1vaXplKGZ1bmMpIHtcbiAgY29uc3Qgbm9kZSA9IGNyZWF0ZU5vZGUoXG4gICAgW11cbiAgKTtcbiAgbGV0IGxhc3RBcmdzID0gbnVsbDtcbiAgY29uc3Qgc2hhbGxvd0VxdWFsID0gY3JlYXRlQ2FjaGVLZXlDb21wYXJhdG9yKHJlZmVyZW5jZUVxdWFsaXR5Q2hlY2spO1xuICBjb25zdCBjYWNoZSA9IGNyZWF0ZUNhY2hlKCgpID0+IHtcbiAgICBjb25zdCByZXMgPSBmdW5jLmFwcGx5KG51bGwsIG5vZGUucHJveHkpO1xuICAgIHJldHVybiByZXM7XG4gIH0pO1xuICBmdW5jdGlvbiBtZW1vaXplZCgpIHtcbiAgICBpZiAoIXNoYWxsb3dFcXVhbChsYXN0QXJncywgYXJndW1lbnRzKSkge1xuICAgICAgdXBkYXRlTm9kZShub2RlLCBhcmd1bWVudHMpO1xuICAgICAgbGFzdEFyZ3MgPSBhcmd1bWVudHM7XG4gICAgfVxuICAgIHJldHVybiBjYWNoZS52YWx1ZTtcbiAgfVxuICBtZW1vaXplZC5jbGVhckNhY2hlID0gKCkgPT4ge1xuICAgIHJldHVybiBjYWNoZS5jbGVhcigpO1xuICB9O1xuICByZXR1cm4gbWVtb2l6ZWQ7XG59XG5cbi8vIHNyYy93ZWFrTWFwTWVtb2l6ZS50c1xudmFyIFN0cm9uZ1JlZiA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IodmFsdWUpIHtcbiAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gIH1cbiAgZGVyZWYoKSB7XG4gICAgcmV0dXJuIHRoaXMudmFsdWU7XG4gIH1cbn07XG52YXIgUmVmID0gdHlwZW9mIFdlYWtSZWYgIT09IFwidW5kZWZpbmVkXCIgPyBXZWFrUmVmIDogU3Ryb25nUmVmO1xudmFyIFVOVEVSTUlOQVRFRCA9IDA7XG52YXIgVEVSTUlOQVRFRCA9IDE7XG5mdW5jdGlvbiBjcmVhdGVDYWNoZU5vZGUoKSB7XG4gIHJldHVybiB7XG4gICAgczogVU5URVJNSU5BVEVELFxuICAgIHY6IHZvaWQgMCxcbiAgICBvOiBudWxsLFxuICAgIHA6IG51bGxcbiAgfTtcbn1cbmZ1bmN0aW9uIHdlYWtNYXBNZW1vaXplKGZ1bmMsIG9wdGlvbnMgPSB7fSkge1xuICBsZXQgZm5Ob2RlID0gY3JlYXRlQ2FjaGVOb2RlKCk7XG4gIGNvbnN0IHsgcmVzdWx0RXF1YWxpdHlDaGVjayB9ID0gb3B0aW9ucztcbiAgbGV0IGxhc3RSZXN1bHQ7XG4gIGxldCByZXN1bHRzQ291bnQgPSAwO1xuICBmdW5jdGlvbiBtZW1vaXplZCgpIHtcbiAgICBsZXQgY2FjaGVOb2RlID0gZm5Ob2RlO1xuICAgIGNvbnN0IHsgbGVuZ3RoIH0gPSBhcmd1bWVudHM7XG4gICAgZm9yIChsZXQgaSA9IDAsIGwgPSBsZW5ndGg7IGkgPCBsOyBpKyspIHtcbiAgICAgIGNvbnN0IGFyZyA9IGFyZ3VtZW50c1tpXTtcbiAgICAgIGlmICh0eXBlb2YgYXJnID09PSBcImZ1bmN0aW9uXCIgfHwgdHlwZW9mIGFyZyA9PT0gXCJvYmplY3RcIiAmJiBhcmcgIT09IG51bGwpIHtcbiAgICAgICAgbGV0IG9iamVjdENhY2hlID0gY2FjaGVOb2RlLm87XG4gICAgICAgIGlmIChvYmplY3RDYWNoZSA9PT0gbnVsbCkge1xuICAgICAgICAgIGNhY2hlTm9kZS5vID0gb2JqZWN0Q2FjaGUgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBvYmplY3ROb2RlID0gb2JqZWN0Q2FjaGUuZ2V0KGFyZyk7XG4gICAgICAgIGlmIChvYmplY3ROb2RlID09PSB2b2lkIDApIHtcbiAgICAgICAgICBjYWNoZU5vZGUgPSBjcmVhdGVDYWNoZU5vZGUoKTtcbiAgICAgICAgICBvYmplY3RDYWNoZS5zZXQoYXJnLCBjYWNoZU5vZGUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNhY2hlTm9kZSA9IG9iamVjdE5vZGU7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGxldCBwcmltaXRpdmVDYWNoZSA9IGNhY2hlTm9kZS5wO1xuICAgICAgICBpZiAocHJpbWl0aXZlQ2FjaGUgPT09IG51bGwpIHtcbiAgICAgICAgICBjYWNoZU5vZGUucCA9IHByaW1pdGl2ZUNhY2hlID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwcmltaXRpdmVOb2RlID0gcHJpbWl0aXZlQ2FjaGUuZ2V0KGFyZyk7XG4gICAgICAgIGlmIChwcmltaXRpdmVOb2RlID09PSB2b2lkIDApIHtcbiAgICAgICAgICBjYWNoZU5vZGUgPSBjcmVhdGVDYWNoZU5vZGUoKTtcbiAgICAgICAgICBwcmltaXRpdmVDYWNoZS5zZXQoYXJnLCBjYWNoZU5vZGUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNhY2hlTm9kZSA9IHByaW1pdGl2ZU5vZGU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgdGVybWluYXRlZE5vZGUgPSBjYWNoZU5vZGU7XG4gICAgbGV0IHJlc3VsdDtcbiAgICBpZiAoY2FjaGVOb2RlLnMgPT09IFRFUk1JTkFURUQpIHtcbiAgICAgIHJlc3VsdCA9IGNhY2hlTm9kZS52O1xuICAgIH0gZWxzZSB7XG4gICAgICByZXN1bHQgPSBmdW5jLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG4gICAgICByZXN1bHRzQ291bnQrKztcbiAgICB9XG4gICAgdGVybWluYXRlZE5vZGUucyA9IFRFUk1JTkFURUQ7XG4gICAgaWYgKHJlc3VsdEVxdWFsaXR5Q2hlY2spIHtcbiAgICAgIGNvbnN0IGxhc3RSZXN1bHRWYWx1ZSA9IGxhc3RSZXN1bHQ/LmRlcmVmKCkgPz8gbGFzdFJlc3VsdDtcbiAgICAgIGlmIChsYXN0UmVzdWx0VmFsdWUgIT0gbnVsbCAmJiByZXN1bHRFcXVhbGl0eUNoZWNrKGxhc3RSZXN1bHRWYWx1ZSwgcmVzdWx0KSkge1xuICAgICAgICByZXN1bHQgPSBsYXN0UmVzdWx0VmFsdWU7XG4gICAgICAgIHJlc3VsdHNDb3VudCAhPT0gMCAmJiByZXN1bHRzQ291bnQtLTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IG5lZWRzV2Vha1JlZiA9IHR5cGVvZiByZXN1bHQgPT09IFwib2JqZWN0XCIgJiYgcmVzdWx0ICE9PSBudWxsIHx8IHR5cGVvZiByZXN1bHQgPT09IFwiZnVuY3Rpb25cIjtcbiAgICAgIGxhc3RSZXN1bHQgPSBuZWVkc1dlYWtSZWYgPyBuZXcgUmVmKHJlc3VsdCkgOiByZXN1bHQ7XG4gICAgfVxuICAgIHRlcm1pbmF0ZWROb2RlLnYgPSByZXN1bHQ7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBtZW1vaXplZC5jbGVhckNhY2hlID0gKCkgPT4ge1xuICAgIGZuTm9kZSA9IGNyZWF0ZUNhY2hlTm9kZSgpO1xuICAgIG1lbW9pemVkLnJlc2V0UmVzdWx0c0NvdW50KCk7XG4gIH07XG4gIG1lbW9pemVkLnJlc3VsdHNDb3VudCA9ICgpID0+IHJlc3VsdHNDb3VudDtcbiAgbWVtb2l6ZWQucmVzZXRSZXN1bHRzQ291bnQgPSAoKSA9PiB7XG4gICAgcmVzdWx0c0NvdW50ID0gMDtcbiAgfTtcbiAgcmV0dXJuIG1lbW9pemVkO1xufVxuXG4vLyBzcmMvY3JlYXRlU2VsZWN0b3JDcmVhdG9yLnRzXG5mdW5jdGlvbiBjcmVhdGVTZWxlY3RvckNyZWF0b3IobWVtb2l6ZU9yT3B0aW9ucywgLi4ubWVtb2l6ZU9wdGlvbnNGcm9tQXJncykge1xuICBjb25zdCBjcmVhdGVTZWxlY3RvckNyZWF0b3JPcHRpb25zID0gdHlwZW9mIG1lbW9pemVPck9wdGlvbnMgPT09IFwiZnVuY3Rpb25cIiA/IHtcbiAgICBtZW1vaXplOiBtZW1vaXplT3JPcHRpb25zLFxuICAgIG1lbW9pemVPcHRpb25zOiBtZW1vaXplT3B0aW9uc0Zyb21BcmdzXG4gIH0gOiBtZW1vaXplT3JPcHRpb25zO1xuICBjb25zdCBjcmVhdGVTZWxlY3RvcjIgPSAoLi4uY3JlYXRlU2VsZWN0b3JBcmdzKSA9PiB7XG4gICAgbGV0IHJlY29tcHV0YXRpb25zID0gMDtcbiAgICBsZXQgZGVwZW5kZW5jeVJlY29tcHV0YXRpb25zID0gMDtcbiAgICBsZXQgbGFzdFJlc3VsdDtcbiAgICBsZXQgZGlyZWN0bHlQYXNzZWRPcHRpb25zID0ge307XG4gICAgbGV0IHJlc3VsdEZ1bmMgPSBjcmVhdGVTZWxlY3RvckFyZ3MucG9wKCk7XG4gICAgaWYgKHR5cGVvZiByZXN1bHRGdW5jID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBkaXJlY3RseVBhc3NlZE9wdGlvbnMgPSByZXN1bHRGdW5jO1xuICAgICAgcmVzdWx0RnVuYyA9IGNyZWF0ZVNlbGVjdG9yQXJncy5wb3AoKTtcbiAgICB9XG4gICAgYXNzZXJ0SXNGdW5jdGlvbihcbiAgICAgIHJlc3VsdEZ1bmMsXG4gICAgICBgY3JlYXRlU2VsZWN0b3IgZXhwZWN0cyBhbiBvdXRwdXQgZnVuY3Rpb24gYWZ0ZXIgdGhlIGlucHV0cywgYnV0IHJlY2VpdmVkOiBbJHt0eXBlb2YgcmVzdWx0RnVuY31dYFxuICAgICk7XG4gICAgY29uc3QgY29tYmluZWRPcHRpb25zID0ge1xuICAgICAgLi4uY3JlYXRlU2VsZWN0b3JDcmVhdG9yT3B0aW9ucyxcbiAgICAgIC4uLmRpcmVjdGx5UGFzc2VkT3B0aW9uc1xuICAgIH07XG4gICAgY29uc3Qge1xuICAgICAgbWVtb2l6ZSxcbiAgICAgIG1lbW9pemVPcHRpb25zID0gW10sXG4gICAgICBhcmdzTWVtb2l6ZSA9IHdlYWtNYXBNZW1vaXplLFxuICAgICAgYXJnc01lbW9pemVPcHRpb25zID0gW10sXG4gICAgICBkZXZNb2RlQ2hlY2tzID0ge31cbiAgICB9ID0gY29tYmluZWRPcHRpb25zO1xuICAgIGNvbnN0IGZpbmFsTWVtb2l6ZU9wdGlvbnMgPSBlbnN1cmVJc0FycmF5KG1lbW9pemVPcHRpb25zKTtcbiAgICBjb25zdCBmaW5hbEFyZ3NNZW1vaXplT3B0aW9ucyA9IGVuc3VyZUlzQXJyYXkoYXJnc01lbW9pemVPcHRpb25zKTtcbiAgICBjb25zdCBkZXBlbmRlbmNpZXMgPSBnZXREZXBlbmRlbmNpZXMoY3JlYXRlU2VsZWN0b3JBcmdzKTtcbiAgICBjb25zdCBtZW1vaXplZFJlc3VsdEZ1bmMgPSBtZW1vaXplKGZ1bmN0aW9uIHJlY29tcHV0YXRpb25XcmFwcGVyKCkge1xuICAgICAgcmVjb21wdXRhdGlvbnMrKztcbiAgICAgIHJldHVybiByZXN1bHRGdW5jLmFwcGx5KFxuICAgICAgICBudWxsLFxuICAgICAgICBhcmd1bWVudHNcbiAgICAgICk7XG4gICAgfSwgLi4uZmluYWxNZW1vaXplT3B0aW9ucyk7XG4gICAgbGV0IGZpcnN0UnVuID0gdHJ1ZTtcbiAgICBjb25zdCBzZWxlY3RvciA9IGFyZ3NNZW1vaXplKGZ1bmN0aW9uIGRlcGVuZGVuY2llc0NoZWNrZXIoKSB7XG4gICAgICBkZXBlbmRlbmN5UmVjb21wdXRhdGlvbnMrKztcbiAgICAgIGNvbnN0IGlucHV0U2VsZWN0b3JSZXN1bHRzID0gY29sbGVjdElucHV0U2VsZWN0b3JSZXN1bHRzKFxuICAgICAgICBkZXBlbmRlbmNpZXMsXG4gICAgICAgIGFyZ3VtZW50c1xuICAgICAgKTtcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgICAgY29uc3QgeyBpZGVudGl0eUZ1bmN0aW9uQ2hlY2ssIGlucHV0U3RhYmlsaXR5Q2hlY2sgfSA9IGdldERldk1vZGVDaGVja3NFeGVjdXRpb25JbmZvKGZpcnN0UnVuLCBkZXZNb2RlQ2hlY2tzKTtcbiAgICAgICAgaWYgKGlkZW50aXR5RnVuY3Rpb25DaGVjay5zaG91bGRSdW4pIHtcbiAgICAgICAgICBpZGVudGl0eUZ1bmN0aW9uQ2hlY2sucnVuKFxuICAgICAgICAgICAgcmVzdWx0RnVuY1xuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlucHV0U3RhYmlsaXR5Q2hlY2suc2hvdWxkUnVuKSB7XG4gICAgICAgICAgY29uc3QgaW5wdXRTZWxlY3RvclJlc3VsdHNDb3B5ID0gY29sbGVjdElucHV0U2VsZWN0b3JSZXN1bHRzKFxuICAgICAgICAgICAgZGVwZW5kZW5jaWVzLFxuICAgICAgICAgICAgYXJndW1lbnRzXG4gICAgICAgICAgKTtcbiAgICAgICAgICBpbnB1dFN0YWJpbGl0eUNoZWNrLnJ1bihcbiAgICAgICAgICAgIHsgaW5wdXRTZWxlY3RvclJlc3VsdHMsIGlucHV0U2VsZWN0b3JSZXN1bHRzQ29weSB9LFxuICAgICAgICAgICAgeyBtZW1vaXplLCBtZW1vaXplT3B0aW9uczogZmluYWxNZW1vaXplT3B0aW9ucyB9LFxuICAgICAgICAgICAgYXJndW1lbnRzXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZmlyc3RSdW4pXG4gICAgICAgICAgZmlyc3RSdW4gPSBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGxhc3RSZXN1bHQgPSBtZW1vaXplZFJlc3VsdEZ1bmMuYXBwbHkobnVsbCwgaW5wdXRTZWxlY3RvclJlc3VsdHMpO1xuICAgICAgcmV0dXJuIGxhc3RSZXN1bHQ7XG4gICAgfSwgLi4uZmluYWxBcmdzTWVtb2l6ZU9wdGlvbnMpO1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduKHNlbGVjdG9yLCB7XG4gICAgICByZXN1bHRGdW5jLFxuICAgICAgbWVtb2l6ZWRSZXN1bHRGdW5jLFxuICAgICAgZGVwZW5kZW5jaWVzLFxuICAgICAgZGVwZW5kZW5jeVJlY29tcHV0YXRpb25zOiAoKSA9PiBkZXBlbmRlbmN5UmVjb21wdXRhdGlvbnMsXG4gICAgICByZXNldERlcGVuZGVuY3lSZWNvbXB1dGF0aW9uczogKCkgPT4ge1xuICAgICAgICBkZXBlbmRlbmN5UmVjb21wdXRhdGlvbnMgPSAwO1xuICAgICAgfSxcbiAgICAgIGxhc3RSZXN1bHQ6ICgpID0+IGxhc3RSZXN1bHQsXG4gICAgICByZWNvbXB1dGF0aW9uczogKCkgPT4gcmVjb21wdXRhdGlvbnMsXG4gICAgICByZXNldFJlY29tcHV0YXRpb25zOiAoKSA9PiB7XG4gICAgICAgIHJlY29tcHV0YXRpb25zID0gMDtcbiAgICAgIH0sXG4gICAgICBtZW1vaXplLFxuICAgICAgYXJnc01lbW9pemVcbiAgICB9KTtcbiAgfTtcbiAgcmV0dXJuIGNyZWF0ZVNlbGVjdG9yMjtcbn1cbnZhciBjcmVhdGVTZWxlY3RvciA9IC8qIEBfX1BVUkVfXyAqLyBjcmVhdGVTZWxlY3RvckNyZWF0b3Iod2Vha01hcE1lbW9pemUpO1xuXG4vLyBzcmMvY3JlYXRlU3RydWN0dXJlZFNlbGVjdG9yLnRzXG52YXIgY3JlYXRlU3RydWN0dXJlZFNlbGVjdG9yID0gKGlucHV0U2VsZWN0b3JzT2JqZWN0LCBzZWxlY3RvckNyZWF0b3IgPSBjcmVhdGVTZWxlY3RvcikgPT4ge1xuICBhc3NlcnRJc09iamVjdChcbiAgICBpbnB1dFNlbGVjdG9yc09iamVjdCxcbiAgICBgY3JlYXRlU3RydWN0dXJlZFNlbGVjdG9yIGV4cGVjdHMgZmlyc3QgYXJndW1lbnQgdG8gYmUgYW4gb2JqZWN0IHdoZXJlIGVhY2ggcHJvcGVydHkgaXMgYSBzZWxlY3RvciwgaW5zdGVhZCByZWNlaXZlZCBhICR7dHlwZW9mIGlucHV0U2VsZWN0b3JzT2JqZWN0fWBcbiAgKTtcbiAgY29uc3QgaW5wdXRTZWxlY3RvcktleXMgPSBPYmplY3Qua2V5cyhpbnB1dFNlbGVjdG9yc09iamVjdCk7XG4gIGNvbnN0IGRlcGVuZGVuY2llcyA9IGlucHV0U2VsZWN0b3JLZXlzLm1hcCgoa2V5KSA9PiBpbnB1dFNlbGVjdG9yc09iamVjdFtrZXldKTtcbiAgY29uc3Qgc3RydWN0dXJlZFNlbGVjdG9yID0gc2VsZWN0b3JDcmVhdG9yKFxuICAgIGRlcGVuZGVuY2llcyxcbiAgICAoLi4uaW5wdXRTZWxlY3RvclJlc3VsdHMpID0+IHtcbiAgICAgIHJldHVybiBpbnB1dFNlbGVjdG9yUmVzdWx0cy5yZWR1Y2UoKGNvbXBvc2l0aW9uLCB2YWx1ZSwgaW5kZXgpID0+IHtcbiAgICAgICAgY29tcG9zaXRpb25baW5wdXRTZWxlY3RvcktleXNbaW5kZXhdXSA9IHZhbHVlO1xuICAgICAgICByZXR1cm4gY29tcG9zaXRpb247XG4gICAgICB9LCB7fSk7XG4gICAgfVxuICApO1xuICByZXR1cm4gc3RydWN0dXJlZFNlbGVjdG9yO1xufTtcbmV4cG9ydCB7XG4gIGNyZWF0ZVNlbGVjdG9yLFxuICBjcmVhdGVTZWxlY3RvckNyZWF0b3IsXG4gIGNyZWF0ZVN0cnVjdHVyZWRTZWxlY3RvcixcbiAgbHJ1TWVtb2l6ZSxcbiAgcmVmZXJlbmNlRXF1YWxpdHlDaGVjayxcbiAgc2V0R2xvYmFsRGV2TW9kZUNoZWNrcyxcbiAgYXV0b3RyYWNrTWVtb2l6ZSBhcyB1bnN0YWJsZV9hdXRvdHJhY2tNZW1vaXplLFxuICB3ZWFrTWFwTWVtb2l6ZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlc2VsZWN0Lm1qcy5tYXAiXSwibmFtZXMiOlsicnVuSWRlbnRpdHlGdW5jdGlvbkNoZWNrIiwicmVzdWx0RnVuYyIsImlzSW5wdXRTYW1lQXNPdXRwdXQiLCJlbXB0eU9iamVjdCIsInN0YWNrIiwiRXJyb3IiLCJlIiwiY29uc29sZSIsIndhcm4iLCJydW5JbnB1dFN0YWJpbGl0eUNoZWNrIiwiaW5wdXRTZWxlY3RvclJlc3VsdHNPYmplY3QiLCJvcHRpb25zIiwiaW5wdXRTZWxlY3RvckFyZ3MiLCJtZW1vaXplIiwibWVtb2l6ZU9wdGlvbnMiLCJpbnB1dFNlbGVjdG9yUmVzdWx0cyIsImlucHV0U2VsZWN0b3JSZXN1bHRzQ29weSIsImNyZWF0ZUFuRW1wdHlPYmplY3QiLCJhcmVJbnB1dFNlbGVjdG9yUmVzdWx0c0VxdWFsIiwiYXBwbHkiLCJhcmd1bWVudHMiLCJmaXJzdElucHV0cyIsInNlY29uZElucHV0cyIsImdsb2JhbERldk1vZGVDaGVja3MiLCJpbnB1dFN0YWJpbGl0eUNoZWNrIiwiaWRlbnRpdHlGdW5jdGlvbkNoZWNrIiwic2V0R2xvYmFsRGV2TW9kZUNoZWNrcyIsImRldk1vZGVDaGVja3MiLCJPYmplY3QiLCJhc3NpZ24iLCJOT1RfRk9VTkQiLCJhc3NlcnRJc0Z1bmN0aW9uIiwiZnVuYyIsImVycm9yTWVzc2FnZSIsIlR5cGVFcnJvciIsImFzc2VydElzT2JqZWN0Iiwib2JqZWN0IiwiYXNzZXJ0SXNBcnJheU9mRnVuY3Rpb25zIiwiYXJyYXkiLCJldmVyeSIsIml0ZW0iLCJpdGVtVHlwZXMiLCJtYXAiLCJuYW1lIiwiam9pbiIsImVuc3VyZUlzQXJyYXkiLCJBcnJheSIsImlzQXJyYXkiLCJnZXREZXBlbmRlbmNpZXMiLCJjcmVhdGVTZWxlY3RvckFyZ3MiLCJkZXBlbmRlbmNpZXMiLCJjb2xsZWN0SW5wdXRTZWxlY3RvclJlc3VsdHMiLCJsZW5ndGgiLCJpIiwicHVzaCIsImdldERldk1vZGVDaGVja3NFeGVjdXRpb25JbmZvIiwiZmlyc3RSdW4iLCJzaG91bGRSdW4iLCJydW4iLCIkUkVWSVNJT04iLCJDVVJSRU5UX1RSQUNLRVIiLCJDZWxsIiwiY29uc3RydWN0b3IiLCJpbml0aWFsVmFsdWUiLCJpc0VxdWFsIiwidHJpcGxlRXEiLCJyZXZpc2lvbiIsIl9pc0VxdWFsIiwiX3ZhbHVlIiwiX2xhc3RWYWx1ZSIsInZhbHVlIiwiYWRkIiwibmV3VmFsdWUiLCJhIiwiYiIsIlRyYWNraW5nQ2FjaGUiLCJmbiIsIl9jYWNoZWRSZXZpc2lvbiIsIl9kZXBzIiwiaGl0cyIsImNsZWFyIiwiX2NhY2hlZFZhbHVlIiwiY3VycmVudFRyYWNrZXIiLCJTZXQiLCJwcmV2VHJhY2tlciIsImZyb20iLCJNYXRoIiwibWF4IiwiZCIsImdldFZhbHVlIiwiY2VsbCIsInNldFZhbHVlIiwic3RvcmFnZSIsImNyZWF0ZUNlbGwiLCJjcmVhdGVDYWNoZSIsIm5ldmVyRXEiLCJjcmVhdGVUYWciLCJkaXJ0eVRhZyIsInRhZyIsImNvbnN1bWVDb2xsZWN0aW9uIiwibm9kZSIsImNvbGxlY3Rpb25UYWciLCJkaXJ0eUNvbGxlY3Rpb24iLCJSRURVWF9QUk9YWV9MQUJFTCIsIlN5bWJvbCIsIm5leHRJZCIsInByb3RvIiwiZ2V0UHJvdG90eXBlT2YiLCJPYmplY3RUcmVlTm9kZSIsInByb3h5IiwiUHJveHkiLCJvYmplY3RQcm94eUhhbmRsZXIiLCJ0YWdzIiwiY2hpbGRyZW4iLCJpZCIsImdldCIsImtleSIsImNhbGN1bGF0ZVJlc3VsdCIsImNoaWxkVmFsdWUiLCJSZWZsZWN0IiwiY2hpbGROb2RlIiwiY3JlYXRlTm9kZSIsInJlcyIsIm93bktleXMiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJwcm9wIiwiaGFzIiwiQXJyYXlUcmVlTm9kZSIsImFycmF5UHJveHlIYW5kbGVyIiwidXBkYXRlTm9kZSIsIm9sZEtleXNTaXplIiwibmV3S2V5c1NpemUiLCJhbnlLZXlzQWRkZWQiLCJfa2V5IiwiaXNEaWZmZXJlbnQiLCJuZXdDaGlsZFZhbHVlIiwiZGVsZXRlTm9kZSIsImNyZWF0ZVNpbmdsZXRvbkNhY2hlIiwiZXF1YWxzIiwiZW50cnkiLCJwdXQiLCJnZXRFbnRyaWVzIiwiY3JlYXRlTHJ1Q2FjaGUiLCJtYXhTaXplIiwiZW50cmllcyIsImNhY2hlSW5kZXgiLCJmaW5kSW5kZXgiLCJzcGxpY2UiLCJ1bnNoaWZ0IiwicG9wIiwicmVmZXJlbmNlRXF1YWxpdHlDaGVjayIsImNyZWF0ZUNhY2hlS2V5Q29tcGFyYXRvciIsImVxdWFsaXR5Q2hlY2siLCJhcmVBcmd1bWVudHNTaGFsbG93bHlFcXVhbCIsInByZXYiLCJuZXh0IiwibHJ1TWVtb2l6ZSIsImVxdWFsaXR5Q2hlY2tPck9wdGlvbnMiLCJwcm92aWRlZE9wdGlvbnMiLCJyZXN1bHRFcXVhbGl0eUNoZWNrIiwiY29tcGFyYXRvciIsInJlc3VsdHNDb3VudCIsImNhY2hlIiwibWVtb2l6ZWQiLCJtYXRjaGluZ0VudHJ5IiwiZmluZCIsImNsZWFyQ2FjaGUiLCJyZXNldFJlc3VsdHNDb3VudCIsImF1dG90cmFja01lbW9pemUiLCJsYXN0QXJncyIsInNoYWxsb3dFcXVhbCIsIlN0cm9uZ1JlZiIsImRlcmVmIiwiUmVmIiwiV2Vha1JlZiIsIlVOVEVSTUlOQVRFRCIsIlRFUk1JTkFURUQiLCJjcmVhdGVDYWNoZU5vZGUiLCJzIiwidiIsIm8iLCJwIiwid2Vha01hcE1lbW9pemUiLCJmbk5vZGUiLCJsYXN0UmVzdWx0IiwiY2FjaGVOb2RlIiwibCIsImFyZyIsIm9iamVjdENhY2hlIiwiV2Vha01hcCIsIm9iamVjdE5vZGUiLCJzZXQiLCJwcmltaXRpdmVDYWNoZSIsIk1hcCIsInByaW1pdGl2ZU5vZGUiLCJ0ZXJtaW5hdGVkTm9kZSIsInJlc3VsdCIsImxhc3RSZXN1bHRWYWx1ZSIsIm5lZWRzV2Vha1JlZiIsImNyZWF0ZVNlbGVjdG9yQ3JlYXRvciIsIm1lbW9pemVPck9wdGlvbnMiLCJtZW1vaXplT3B0aW9uc0Zyb21BcmdzIiwiY3JlYXRlU2VsZWN0b3JDcmVhdG9yT3B0aW9ucyIsImNyZWF0ZVNlbGVjdG9yMiIsInJlY29tcHV0YXRpb25zIiwiZGVwZW5kZW5jeVJlY29tcHV0YXRpb25zIiwiZGlyZWN0bHlQYXNzZWRPcHRpb25zIiwiY29tYmluZWRPcHRpb25zIiwiYXJnc01lbW9pemUiLCJhcmdzTWVtb2l6ZU9wdGlvbnMiLCJmaW5hbE1lbW9pemVPcHRpb25zIiwiZmluYWxBcmdzTWVtb2l6ZU9wdGlvbnMiLCJtZW1vaXplZFJlc3VsdEZ1bmMiLCJyZWNvbXB1dGF0aW9uV3JhcHBlciIsInNlbGVjdG9yIiwiZGVwZW5kZW5jaWVzQ2hlY2tlciIsInByb2Nlc3MiLCJyZXNldERlcGVuZGVuY3lSZWNvbXB1dGF0aW9ucyIsInJlc2V0UmVjb21wdXRhdGlvbnMiLCJjcmVhdGVTZWxlY3RvciIsImNyZWF0ZVN0cnVjdHVyZWRTZWxlY3RvciIsImlucHV0U2VsZWN0b3JzT2JqZWN0Iiwic2VsZWN0b3JDcmVhdG9yIiwiaW5wdXRTZWxlY3RvcktleXMiLCJrZXlzIiwic3RydWN0dXJlZFNlbGVjdG9yIiwicmVkdWNlIiwiY29tcG9zaXRpb24iLCJpbmRleCIsInVuc3RhYmxlX2F1dG90cmFja01lbW9pemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/reselect/dist/reselect.mjs\n");

/***/ })

};
;