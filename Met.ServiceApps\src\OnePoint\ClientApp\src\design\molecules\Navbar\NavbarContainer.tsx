import { Navbar } from '@/design/molecules';
import { selectedOrderGroups } from '@/features/serviceOrder';
import { useAppSelector } from '@/store/hooks';

interface Props {
  closeNotification?: () => void;
}

export const NavbarContainer = ({ closeNotification }: Props) => {
  const savedOrderGroups = useAppSelector(selectedOrderGroups);

  return (
    <Navbar
      savedOrderGroups={savedOrderGroups}
      closeNotification={closeNotification}
    />
  );
};
