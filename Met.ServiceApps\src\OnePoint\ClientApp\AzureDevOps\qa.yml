trigger: none

resources:
  repositories:
    - repository: templates
      type: git
      name: ServiceApps/PipelineTemplates
      ref: refs/tags/1.1.0

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: build_test
    displayName: Build and Test
    jobs:
      - job: build_test
        displayName: Build and Test
        steps:
          - template: frontend_qa.yml@templates
            parameters:
              projectPath: Met.ServiceApps/src/OnePoint/ClientApp
  - stage: snyk_scan
    displayName: Snyk SAST/SCA
    dependsOn: []
    jobs:
      - template: SnykTemplates/snyk_sast.yml@templates
        parameters:
          scanDirectory: /Met.ServiceApps/src/OnePoint/ClientApp
      - template: SnykTemplates/snyk_sca.yml@templates
        parameters:
          scanDirectory: /Met.ServiceApps/src/OnePoint/ClientApp
