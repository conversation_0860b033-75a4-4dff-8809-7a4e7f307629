/* eslint-disable react/jsx-no-literals */
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '@emotion/react';
import { createTheme } from '@mui/material/styles';
import { PageHeaderContainer } from './PageHeaderContainer';

describe('PageHeaderContainer', () => {
  jest.mock('@mui/system', () => ({
    ...jest.requireActual('@mui/system'),
    useTheme: () => createTheme()
  }));
  it('renders children correctly', () => {
    const { getByTestId } = render(
      <ThemeProvider theme={createTheme()}>
        <PageHeaderContainer>
          <div data-testid="test-child">Test Child</div>
        </PageHeaderContainer>
      </ThemeProvider>
    );

    const childElement = getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
  });

  it('applies styles correctly', () => {
    const { container } = render(
      <ThemeProvider theme={createTheme()}>
        <PageHeaderContainer sx={{ color: 'primary.main' }}>
          <div>Test Child</div>
        </PageHeaderContainer>
      </ThemeProvider>
    );

    const appBarElement = container.querySelector('div');
    expect(appBarElement).toHaveStyle('color: primary.main');
  });
});
