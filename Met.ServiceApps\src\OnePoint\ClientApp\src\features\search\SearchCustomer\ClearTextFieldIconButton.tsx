import { CloseIcon } from '@/design/atoms';
import { AccountSearchSchemaType } from '@/features/search';
import { IconButton, Tooltip } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { Control, UseFormResetField, useWatch } from 'react-hook-form';

interface Props {
  control: Control<AccountSearchSchemaType>;
  isFetching: boolean;
  propName: keyof AccountSearchSchemaType;
  resetField: UseFormResetField<AccountSearchSchemaType>;
}

export const ClearTextFieldIconButton = ({
  resetField,
  propName,
  control,
  isFetching
}: Props) => {
  const { t } = useTranslation();
  const value = useWatch({
    control,
    name: propName,
    defaultValue: ''
  });

  if (isFetching || value === undefined || value.length < 1) return <></>;
  return (
    <>
      <Tooltip placement="top" title={t('common.clear')}>
        <IconButton
          data-testid="closeIconBtn-testId"
          onClick={() => resetField(propName)}
        >
          <CloseIcon
            style={{
              width: '24px',
              height: '24px'
            }}
            viewBox="0 0 24 24"
            data-testid="closeButtonSearch-testId"
          />
        </IconButton>
      </Tooltip>
    </>
  );
};
