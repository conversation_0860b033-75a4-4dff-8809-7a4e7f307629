import { LocationById } from '@/features/branch';
import { Customer, CustomerCard } from '@/features/customer';
import { Product, ProductListItem } from '@/features/product';
import {
  CardContentSection,
  CheckReturnsSelectedAlert,
  CreateSalesOrderReturnResponse,
  OrderResponse,
  ProductReturnSelection,
  SalesOrderRefundDrawer
} from '@/features/salesOrder';
import { PaymentMethodText } from '@/features/serviceOrder';
import { getCurrencyType, getPriceAsString } from '@/util';
import {
  Box,
  Card,
  CardContent,
  Divider,
  Grid,
  Typography
} from '@mui/material';
import { useTranslation } from 'next-export-i18n';

interface Props {
  products: Map<string, Product & ProductReturnSelection>;
  originalOrder: OrderResponse;
  customer?: Customer;
  originalOrderLocation: LocationById;
  alreadyReturnedItems: Map<string, number>;
  pendingReturn?: CreateSalesOrderReturnResponse | { isLoading: boolean };
  itemInclusionChanged: (sku: string, count: number) => void;
  onConfirmReturn: () => void;
  onCompleteReturn: () => void;
}

export const CreateOrderReturn = ({
  products,
  originalOrder,
  customer,
  originalOrderLocation,
  alreadyReturnedItems,
  pendingReturn,
  itemInclusionChanged,
  onConfirmReturn,
  onCompleteReturn
}: Props) => {
  const { t } = useTranslation();

  const currencyType = getCurrencyType(originalOrderLocation?.countryCode);

  const productsAndSelection = originalOrder.items
    .filter((p) => products.has(p.sku))
    .map((p) => ({
      ...products.get(p.sku)!,
      ...p,
      remainingQuantity: p.quantity - (alreadyReturnedItems.get(p.sku) ?? 0)
    }));

  const returnConfirmed =
    pendingReturn != null && 'isLoading' in pendingReturn
      ? pendingReturn.isLoading
      : pendingReturn != null;

  const updateReturnSelection = (
    product: (typeof productsAndSelection)[0],
    count: number
  ) =>
    itemInclusionChanged(
      product.sku,
      Math.max(0, Math.min(product.remainingQuantity, count))
    );

  const salesOrderNumberDisplay = () =>
    `${t('features.salesOrder.orderDetailsIdentifier')} ${
      originalOrder.salesOrderNumber
    }`;

  return (
    <>
      {customer && (
        <Box
          sx={{
            borderRadius: '4px',
            bgcolor: 'common.white',
            border: '1px solid',
            borderColor: 'neutral.300'
          }}
        >
          <CustomerCard customer={customer} />
        </Box>
      )}
      <Box sx={{ mt: '.5rem' }}>
        <Typography variant="h5" color="neutral.500">
          {t('features.salesOrder.cart', {
            count: originalOrder.items.reduce(
              (total: number, item) => total + item.quantity,
              0
            )
          })}
        </Typography>
      </Box>
      <Card variant="outlined" sx={{ boxShadow: 1, my: '1rem' }}>
        <CardContent
          sx={{
            padding: '0',
            backgroundColor: 'common.white',
            ':last-child': {
              paddingBottom: '0'
            }
          }}
        >
          <CardContentSection>
            <Box>
              <Typography color="neutral.700">
                {salesOrderNumberDisplay()}
              </Typography>
            </Box>
          </CardContentSection>
          <CardContentSection sx={{ borderBottom: 'none' }}>
            <Box sx={{ width: '100%' }}>
              {productsAndSelection.map((p, i) => (
                <>
                  {i >= 1 && <Divider sx={{ color: 'neutral.50' }} />}
                  <ProductListItem
                    key={p.sku}
                    item={p}
                    hideSelectButton
                    displayCheckboxSelection
                    displayPrices
                    displayItemCounter={p.remainingQuantity > 1}
                    resultItemStyleOverride={{
                      borderBottom: 'none',
                      borderColor: 'none'
                    }}
                    minSelectableQuantity={0}
                    maxSelectableQuantity={p.remainingQuantity}
                    selectedCheckboxItems={p.count > 0 ? [p.sku] : []}
                    netPrice={getPriceAsString(p.price, currencyType)}
                    selectedQuantity={p.count}
                    handleCheckboxSelection={(_, checked) =>
                      updateReturnSelection(p, checked ? 1 : 0)
                    }
                    onItemCounterChanges={(_, count) =>
                      updateReturnSelection(p, count)
                    }
                    disabled={p.remainingQuantity < 1}
                    disableCheckboxSelection={returnConfirmed}
                    disableCounter={returnConfirmed}
                  />
                </>
              ))}
            </Box>
          </CardContentSection>
          <CheckReturnsSelectedAlert selectedProducts={productsAndSelection} />
        </CardContent>
      </Card>
      <Box sx={{ mt: '.5rem' }}>
        <Typography variant="h5" color="neutral.500">
          {t('features.order.return.paymentMethod')}
        </Typography>
      </Box>
      <Card variant="outlined" sx={{ boxShadow: 1, my: '1rem' }}>
        <CardContent
          sx={{
            backgroundColor: 'common.white'
          }}
        >
          <Grid container direction={'column'} alignItems={'flex-start'}>
            <Grid item>
              <Typography variant="caption" color="primary.main">
                {t('common.paymentMethod')}
                {t('common.colonChar')}
              </Typography>
            </Grid>
            <Grid item>
              <PaymentMethodText
                isWarrantyPayment={false}
                cardBrand={originalOrder.payment?.cardBrand}
                cardLast4={originalOrder.payment?.cardLast4}
                paymentMethodCode={originalOrder.payment?.paymentMethodId}
                totalPrice={originalOrder.payment?.totalPrice}
                purchaseOrder={originalOrder.payment?.purchaseOrderNumber}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      <SalesOrderRefundDrawer
        selectedProducts={productsAndSelection.filter((p) => p.count > 0)}
        currencyType={currencyType}
        pendingReturn={pendingReturn}
        onCompleteReturn={onCompleteReturn}
        onConfirmReturn={onConfirmReturn}
      />
    </>
  );
};
