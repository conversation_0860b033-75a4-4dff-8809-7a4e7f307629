import { ThemeProvider } from '@emotion/react';
import { createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import React from 'react';
import { SelectFormControl } from '@/design/molecules';
import {
  SELECT_FORM_CONTROL_VARIANTS,
  SelectFormControlVariant
} from './variants';

const renderWithTheme = (component: React.ReactNode) => {
  return render(
    <ThemeProvider theme={createTheme()}>{component}</ThemeProvider>
  );
};

describe('SelectFormControl Variants', () => {
  const onValueChangeMock = jest.fn();
  Object.keys(SELECT_FORM_CONTROL_VARIANTS).forEach((variant) => {
    it(`renders ${variant} variant correctly`, () => {
      const { getByTestId } = renderWithTheme(
        <SelectFormControl
          name="test"
          inputLabel="Test Label"
          value=""
          onValueChange={onValueChangeMock}
          variant={variant as SelectFormControlVariant}
        >
          {[]}
        </SelectFormControl>
      );

      const selectFormControl = getByTestId('select-test-testId');
      expect(selectFormControl).toBeInTheDocument();
    });
  });
});

it(`renders envelope icon correctly`, () => {
  const { getByTestId } = renderWithTheme(
    <SelectFormControl
      name="test"
      inputLabel="Test Label"
      value=""
      onValueChange={jest.fn()}
      variant={'standard'}
      isIconVisible={true}
    >
      {[]}
    </SelectFormControl>
  );

  const selectFormControl = getByTestId('select-test-testId');
  expect(selectFormControl).toBeInTheDocument();
});
