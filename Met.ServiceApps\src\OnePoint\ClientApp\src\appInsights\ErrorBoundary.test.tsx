import React from 'react';
import { screen, render } from '@testing-library/react';
import { ErrorBoundary } from './ErrorBoundary';

const customError = 'Some custom error';
const originalConsoleError = console.error;

function ErrorChild() {
  const throwError = () => {
    throw new Error('test error');
  };

  return <p>{throwError()}</p>;
}

beforeEach(() => {
  console.error = jest.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
});

describe('ErrorBoundary Tests', () => {
  it('Should display the fallback error component when a custom error is not provided', async () => {
    render(
      <ErrorBoundary>
        <ErrorChild />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).not.toBeNull();
  });

  it('Should display the custom error component when one is provided', async () => {
    render(
      <ErrorBoundary customError={<div>{customError}</div>}>
        <ErrorChild />
      </ErrorBoundary>
    );

    expect(screen.getByText(customError)).not.toBeNull();
  });
});
