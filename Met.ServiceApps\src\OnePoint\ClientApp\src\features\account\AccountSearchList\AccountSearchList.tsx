import { WarningNotificationIcon } from '@/design/atoms';
import {
  AccountNotFound,
  AccountSearchItem,
  AccountSearchResultHeader,
  SiteItem
} from '@/features/account';
import { PagedListDetails } from '@/features/search';
import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Typography
} from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useEffect, useRef, useState } from 'react';

interface Props {
  isFetching: boolean;
  items: SiteItem[];
  paginationData: PagedListDetails;
  isViewMode: boolean;
  isListOpened: boolean;
  valueNotFound: boolean;
  onAccountSelected: (siteNumber: string, site: SiteItem) => void;
  onLoadMoreClick: () => void;
  searchText?: string;
  hasTooManySitesResults?: boolean;
}

export const AccountSearchList = ({
  isFetching,
  items,
  onAccountSelected,
  isViewMode,
  isListOpened,
  valueNotFound,
  paginationData,
  onLoadMoreClick,
  searchText,
  hasTooManySitesResults
}: Props) => {
  const [isProfileFormHidden, setProfileFormHidden] = useState(true);
  const { t } = useTranslation();
  const gridItemRef = useRef<HTMLDivElement | null>(null);
  const gridContainerRef = useRef<HTMLDivElement | null>(null);
  const defaultItemBoxHeight = 155;
  const [, setItemBoxHeight] = useState(defaultItemBoxHeight);
  const topSizeInPx = 272;
  useEffect(() => {
    // Height of product list container is defined based on the number of items to fit in vertically (instead of fixed pixels).
    const item = gridItemRef.current;
    if (item) {
      const calculateHeight = () => {
        const height = item.getBoundingClientRect().height;
        setItemBoxHeight(height);
      };

      const image = item.querySelector('img');
      image?.addEventListener('load', calculateHeight);

      return () => {
        image?.removeEventListener('load', calculateHeight);
      };
    }
  }, [items]);

  useEffect(() => {
    if (!isListOpened) {
      setProfileFormHidden(true);
    }
  }, [isListOpened]);

  return (
    <Box
      data-testid="customerList-testId"
      ref={gridContainerRef}
      sx={{
        position: 'relative',
        zIndex: 10,
        flexDirection: 'column',
        overflowY: 'auto',
        maxHeight: `calc(100vh - ${topSizeInPx + 32}px)`,
        mb: 4,
        borderColor: 'neutral.300',
        backgroundColor: 'common.white'
      }}
    >
      {isListOpened && (
        <Box
          sx={{
            width: '100%',
            position: 'relative',
            zIndex: 1,
            backgroundColor: 'transparent'
          }}
        >
          {valueNotFound && !isFetching ? (
            <AccountNotFound />
          ) : (
            <>
              {isProfileFormHidden && (
                <>
                  {hasTooManySitesResults ? (
                    <>
                      <Box
                        sx={{
                          display: 'flex',
                          mt: '24px',
                          marginBottom: '24px',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          gap: '12px'
                        }}
                        data-testid="searchResultsMoreThan50AlertContainer-testId"
                      >
                        <WarningNotificationIcon
                          data-testid="WarningNotificationIcon-testId"
                          sx={{
                            width: '48px',
                            height: '38px'
                          }}
                        />
                        <Typography
                          data-testid="searchResultsMoreThan50Text-testId"
                          variant="p3"
                          color="neutral.500"
                        >
                          {t('features.account.search.searchResultsMoreThan50')}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <>
                      {items.length > 0 && (
                        <AccountSearchResultHeader sites={items} />
                      )}
                      {items.map((site) => (
                        <AccountSearchItem
                          key={site.id}
                          site={site}
                          isViewMode={isViewMode}
                          onAccountSelected={onAccountSelected}
                          searchText={searchText}
                        />
                      ))}
                      <Divider sx={{ color: 'neutral.50' }} />
                      {items &&
                        paginationData?.page < paginationData?.totalPages &&
                        !isFetching && (
                          <Box
                            data-testid="loadMore-testId"
                            onClick={onLoadMoreClick}
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              margin: '20px',
                              cursor: 'pointer'
                            }}
                          >
                            <Typography color={'primary.main'}>
                              {t('common.loadMore')}
                            </Typography>
                          </Box>
                        )}
                    </>
                  )}
                </>
              )}
            </>
          )}

          {isFetching && (
            <Grid data-testid="loadingSpinner-testId">
              <Card
                sx={{
                  background: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  alignSelf: 'stretch'
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <CircularProgress />
                </CardContent>
              </Card>
            </Grid>
          )}
        </Box>
      )}
    </Box>
  );
};
