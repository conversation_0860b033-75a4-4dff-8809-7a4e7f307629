'use client';

import { useRoleActions } from '@/auth';
import {
  ActionCategory,
  ActionContent,
  ActionDepth,
  ActionDepthContext,
  ActionParent,
  ActionPushTypeEvent,
  ActionTarget,
  ActionType,
  pushTrackedGtmLayer,
  useLoggedUser
} from '@/config';
import {
  CloseIcon,
  EditIconHeader,
  ListIcon,
  PlusIcon,
  WarningNotificationIcon
} from '@/design/atoms';
import {
  Button,
  Loading,
  PageHeaderContainer,
  Snackbar,
  Tooltip
} from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import {
  clearSelectedCustomer,
  onShowContactDetailsModalChange,
  selectedAccountPageState,
  useGetAccount
} from '@/features/account';
import { BranchesCheck, Location, selectBranch } from '@/features/branch';
import {
  Customer,
  CustomerAddress,
  CustomerCard,
  CustomerPreferences,
  EditProfileModal,
  ProfileFormModal,
  setRedirectToHome,
  setSelectedCustomer,
  statesData,
  useCreateAddressMutation,
  useLazyGetCustomerQuery,
  useSearchCustomersQuery
} from '@/features/customer';
import { selectHub } from '@/features/hub';
import { CurrencyType } from '@/features/pricing';
import {
  CategoryCode,
  ProductCardContainer,
  RealTimeServiceTypes,
  SelectProductStrategies,
  SelectedProduct,
  ServiceCartProduct,
  WorkOrderItem,
  useGetProductBySkuQuery
} from '@/features/product';
import {
  AddRepairItemButton,
  CartIcon,
  CheckoutActionButtonContainer,
  CloseRepairPromptContainer,
  ConfirmationPage,
  NoItemsSelected,
  PreCheckoutPage,
  RealtimeComplete,
  RepairHeader,
  RepairItemsContainer,
  RepairItemsCount,
  WorkItemsContainer,
  initRealTimeServiceChange,
  onCustomerAccountChange,
  onServiceCategoriesDataChange,
  selectedRepairPageState
} from '@/features/repair';
import { SearchCustomerContainer } from '@/features/search';
import {
  BlobContainer,
  CreateOrderGroupRequest,
  CreateOrderRequest,
  DeliveryType,
  GroupResponse,
  OrderPaymentState,
  OrderRepairLine,
  OrderRepairStep,
  RepairTypeCode,
  ReturnLocationAddress,
  ServiceCartProductContainer,
  ServiceCartSidebarHeader,
  ServiceOrderPaymentDrawer,
  ServiceOrderPaymentStepCode,
  ServiceOrderSummary,
  UpdateServiceOrdersPickupRequest,
  addOrderGroup,
  clearOrderGroups,
  clearRealtimeService,
  clearSelectedProducts,
  clearServiceCart,
  handleWorkItemsChange,
  onPaymentMethodsChange,
  orderGroupsStateEqual,
  removeOrderGroup,
  selectedOrderGroups,
  selectedServiceOrderState,
  setIsRealtimeService,
  updateOrderGroup,
  updatePricingTax,
  useCalculatePaymentMethods,
  useCreateServiceOrderChargesMutation,
  useCreateServiceOrderGroupMutation,
  useCreateServiceOrderGroupRealTimeMutation,
  useCreateServiceOrderGroupSessionMutation,
  useLazyGetCategoriesQuery,
  useLazyGetServiceOrderGroupQuery,
  useLazyGetServiceOrderPricingQuery,
  useLazyGetUserAddressesQuery,
  useSubmitFile,
  useUpdateServiceOrdersPickupMutation,
  useUploadProofOfPurchaseMutation
} from '@/features/serviceOrder';
import { useFetchLightingMaxRepairPricing } from '@/hooks';
import { useSignalR } from '@/signalr';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  LMR_ADJUSMENT_SKU,
  PaymentMethodCode,
  SnackbarState,
  checkIfIsCustomerComplete,
  getCurrencyType
} from '@/util';
import {
  Alert,
  Box,
  Divider,
  IconButton,
  SwipeableDrawer,
  Typography
} from '@mui/material';
import { skipToken } from '@reduxjs/toolkit/query';
import dayjs from 'dayjs';
import { useTranslation } from 'next-export-i18n';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const signalrMethod = 'RefreshServiceRequest';

export default function OrderRepairPage() {
  const { t } = useTranslation();
  const {
    isConnected,
    joinServiceRequestGroup,
    addListener,
    connect,
    disconnect,
    removeListener
  } = useSignalR();

  const {
    canCreateCustomer,
    hasBranchSelection,
    hasHubSelection,
    canStartRealtimeRepair
  } = useRoleActions();
  const {
    dropOffParty,
    currentSite,
    showContactDetailsModal,
    customerProfileSelected
  } = useAppSelector(selectedAccountPageState);

  const { handleSubmit } = useSubmitFile();
  const searchParams = useSearchParams();
  const customerId = searchParams?.get('id') ?? '';
  const isTaxExempt = searchParams.get('isTaxExempt') === 'true';
  const isToteOrder = searchParams.get('isToteOrder') === 'true';
  const isRealTime = searchParams.get('realtime') === 'true';
  const forceRealtimeService = useRef(
    () => isRealTime && canStartRealtimeRepair
  );

  const forceEnterRepairLines = useRef(
    () => isRealTime && canStartRealtimeRepair
  );
  const accountId = searchParams?.get('aid') ?? '';
  const searchPostalCode = searchParams?.get('spc') ?? '';

  const { data: customerData, isFetching } = useSearchCustomersQuery(
    customerId
      ? {
          ids: [customerId]
        }
      : skipToken
  );
  const router = useRouter();
  const { redirectToHome } = useAppSelector((state) => state.customer);
  const returnTo = useAppSelector((state) => state.serviceOrder.returnTo);

  const dispatch = useAppDispatch();
  const [isRepairItemsOpen, setRepairItemsOpen] = React.useState(false);
  const [isWorkItemsOpen, setIsWorkItemsOpen] = React.useState(false);
  const [externalId, setExternalId] = React.useState<string | undefined>(
    undefined
  );
  const [paymentStep, setPaymentStep] =
    React.useState<ServiceOrderPaymentStepCode>(
      ServiceOrderPaymentStepCode.PayNow
    );
  const [temporalSelectionId, setTemporalSelectionId] = React.useState<
    string | undefined
  >(undefined);
  const [openProfileFormModal, setProfileFormModalOpen] = React.useState(false);
  const [editCustomerProfileHidden, setEditCustomerProfileHidden] =
    React.useState(true);
  const [openPromptModal, setOpenPromptModal] = React.useState(false);
  const [orderWarrantyInteraction, setOrderWarrantyInteraction] =
    React.useState(false);
  const [repairStep, setRepairStep] = React.useState(OrderRepairStep.Selection);
  const [snackbarState, setSnackbarState] = React.useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });
  const [
    useSuggestedProductInRealtimeReplacement,
    setUseSuggestedProductInRealtimeReplacement
  ] = React.useState(false);
  const [isCustomerNotFound, setIsCustomerNotFound] = React.useState(false);
  const [isAccountNotFound, setIsAccountNotFound] = React.useState(false);
  const [isServiceCartSidebarOpened, setServiceCartSidebarOpen] =
    React.useState(false);
  const [isOrderSummaryOpened, setIsOrderSummaryOpened] = React.useState(false);
  const [isCartIconClicked, setCartIconClicked] = React.useState(true);
  const [customerPreferences, setCustomerPreferences] =
    React.useState<CustomerPreferences>();
  const [customerAddresses, setCustomerAddresses] = React.useState<
    CustomerAddress[]
  >([]);
  const [orderSummary, setOrderSummary] = React.useState<ServiceCartProduct[]>(
    []
  );

  const branchSelected = useAppSelector(selectBranch);
  const selectedHub = useAppSelector(selectHub);
  const customerSelected = useAppSelector(
    (state) => state.customer.selectedCustomer
  );
  const serviceCategoriesData = useAppSelector(
    (state) => state.repairPage.serviceCategoriesData
  );
  const customerSelectedReference = React.useRef<Customer>();

  const [selectProductStrategy, setSelectProductStrategy] =
    React.useState<SelectProductStrategies>(
      SelectProductStrategies.MainProduct
    );

  const [currentSelectedProduct, setCurrentSelectedProduct] =
    React.useState('');
  const [shouldFetchCustomerPreferences, setShouldFetchCustomerPreferences] =
    React.useState(false);
  const [isCustomerComplete, setIsCustomerComplete] = React.useState<
    boolean | undefined
  >(undefined);
  const [currencyType, setCurrencyType] = React.useState<
    CurrencyType | undefined
  >();

  const [customerPreferencesQuery] = useLazyGetCustomerQuery();
  const [getCustomerAddresses] = useLazyGetUserAddressesQuery();
  const [createOrderQuery, { isLoading: isCreateOrderQueryLoading }] =
    useCreateServiceOrderGroupMutation();
  const [
    createOrderQueryRealTime,
    { isLoading: isCreateOrderRealTimeQueryLoading }
  ] = useCreateServiceOrderGroupRealTimeMutation();

  const [
    createServiceOrderCharges,
    { isLoading: isCreateOrderChargesLoading }
  ] = useCreateServiceOrderChargesMutation();

  const [uploadProofOfPurchaseMutation] = useUploadProofOfPurchaseMutation();

  const [searchServiceOrderGroup] = useLazyGetServiceOrderGroupQuery();

  const [realTimeServiceType, setRealTimeServiceType] =
    React.useState<RealTimeServiceTypes>(RealTimeServiceTypes.Replace);

  const [getCategoriesTrigger] = useLazyGetCategoriesQuery();

  const [createAddressMutation] = useCreateAddressMutation();

  const [createOrderGroupQuery] = useCreateServiceOrderGroupSessionMutation();

  const [
    searchServiceOrderPricing,
    { isLoading: isLoadingPricing, isError: isErrorSearchServiceOrderPricing }
  ] = useLazyGetServiceOrderPricingQuery();
  const [isLoadingCheckout, setIsLoadingCheckout] = useState(false);
  const [realtimeCheckoutIsStarted, setRealtimeCheckoutIsStarted] =
    useState(false);
  const [showPricingErrorToast, setShowPricingErrorToast] = useState(false);

  const [updateServiceOrdersPickup] = useUpdateServiceOrdersPickupMutation();

  const getCategoriesData = React.useCallback(async () => {
    const response = await getCategoriesTrigger();

    if (response.data && response.data.categories.length > 0) {
      const data = response.data.categories.map((x) => {
        return { id: x.code, value: x.description };
      });

      dispatch(onServiceCategoriesDataChange(data));
    }
  }, [dispatch, getCategoriesTrigger]);

  const savedOrderGroups = useAppSelector(
    selectedOrderGroups,
    orderGroupsStateEqual
  );

  const serviceOrderState = useAppSelector(selectedServiceOrderState);
  const pricingTax = serviceOrderState.pricingTax;
  const { calculatePaymentMethodsRealTime } = useCalculatePaymentMethods();
  const { customerAccount } = useAppSelector(selectedRepairPageState);

  const location = React.useMemo(
    () => (hasHubSelection ? selectedHub : branchSelected),
    [hasHubSelection, branchSelected, selectedHub]
  );
  const skus = React.useMemo(
    () => [
      ...serviceOrderState.serviceCartProducts.map((product) => product.sku),
      ...serviceOrderState.selectedProducts.map((x) => x.sku)
    ],
    [serviceOrderState.serviceCartProducts, serviceOrderState.selectedProducts]
  );

  useFetchLightingMaxRepairPricing({
    countryCode: location?.countryCode,
    skus: skus
  });

  React.useEffect(() => {
    const handleUpdate = (update: {
      id: string;
      serviceRequestNumber: string;
    }) => {
      setOrderSummary((prev) =>
        prev.map((x) =>
          x.serviceOrderId === update.id
            ? {
                ...x,
                serviceRequestNumber: update.serviceRequestNumber
              }
            : x
        )
      );
    };

    const initializeSignalR = async (serviceRequestId: string) => {
      if (!isConnected) {
        try {
          await connect();
        } catch (error) {
          console.error('Error connecting to SignalR', error);
        }
      }

      addListener(signalrMethod, handleUpdate);
      try {
        await joinServiceRequestGroup(serviceRequestId);
      } catch (err) {
        console.error('Error joining SignalR group:', err);
      }
    };

    orderSummary.forEach((x) => initializeSignalR(x.serviceOrderId!));

    return () => {
      removeListener(signalrMethod, handleUpdate);
      disconnect();
    };
  }, [
    addListener,
    connect,
    disconnect,
    isConnected,
    joinServiceRequestGroup,
    orderSummary,
    removeListener
  ]);

  const handleRepairClick = React.useCallback(() => {
    setRepairStep(OrderRepairStep.Selection);
  }, []);

  const openOrderGroup = React.useMemo(
    () => savedOrderGroups.find((x) => x.isOpen),
    [savedOrderGroups]
  );

  const { getCustomerAccount } = useGetAccount();

  const [returnLocationIsCompleted, setReturnLocationIsCompleted] =
    useState(false);

  const serviceCartProducts = React.useMemo(() => {
    return openOrderGroup?.serviceCartProducts ?? [];
  }, [openOrderGroup]);

  let selectedProducts = React.useMemo(() => {
    return openOrderGroup?.selectedProducts ?? [];
  }, [openOrderGroup?.selectedProducts]);

  const isDeniedWarrantyForRealtime = React.useMemo(() => {
    return serviceCartProducts.some(
      (product) =>
        product.workOrderItems?.some(
          (woItem) => woItem.categoryCode === CategoryCode.Chargeable
        ) || (pricingTax?.total ?? 0) > 0
    );
  }, [serviceCartProducts, pricingTax]);

  const [serviceOrderGroupTempId, setServiceOrderGroupTempId] = React.useState<
    string | undefined
  >(openOrderGroup?.tempId);

  const [serviceOrderGroupId, setServiceOrderGroupId] = React.useState<
    string | undefined
  >();

  const [realTimeServiceOrders, setRealTimeServiceOrders] = React.useState<
    string[]
  >([]);

  const [group, setGroup] = React.useState<GroupResponse | undefined>();

  const repairAdjProduct = useGetProductBySkuQuery(LMR_ADJUSMENT_SKU);

  React.useEffect(() => {
    const createServiceOrderGroupSession = async () => {
      const request: CreateOrderGroupRequest = {
        branchId: branchSelected?.id ?? selectedHub?.id,
        customerId: customerSelected!.userId,
        isTaxExempt: isTaxExempt,
        serviceOrders: []
      };

      const response = await createOrderGroupQuery(request).unwrap();
      setServiceOrderGroupId(response?.id);
    };

    if (isToteOrder && customerSelected?.userId) {
      createServiceOrderGroupSession();
    }
  }, [
    branchSelected?.id,
    createOrderGroupQuery,
    customerId,
    customerSelected,
    dropOffParty,
    isTaxExempt,
    isToteOrder,
    selectedHub?.id
  ]);

  React.useEffect(() => {
    if (!openOrderGroup) {
      setServiceOrderGroupTempId(undefined);
    }
  }, [openOrderGroup]);

  const handleProfileModalChange = (value: boolean) => {
    setProfileFormModalOpen(value);
  };

  useEffect(() => {
    const getCustomerAccountData = async () => {
      const account = await getCustomerAccount(
        accountId,
        isTaxExempt,
        searchPostalCode,
        branchSelected,
        currentSite
      );

      if (account) {
        dispatch(onCustomerAccountChange(account));
      }
    };

    if (!customerAccount) {
      getCustomerAccountData();
    }
  }, [
    accountId,
    isTaxExempt,
    searchPostalCode,
    currentSite,
    dispatch,
    getCustomerAccount,
    branchSelected,
    customerAccount
  ]);

  useEffect(() => {
    if (!serviceCategoriesData.length) {
      getCategoriesData();
    }
  }, [getCategoriesData, serviceCategoriesData]);

  useEffect(() => {
    if (customerSelected) {
      customerSelectedReference.current = customerSelected;
    }
  }, [customerSelected]);

  useEffect(() => {
    if (customerSelected === undefined) return;

    const isComplete = checkIfIsCustomerComplete(
      customerSelected?.firstName,
      customerSelected?.lastName,
      customerSelected?.phoneNumber,
      customerSelected?.emailAddress,
      customerSelected?.address
    );

    setIsCustomerComplete(isComplete);
  }, [customerSelected]);

  useEffect(() => {
    if (isCustomerComplete === false) handleEditCustomerProfile(true);
  }, [isCustomerComplete]);

  useEffect(() => {
    let selectedLocation: Location | undefined;

    if (hasHubSelection && selectedHub) {
      selectedLocation = selectedHub as Location;
    } else if (!hasHubSelection && branchSelected) {
      selectedLocation = branchSelected as Location;
    }

    const currency: CurrencyType = selectedLocation
      ? getCurrencyType(selectedLocation.countryCode)
      : CurrencyType.Usd;
    setCurrencyType(currency);
  }, [branchSelected, hasHubSelection, selectedHub]);

  const handleEditCustomerProfile = (value: boolean) => {
    setEditCustomerProfileHidden(!value);
    setShouldFetchCustomerPreferences(true);
  };
  const handlePromptClose = () => {
    setOpenPromptModal(false);
  };

  useEffect(() => {
    if (currentSite && (showContactDetailsModal || customerProfileSelected)) {
      // stop showing modal
      dispatch(onShowContactDetailsModalChange(false));
      // clear customer profile selected for account flow
      dispatch(clearSelectedCustomer());
    }
  }, [currentSite, showContactDetailsModal, customerProfileSelected, dispatch]);

  useEffect(() => {
    if (
      isServiceCartSidebarOpened &&
      forceRealtimeService.current() &&
      isDeniedWarrantyForRealtime &&
      serviceCartProducts &&
      serviceCartProducts.length > 0 &&
      customerAccount
    ) {
      dispatch(
        onPaymentMethodsChange(calculatePaymentMethodsRealTime(customerAccount))
      );
    }
  }, [
    calculatePaymentMethodsRealTime,
    dispatch,
    isServiceCartSidebarOpened,
    isDeniedWarrantyForRealtime,
    serviceCartProducts,
    customerAccount
  ]);

  React.useEffect(() => {
    if (isErrorSearchServiceOrderPricing) {
      setShowPricingErrorToast(true);
    }
  }, [isErrorSearchServiceOrderPricing]);

  const handlePromptOpen = () => {
    if (isToteOrder) {
      setOpenPromptModal(true);
      return;
    }

    if (
      orderWarrantyInteraction &&
      (selectedProducts.length > 0 || serviceCartProducts.length > 0)
    ) {
      setOpenPromptModal(true);
    } else if (currentSite) {
      // reset repair item flow
      handleProductSelection();
      dispatch(clearSelectedProducts());

      router.back();
      return;
    } else {
      dispatch(setSelectedCustomer(undefined));
      router.push(returnTo);
    }
  };

  const user = useLoggedUser();

  const handleOrderSubmit = React.useCallback(async () => {
    const selectedLocationId = branchSelected?.id ?? selectedHub?.id;

    if (
      !isServiceCartSidebarOpened ||
      serviceCartProducts.length === 0 ||
      !selectedLocationId ||
      !customerSelectedReference.current
    ) {
      return;
    }

    const serviceOrders = serviceCartProducts.map(async (product) => {
      const serviceCartProduct = serviceOrderState.serviceCartProducts.find(
        (productV2) => productV2.sku === product.sku
      )!;
      const item: CreateOrderRequest = {
        sku: product.sku,
        serialNumber: product.serialNumber.code,
        isSerialNumberUnreadable: product.serialNumber.isUnreadable,
        problemIsNotProvided: product.problemIsNotProvided,
        purchaseDate: dayjs.isDayjs(product.proofOfPurchase.purchaseDate)
          ? product.proofOfPurchase.purchaseDate.format('YYYY/MM/DD')
          : undefined,
        problems: product.problems.map((problem) => problem.id),
        otherProblem: product.extraProblem,
        inclusions: product.inclusions.map((inclusion) => inclusion.id),
        otherInclusion: product.extraInclusion,
        toolNickname: serviceCartProduct.toolNickName
          ? serviceCartProduct.toolNickName.trim().length > 0
            ? serviceCartProduct.toolNickName
            : undefined
          : undefined,
        repairLocationId:
          serviceCartProduct.repairLocationId !== undefined &&
          serviceCartProduct.repairLocationId !== null
            ? serviceCartProduct.repairLocationId
            : selectedLocationId,
        shelfLocation: serviceCartProduct.shelfLocation,
        masterTrackingNumber: serviceCartProduct.masterTrackingNumber,
        purchaseOrder: serviceCartProduct.purchaseOrder,
        shipToSiteNumber: serviceCartProduct.shipToSiteNumber
          ? serviceCartProduct.shipToSiteNumber.trim().length > 0
            ? serviceCartProduct.shipToSiteNumber
            : undefined
          : undefined,
        deliveryTypeId:
          serviceCartProduct.deliveryTypeId === DeliveryType.ShipTo // Ship To is a UI only DeliveryType. In case it is selected, DeliveryTypeId ShipToCustomer (2) will be pass
            ? DeliveryType.DropShip
            : serviceCartProduct.deliveryTypeId,
        accountAuthorizationNumber:
          serviceCartProduct.accountAuthorizationNumber,
        dropShipAddressId:
          serviceCartProduct.deliveryTypeId === DeliveryType.DropShip
            ? serviceCartProduct.dropShipAddressId
            : undefined,
        laborAmount: serviceCartProduct.laborAmount
      };

      if (product.proofOfPurchase.receiptFile) {
        const params = new FormData();
        params.append('File', product.proofOfPurchase.receiptFile);
        const uploadFileResponse =
          await uploadProofOfPurchaseMutation(params).unwrap();
        item.proofOfPurchaseBlobPath =
          uploadFileResponse.proofOfPurchase.fileName;
      }

      if (product.repairLines?.resolutionId) {
        item.deniedWarrantyRepairLine = product.repairLines;
      }
      if (product.realTimeReplacementProduct) {
        item.serviceRequestReplacement = {
          sku: product.realTimeReplacementProduct?.sku,
          subInventory: 'Stock',
          serialNumber:
            product.realTimeReplacementProduct?.serialNumber?.code ?? '',
          reason: product.realTimeReplacementProduct?.replacementReason ?? '',
          serviceCategory:
            product.realTimeReplacementProduct?.serviceCategoryDescription ?? ''
        };
      }

      return item;
    });

    const request: CreateOrderGroupRequest = {
      serviceOrderGroupId: serviceOrderGroupId,
      branchId: selectedLocationId,
      customerId: customerSelectedReference.current!.userId,
      dropOffParty: dropOffParty,
      isTaxExempt: isTaxExempt,
      serviceOrders: await Promise.all(serviceOrders)
    };

    if (isTaxExempt && serviceOrderState.taxExemptCertificateFile) {
      const blobPath = await handleSubmit(
        BlobContainer.TaxExemptCertificate,
        serviceOrderState.taxExemptCertificateFile
      );

      if (blobPath) {
        request.taxExemptCertificateBlobPath = blobPath;
      }
    }

    let groupId: string;

    try {
      await createOrderQuery(request)
        .unwrap()
        .then((response) => {
          groupId = response.id;
          setExternalId(
            response.serviceOrders?.[response.serviceOrders.length - 1]?.id
          );
          const serviceOrderUpdates = serviceCartProducts.map(
            (x) =>
              ({
                ...x,
                serviceOrderId: response.serviceOrders[0].id,
                serviceRequestNumber:
                  response.serviceOrders[0].serviceRequestNumber
              }) as ServiceCartProduct
          );
          setOrderSummary((prev) => [...(prev ?? []), ...serviceOrderUpdates]);
        });
    } catch {
      setSnackbarState({
        open: true,
        message: t('features.order.serviceOrderNotCreatedWarning'),
        severity: 'error'
      });
    }

    const pushGoogleTagManagerLayer = () => {
      const additionalData = {
        event: ActionPushTypeEvent.FunnelEngagementTracking,
        action_Type: ActionType.ChangeFocus,
        action_category: ActionCategory.SiteNavigation,
        action_content: ActionContent.OrderConfirmation,
        action_target: ActionTarget.End,
        action_parent: ActionParent.ReviewOrder,
        action_parent_detail: undefined,
        action_depth: ActionDepth.SubmitOrder,
        action_depth_context: ActionDepthContext.StepsQuantity,
        action_level: undefined,
        action_level_context: undefined,
        reference_id: groupId,
        product_sku: serviceCartProducts
          .map((product) => product.sku)
          .join('|'),

        userId: user?.localAccountId ?? undefined
      };
      pushTrackedGtmLayer(additionalData);
    };
    pushGoogleTagManagerLayer();

    setCartIconClicked(true);
    setServiceCartSidebarOpen(false);
    dispatch(clearSelectedProducts());
    dispatch(clearServiceCart());
    dispatch(clearOrderGroups());
    setRepairStep(OrderRepairStep.Confirmation);
    setServiceOrderGroupTempId(undefined);
    if (serviceOrderGroupTempId)
      dispatch(removeOrderGroup(serviceOrderGroupTempId));
  }, [
    branchSelected?.id,
    selectedHub?.id,
    isServiceCartSidebarOpened,
    serviceCartProducts,
    serviceOrderGroupId,
    dropOffParty,
    isTaxExempt,
    serviceOrderState.taxExemptCertificateFile,
    serviceOrderState.serviceCartProducts,
    createOrderQuery,
    dispatch,
    serviceOrderGroupTempId,
    uploadProofOfPurchaseMutation,
    handleSubmit,
    t,
    user?.localAccountId
  ]);

  const handleCloseSnackbar = () => {
    setSnackbarState({
      ...snackbarState,
      open: false
    });
  };

  const handleHasRepairLineValue = React.useCallback(
    (repairLine?: OrderRepairLine): boolean => repairLine !== undefined,
    []
  );

  const handleAddToCart = React.useCallback(
    (product: SelectedProduct, serviceCartProductId: string) => {
      if (selectedProducts.length == 1) {
        setRepairStep(OrderRepairStep.PreCheckout);
      }

      const hasDeniedWarrantyManual = handleHasRepairLineValue(
        product.repairLines
      );

      setCartIconClicked(true);
      setServiceCartSidebarOpen(true);
      const productsWithoutExistingProduct = serviceCartProducts.filter(
        (existingProduct) =>
          existingProduct.serviceCartProductId !== product.serviceCartProductId
      );

      dispatch(
        updateOrderGroup({
          tempId: serviceOrderGroupTempId,
          isOpen: true,
          serviceCartProducts: [
            ...productsWithoutExistingProduct,
            {
              ...product,
              repairLines: product.repairLines
                ? {
                    ...product.repairLines,
                    repairTypeId: repairTypeMapping(
                      hasDeniedWarrantyManual,
                      product.repairLines.diagnosisCodeId,
                      product.repairLines.repairTypeId
                    )
                  }
                : undefined,
              serviceCartProductId: serviceCartProductId
            }
          ]
        })
      );
    },
    [
      dispatch,
      handleHasRepairLineValue,
      selectedProducts.length,
      serviceCartProducts,
      serviceOrderGroupTempId
    ]
  );

  const repairTypeMapping = (
    hasDeniedWarrantyManual: boolean,
    diagnosisCodeId: number,
    repairTypeId?: number
  ): number => {
    if (forceRealtimeService.current() && repairTypeId) {
      return hasDeniedWarrantyManual ? repairTypeId : RepairTypeCode.Unknown;
    }
    return hasDeniedWarrantyManual ? diagnosisCodeId : RepairTypeCode.Unknown;
  };

  const handleRemoveFromCart = (serviceCartProductId: string) => {
    const serviceCartProductsUpdated = serviceCartProducts.filter(
      (product) => product.serviceCartProductId !== serviceCartProductId
    );
    setServiceOrderGroupTempId(undefined);
    dispatch(
      updateOrderGroup({
        tempId: serviceOrderGroupTempId,
        isOpen: true,
        serviceCartProducts: serviceCartProductsUpdated,
        selectedProducts: selectedProducts
      })
    );
  };

  const handleRealtimePaymentCompletion = useCallback(
    async (
      paidWithCash: boolean,
      orderPaymentState: OrderPaymentState
    ): Promise<boolean> => {
      try {
        const payload: UpdateServiceOrdersPickupRequest = {
          paymentMethodId:
            orderPaymentState.paymentMethodId ?? PaymentMethodCode.Unknown,
          userId: customerSelectedReference.current!.userId,
          trapReferenceNumber: orderPaymentState.trapReferenceNumber,
          cashCollected: paidWithCash
            ? orderPaymentState.cashCollected
            : undefined,
          paymentTerms: orderPaymentState.paymentTerms,
          serviceOrderIds: realTimeServiceOrders,
          amountDue: orderPaymentState.amountDue,
          changeDue: orderPaymentState.changeDue,
          cardLast4: orderPaymentState.cardLast4,
          cardBrand: orderPaymentState.cardBrand,
          cardProcessedAt: orderPaymentState.cardProcessedAt,
          cardPaymentId: orderPaymentState.cardPaymentId,
          totalPrice: orderPaymentState.totalPrice,
          pickupParty: orderPaymentState.pickupParty,
          orders: realTimeServiceOrders.map((orderId) => {
            const payNowData = orderPaymentState.poNumbers?.find(
              (p) => p.orderId === orderId
            );
            const commonPayment = orderPaymentState.poNumbers?.[0] ?? null;

            return {
              orderId,
              serviceOrderNumber: payNowData?.serviceOrderNumber,
              poNumber: commonPayment?.poNumber,
              accountAuthorizationNumber:
                commonPayment?.accountAuthorizationNumber
            };
          }),
          isRealtimeOrder: true
        };

        await updateServiceOrdersPickup(payload).unwrap();
        if (paidWithCash) {
          setSnackbarState({
            open: true,
            message: t('features.repair.realtime.changeDue', {
              change: orderPaymentState.changeDue?.toFixed(2)
            }),
            severity: 'success',
            sx: { color: 'success.700', fontWeight: '600' },
            autoHideDuration: null
          });
        }

        return true;
      } catch (error) {
        // Handle API call error
        setSnackbarState({
          open: true,
          message: t('features.repair.realtime.pickupUpdateError'),
          severity: 'error'
        });

        return false;
      }
    },
    [realTimeServiceOrders, t, updateServiceOrdersPickup]
  );

  const handleCheckoutButtonClick = React.useCallback(async () => {
    setCartIconClicked(false);
    setServiceCartSidebarOpen(true);

    if (forceRealtimeService.current()) {
      const selectedLocationId = branchSelected?.id ?? selectedHub?.id;

      if (realTimeServiceOrders.length > 0) {
        return;
      }
      setIsLoadingCheckout(true);
      setRealtimeCheckoutIsStarted(true);
      dispatch(setIsRealtimeService(true));

      const serviceOrders = serviceCartProducts.map(async (product) => {
        const serviceCartProduct = serviceOrderState.serviceCartProducts.find(
          (productV2) => productV2.sku === product.sku
        )!;
        const item: CreateOrderRequest = {
          sku: product.sku,
          serialNumber: product.serialNumber.code,
          isSerialNumberUnreadable: product.serialNumber.isUnreadable,
          problemIsNotProvided: product.problemIsNotProvided,
          purchaseDate: dayjs.isDayjs(product.proofOfPurchase.purchaseDate)
            ? product.proofOfPurchase.purchaseDate.format('YYYY/MM/DD')
            : undefined,
          problems: product.problems.map((problem) => problem.id),
          otherProblem: product.extraProblem,
          inclusions: product.inclusions.map((inclusion) => inclusion.id),
          otherInclusion: product.extraInclusion,
          toolNickname: serviceCartProduct?.toolNickName
            ? serviceCartProduct.toolNickName.trim().length > 0
              ? serviceCartProduct.toolNickName
              : undefined
            : undefined,
          repairLocationId:
            serviceCartProduct?.repairLocationId ?? selectedLocationId,
          shelfLocation: serviceCartProduct.shelfLocation,
          masterTrackingNumber: serviceCartProduct.masterTrackingNumber,
          purchaseOrder: serviceCartProduct.purchaseOrder,
          shipToSiteNumber: serviceCartProduct.shipToSiteNumber
            ? serviceCartProduct.shipToSiteNumber.trim().length > 0
              ? serviceCartProduct.shipToSiteNumber
              : undefined
            : undefined,
          deliveryTypeId: DeliveryType.WillCall,
          accountAuthorizationNumber:
            serviceCartProduct.accountAuthorizationNumber,
          dropShipAddressId: undefined
        };

        if (product.proofOfPurchase.receiptFile) {
          const params = new FormData();
          params.append('File', product.proofOfPurchase.receiptFile);
          const uploadFileResponse =
            await uploadProofOfPurchaseMutation(params).unwrap();
          item.proofOfPurchaseBlobPath =
            uploadFileResponse.proofOfPurchase.fileName;
        }

        if (product.repairLines?.resolutionId) {
          item.deniedWarrantyRepairLine = product.repairLines;
        }
        if (product.realTimeReplacementProduct) {
          item.serviceRequestReplacement = {
            sku: product.realTimeReplacementProduct?.sku,
            subInventory: 'Stock',
            serialNumber:
              product.realTimeReplacementProduct?.serialNumber?.code ?? '',
            reason: product.realTimeReplacementProduct?.replacementReason ?? '',
            serviceCategory:
              product.realTimeReplacementProduct?.serviceCategoryDescription ??
              ''
          };
        }

        return item;
      });

      const request: CreateOrderGroupRequest = {
        serviceOrderGroupId: serviceOrderGroupId,
        branchId: selectedLocationId,
        customerId: customerSelectedReference.current!.userId,
        dropOffParty: dropOffParty,
        isTaxExempt: isTaxExempt,
        serviceOrders: await Promise.all(serviceOrders)
      };

      if (isTaxExempt && serviceOrderState.taxExemptCertificateFile) {
        const blobPath = await handleSubmit(
          BlobContainer.TaxExemptCertificate,
          serviceOrderState.taxExemptCertificateFile
        );

        if (blobPath) {
          request.taxExemptCertificateBlobPath = blobPath;
        }
      }

      const responseOrdersCreated =
        await createOrderQueryRealTime(request).unwrap();
      if (responseOrdersCreated != null) {
        const realTimeServiceOrdersIds =
          responseOrdersCreated.serviceOrders.map((x) => x.id);
        const groupedProducts = serviceCartProducts.reduce<
          Record<string, WorkOrderItem[][]>
        >((acc, product) => {
          if (!acc[product.sku]) acc[product.sku] = [];
          acc[product.sku].push(product.workOrderItems ?? []);
          return acc;
        }, {});

        for (const [sku, workOrderItemsGroups] of Object.entries(
          groupedProducts
        )) {
          const matchingOrders = responseOrdersCreated.serviceOrders.filter(
            (order) => order.sku === sku
          );
          const promises: Promise<void>[] = [];
          for (let i = 0; i < workOrderItemsGroups.length; i++) {
            const matchingOrder = matchingOrders[i];
            if (!matchingOrder) continue;

            const workOrderItems = workOrderItemsGroups[i];
            const createServiceOrderChargesRequest = {
              externalId: matchingOrder.id,
              serviceRequestCharges: workOrderItems.map(
                (workOrderItem: WorkOrderItem) => {
                  const isChargeable =
                    workOrderItem.categoryCode === 'MT_Chargeable' ||
                    workOrderItem.categoryCode === 'MT_LMR Adjustment';
                  return {
                    price: isChargeable ? workOrderItem.price : 0,
                    sku: workOrderItem.sku,
                    quantity: workOrderItem.quantity ?? 1,
                    uomCode: workOrderItem.product.unitOfMeasureCode ?? 'EA',
                    serviceActivity: workOrderItem.categoryCode
                      ? workOrderItem.categoryCode.replace(/^MT_/, '')
                      : '',
                    billingType: workOrderItem.billingType
                  };
                }
              )
            };

            promises.push(
              createServiceOrderCharges(
                createServiceOrderChargesRequest
              ).unwrap()
            );
          }
          await Promise.all(promises);
        }

        const orderGroupResponse = await searchServiceOrderGroup(
          responseOrdersCreated.id
        );

        if (orderGroupResponse.data) {
          setGroup(orderGroupResponse.data.serviceOrderGroup);
          setRealTimeServiceOrders(
            orderGroupResponse.data.serviceOrderGroup.serviceOrders.map(
              (x) => x.id
            )
          );
        }

        const taxResponse = await searchServiceOrderPricing({
          externalIds: realTimeServiceOrdersIds
        });

        if (taxResponse.data?.total === 0) {
          setPaymentStep(ServiceOrderPaymentStepCode.Confirmation);
        }
        dispatch(updatePricingTax(taxResponse.data));
        setIsLoadingCheckout(false);
      }
    }
  }, [
    branchSelected?.id,
    selectedHub?.id,
    serviceCartProducts,
    realTimeServiceOrders.length,
    serviceOrderGroupId,
    dropOffParty,
    isTaxExempt,
    serviceOrderState.taxExemptCertificateFile,
    serviceOrderState.serviceCartProducts,
    createOrderQueryRealTime,
    uploadProofOfPurchaseMutation,
    handleSubmit,
    searchServiceOrderGroup,
    searchServiceOrderPricing,
    dispatch,
    createServiceOrderCharges
  ]);

  const handleCartIconClick = () => {
    if (realtimeCheckoutIsStarted) {
      setCartIconClicked(false);
    } else {
      setCartIconClicked(true);
    }
    setServiceCartSidebarOpen(true);
  };

  const handleOrderSummaryIconClick = () => {
    setIsOrderSummaryOpened(true);
  };

  const handleSelectReplacementProduct = React.useCallback(
    (selectionId: string) => {
      setCurrentSelectedProduct(selectionId);
      setSelectProductStrategy(SelectProductStrategies.ReplacementProduct);
      setRepairItemsOpen(true);
    },
    []
  );

  const handleSelectRealTimeReplacementProduct = React.useCallback(
    (selectionId: string) => {
      setCurrentSelectedProduct(selectionId);
      setSelectProductStrategy(
        SelectProductStrategies.RealTimeReplacementProduct
      );
      setRepairItemsOpen(true);
    },
    []
  );

  const handleAddWorkOrderItemClick = React.useCallback(
    (selectionId: string) => {
      setTemporalSelectionId(selectionId);
      setIsWorkItemsOpen(true);
    },
    []
  );

  const RepairStepView = React.useCallback(
    (orderRepairStep: OrderRepairStep): JSX.Element => {
      switch (orderRepairStep) {
        case OrderRepairStep.Selection:
          return (
            <>
              <ProductCardContainer
                serviceOrderGroupTempId={serviceOrderGroupTempId}
                customerSelected={customerSelectedReference.current}
                branchSelected={branchSelected ?? selectedHub}
                hasWarrantyInteraction={orderWarrantyInteraction}
                onSnackbarStateChange={setSnackbarState}
                onOrderWarrantyInteraction={setOrderWarrantyInteraction}
                updateServiceOrderGroupTempId={setServiceOrderGroupTempId}
                onAddToCart={handleAddToCart}
                onSelectReplacementProduct={handleSelectReplacementProduct}
                onSelectRealTimeReplacementProduct={
                  handleSelectRealTimeReplacementProduct
                }
                hasRepairLineSomeValue={handleHasRepairLineValue}
                handleAddWorkOrderItemClick={handleAddWorkOrderItemClick}
                handleSetRealTimeServiceType={setRealTimeServiceType}
                forceRealtimeService={forceRealtimeService.current()}
                forceEnterRepairLines={forceEnterRepairLines.current()}
                currencyType={currencyType ?? CurrencyType.Usd}
                useSuggestedProductInRealtimeReplacement={
                  useSuggestedProductInRealtimeReplacement
                }
                lmrAdjustmentProduct={repairAdjProduct.data}
              />
              <NoItemsSelected
                onAddRepairItemClick={() => setRepairItemsOpen(true)}
              />
            </>
          );
        case OrderRepairStep.PreCheckout: {
          return (
            <PreCheckoutPage
              onAddRepairClick={() => setRepairItemsOpen(true)}
              onContinueToCheckoutClick={handleCheckoutButtonClick}
              disabledAddMoreItems={isToteOrder || realtimeCheckoutIsStarted}
              hideContinueToCheckoutButton={isRealTime}
            />
          );
        }
        case OrderRepairStep.Confirmation:
          return (
            <>
              <ConfirmationPage
                site={currentSite}
                onAddRepairClick={handleRepairClick}
                userId={customerSelectedReference.current!.userId}
                isToteOrder={isToteOrder}
                svNumber={orderSummary.slice(-1)[0]?.serviceRequestNumber}
                externalId={externalId}
              />
            </>
          );
        case OrderRepairStep.RealtimeComplete:
          return (
            <RealtimeComplete
              site={currentSite}
              onAddRepairClick={handleRepairClick}
              userId={customerSelectedReference.current!.userId}
              isToteOrder={isToteOrder}
              svNumber={orderSummary.slice(-1)[0]?.serviceRequestNumber}
              orderId={realTimeServiceOrders[0]}
            />
          );

        default:
          return <></>;
      }
    },
    [
      serviceOrderGroupTempId,
      branchSelected,
      selectedHub,
      orderWarrantyInteraction,
      handleAddToCart,
      handleSelectReplacementProduct,
      handleSelectRealTimeReplacementProduct,
      handleHasRepairLineValue,
      handleAddWorkOrderItemClick,
      currencyType,
      useSuggestedProductInRealtimeReplacement,
      currentSite,
      handleRepairClick,
      isToteOrder,
      orderSummary,
      handleCheckoutButtonClick,
      externalId,
      isRealTime,
      repairAdjProduct.data,
      realtimeCheckoutIsStarted,
      realTimeServiceOrders
    ]
  );

  const RepairStepHeader = (): JSX.Element => {
    switch (repairStep) {
      case OrderRepairStep.Selection:
      case OrderRepairStep.Confirmation:
      case OrderRepairStep.PreCheckout:
        return (
          <>
            <RepairHeader
              forceRealtimeService={forceRealtimeService.current()}
            />
          </>
        );
      default:
        return <></>;
    }
  };

  React.useEffect(() => {
    if (redirectToHome) {
      router.push('/');
      dispatch(setRedirectToHome(false));
    }
  }, [redirectToHome, router, dispatch]);

  React.useEffect(() => {
    dispatch(initRealTimeServiceChange());
  }, [dispatch]);

  React.useEffect(() => {
    // For when a redirection is coming from customer profile
    if (customerData?.users && customerData.users.length > 0) {
      dispatch(setSelectedCustomer(customerData.users[0]));
    }
  }, [dispatch, customerData?.users]);

  React.useEffect(() => {
    if (!customerSelected) return;
    // For when the customer is loaded up

    const fetchCustomerPreferences = async (id: string) => {
      const preferencesResponse = await customerPreferencesQuery(id);
      setCustomerPreferences(preferencesResponse.data);
    };
    const fetchCustomerAddresses = async (id: string) => {
      const addresses = await getCustomerAddresses(
        id,
        !customerSelected.isUpdated
      );
      setCustomerAddresses(addresses.data?.addresses ?? []);
    };

    fetchCustomerAddresses(customerSelected.userId);
    if (shouldFetchCustomerPreferences || !customerPreferences) {
      fetchCustomerPreferences(customerSelected.userId);
      setShouldFetchCustomerPreferences(false);
    }
  }, [
    customerSelected,
    customerPreferences,
    shouldFetchCustomerPreferences,
    customerPreferencesQuery,
    getCustomerAddresses
  ]);

  useEffect(() => {
    const allWillCallHasReturnLocation =
      serviceOrderState.serviceCartProducts
        .filter((product) => product.deliveryTypeId === DeliveryType.WillCall)
        .every((x) => x.returnLocationId != undefined) ?? false;
    const allDropshipHasDropshipLocation =
      serviceOrderState.serviceCartProducts
        .filter((product) => product.deliveryTypeId === DeliveryType.DropShip)
        .every((x) => x.dropShipAddressId != undefined) ?? false;

    const isComplete =
      allWillCallHasReturnLocation && allDropshipHasDropshipLocation;

    setReturnLocationIsCompleted((prev) => {
      if (prev === isComplete) return prev;
      return isComplete;
    });
  }, [serviceOrderState?.serviceCartProducts]);

  const toggleDrawer =
    (open: boolean) => (event: React.KeyboardEvent | React.MouseEvent) => {
      if (
        event &&
        event.type === 'keydown' &&
        ((event as React.KeyboardEvent).key === 'Tab' ||
          (event as React.KeyboardEvent).key === 'Shift')
      ) {
        return;
      }
      setServiceCartSidebarOpen(open);
    };

  const handleProductSelection = (product?: SelectedProduct) => {
    setRepairItemsOpen(false);
    setRepairStep(OrderRepairStep.Selection);
    setSelectProductStrategy(SelectProductStrategies.MainProduct);
    if (product) {
      const updatedSelectedProducts = [...selectedProducts, product];
      selectedProducts = [...selectedProducts, product];
      handleServiceOrderGroupUpsert(updatedSelectedProducts);

      if (isRealTime && realTimeServiceType === RealTimeServiceTypes.Replace) {
        setUseSuggestedProductInRealtimeReplacement(true);
      }
    }
  };

  const handleServiceOrderGroupCreate = React.useCallback(
    (selectedProductsParameter?: SelectedProduct[]) => {
      const timestamp = new Date().getTime();
      const tempId = `${branchSelected?.id}-${customerSelected?.userId}-${timestamp}`;
      setServiceOrderGroupTempId(tempId);

      const loadingOrderGroup = savedOrderGroups.find(
        (x) => x.tempId === tempId
      );

      if (!loadingOrderGroup) {
        dispatch(
          addOrderGroup({
            tempId: tempId,
            createdDate: dayjs(),
            customerId: customerSelected?.userId,
            branchId: branchSelected?.id,
            isOpen: true,
            selectedProducts: selectedProductsParameter ?? selectedProducts,
            serviceCartProducts: serviceCartProducts,
            forceRealtimeService: forceRealtimeService.current()
          })
        );
      }
    },
    [
      selectedProducts,
      serviceCartProducts,
      customerSelected?.userId,
      branchSelected?.id,
      dispatch,
      setServiceOrderGroupTempId,
      savedOrderGroups
    ]
  );

  const handleServiceOrderGroupUpdate = React.useCallback(
    (selectedProductsParameter?: SelectedProduct[]) => {
      dispatch(
        updateOrderGroup({
          tempId: serviceOrderGroupTempId,
          isOpen: true,
          selectedProducts: selectedProductsParameter ?? selectedProducts,
          serviceCartProducts: serviceCartProducts,
          forceRealtimeService: forceRealtimeService.current()
        })
      );
    },
    [selectedProducts, serviceOrderGroupTempId, serviceCartProducts, dispatch]
  );
  const handleServiceOrderGroupUpsert = React.useCallback(
    (selectedProductsParameter?: SelectedProduct[]) => {
      if (!serviceOrderGroupTempId) {
        handleServiceOrderGroupCreate(selectedProductsParameter);
      } else {
        handleServiceOrderGroupUpdate(selectedProductsParameter);
      }
    },
    [
      serviceOrderGroupTempId,
      handleServiceOrderGroupCreate,
      handleServiceOrderGroupUpdate
    ]
  );

  const handleAccountNotFound = (value: boolean) => {
    setIsAccountNotFound(value);
  };

  const handleWorkItemsBackButtonClick = () => {
    setIsWorkItemsOpen(false);
    setTemporalSelectionId(undefined);
  };

  const handleAttachWorkOrderItems = (workOrderItems: WorkOrderItem[]) => {
    setIsWorkItemsOpen(false);
    if (temporalSelectionId === undefined) return;

    dispatch(
      handleWorkItemsChange({
        selectionId: temporalSelectionId,
        workOrderItems: workOrderItems
      })
    );
  };

  if (redirectToHome) {
    router.push('/');
  }

  const handleAdditionalAddress = async (address: ReturnLocationAddress) => {
    if (!customerSelected) return '';
    const response = await createAddressMutation({
      ...address,
      country:
        statesData.find((x) =>
          x.states.some((y) => y.stateId === address.state)
        )?.countryCode || '',
      userId: customerSelected.userId
    }).unwrap();

    const succesfullyCreated = response?.data?.id?.length > 0;
    /**
     * Note: Due to concurrency issues, we manually update addresses rather than re-calling getCustomerAddresses from the backend.
     * When creating an address, propagating that new item to all BE services takes time.
     * Therefore, invoking that call immediately after the POST success may not retrieve the new address.
     */
    if (succesfullyCreated)
      setCustomerAddresses([
        ...customerAddresses,
        {
          addressLine1: address.address1!,
          city: address.city!,
          countryCode: address.country!,
          id: response.data.id,
          postalCode: address.postalCode!,
          state: address.state!,
          addressLine2: address.address2 ?? undefined,
          addressLine3: address.address3 ?? undefined
        }
      ]);

    return response.data.id;
  };

  const handleRealTimeOrderSubmit = React.useCallback(() => {
    dispatch((dispatch) => {
      dispatch(clearSelectedProducts());
      dispatch(clearServiceCart());
      dispatch(clearOrderGroups());
      dispatch(
        updatePricingTax({
          totalTax: 0,
          subTotal: 0,
          total: 0,
          calculateServiceOrderTaxesResponses: []
        })
      );
    });
    dispatch(clearRealtimeService());
    setServiceCartSidebarOpen(false);
    setRealtimeCheckoutIsStarted(false);
    setRepairStep(OrderRepairStep.RealtimeComplete);
  }, [dispatch]);

  const getNotificationText = (): JSX.Element => {
    const titleMessage = t('features.repair.realtime.checkoutWarningTitleText');
    const detailsMessage = t(
      'features.repair.realtime.checkoutWarningDetailsText'
    );
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Typography
          variant="p3"
          sx={{
            color: 'neutral.800',
            fontWeight: 600
          }}
        >
          {titleMessage}
        </Typography>
        <Typography
          variant="p3"
          sx={{
            color: 'neutral.800',
            fontWeight: 400
          }}
        >
          {detailsMessage}
        </Typography>
      </Box>
    );
  };

  return (
    <BranchesCheck pagePathRedirect="/serviceOrder/repair">
      <Snackbar
        open={showPricingErrorToast}
        message={t('features.order.ErrorPricingRequestMessage')}
        severity="error"
        handleClose={() => setShowPricingErrorToast(false)}
        autoHideDuration={null}
      />
      {!redirectToHome && (isRepairItemsOpen || isWorkItemsOpen) ? (
        <PageAreaBox
          pageAreaBoxName="ProductSearch"
          sx={{ maxWidth: '1460px' }}
        >
          {isRepairItemsOpen && (
            <RepairItemsContainer
              onProductSelected={handleProductSelection}
              selectProductStrategy={selectProductStrategy}
              currentSelectedProduct={currentSelectedProduct}
            />
          )}
          {isWorkItemsOpen && (
            <WorkItemsContainer
              selectionId={temporalSelectionId}
              onAttachWorkOrderItems={handleAttachWorkOrderItems}
              onBackButtonClick={handleWorkItemsBackButtonClick}
              onProductSelected={handleProductSelection}
              realTimeServiceType={realTimeServiceType}
              customerId={customerId ? customerId : openOrderGroup?.customerId}
              branchSelected={branchSelected ?? selectedHub}
              currencyType={currencyType ?? CurrencyType.Usd}
              isTaxExempt={isTaxExempt}
              siteNumber={customerAccount?.accountNumber ?? ''}
              lmrAdjustmentProduct={repairAdjProduct.data}
            />
          )}
        </PageAreaBox>
      ) : (
        !redirectToHome && (
          <>
            <PageHeaderContainer
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <RepairStepHeader />
              </Box>
              <Tooltip placement="bottom" title={t('common.close')}>
                <IconButton
                  onClick={() => handlePromptOpen()}
                  data-testid="closeBtn-testId"
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </PageHeaderContainer>
            <PageAreaBox
              sx={{ mt: 6, maxWidth: '1460px' }}
              pageAreaBoxName="OrderRepair"
            >
              <Loading
                isLoading={isFetching}
                fallbackContainerProps={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  zIndex: 1
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    pt: '36px'
                  }}
                >
                  {customerSelected ? (
                    <>
                      <CloseRepairPromptContainer
                        open={openPromptModal}
                        cancelButtonLabel={t('common.cancel')}
                        serviceOrderGroupTempId={serviceOrderGroupTempId}
                        onPromptClose={handlePromptClose}
                        isToteOrder={isToteOrder}
                        showAdvancedConfirm={!isRealTime}
                      />
                      <EditProfileModal
                        openModal={!editCustomerProfileHidden}
                        customerSelected={customerSelected}
                        onEditCustomerProfile={handleEditCustomerProfile}
                      />
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          gap: '16px',
                          borderRadius: '4px',
                          bgcolor: 'common.white',
                          border: '1px solid',
                          borderColor: 'neutral.300',
                          pr: '16px'
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '8px'
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              gap: '16px'
                            }}
                          >
                            <CustomerCard
                              customer={customerSelected}
                              customerPreferences={customerPreferences}
                              displayProfileIncomplete={!isCustomerComplete}
                              containerSxProps={{
                                py: 0,
                                pb: 2
                              }}
                              dropOffParty={dropOffParty}
                              isTaxExempt={isTaxExempt}
                            />
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2
                          }}
                        >
                          <Tooltip placement="top" title={t('common.edit')}>
                            <IconButton
                              data-testid="editCustomerBtn-testId"
                              onClick={() => handleEditCustomerProfile(true)}
                            >
                              <EditIconHeader
                                sx={{
                                  color: 'neutral.800',
                                  width: '24px',
                                  height: '20px'
                                }}
                                viewBox="-2 0 24 20"
                              />
                            </IconButton>
                          </Tooltip>
                          <CartIcon onCartIconClick={handleCartIconClick} />
                          {isToteOrder && orderSummary.length > 0 && (
                            <Tooltip
                              placement="top"
                              title={t('features.repair.orderSummary')}
                            >
                              <IconButton
                                data-testid="orderSummaryBtn-testId"
                                onClick={handleOrderSummaryIconClick}
                              >
                                <ListIcon
                                  sx={{
                                    color: 'neutral.800',
                                    width: '34px',
                                    height: '30px'
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </Box>
                      <Divider sx={{ color: 'neutral.300', mt: 2.5 }} />
                      {repairStep !== OrderRepairStep.PreCheckout && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mt: 3
                          }}
                        >
                          <RepairItemsCount step={repairStep} />
                          <Box
                            sx={{
                              display: 'flex',
                              gap: 2
                            }}
                          >
                            {repairStep === OrderRepairStep.Selection &&
                              !isToteOrder && (
                                <AddRepairItemButton
                                  onButtonClick={() => setRepairItemsOpen(true)}
                                />
                              )}

                            {!isRealTime && (
                              <CheckoutActionButtonContainer
                                step={repairStep}
                                onContinueToCheckoutClick={
                                  handleCheckoutButtonClick
                                }
                                serviceOrderGroupTempId={
                                  serviceOrderGroupTempId
                                }
                                isLoading={isCreateOrderQueryLoading}
                                onSubmitClick={handleOrderSubmit}
                              />
                            )}
                          </Box>
                        </Box>
                      )}
                      {RepairStepView(repairStep)}
                      <SwipeableDrawer
                        anchor="right"
                        open={
                          isServiceCartSidebarOpened &&
                          serviceCartProducts.length > 0
                        }
                        onClose={toggleDrawer(false)}
                        onOpen={toggleDrawer(true)}
                      >
                        <ServiceCartSidebarHeader
                          setServiceCartSidebarOpen={setServiceCartSidebarOpen}
                          isCartIconClicked={isCartIconClicked}
                        />
                        <Box
                          sx={{
                            width: isCartIconClicked ? '512px' : '100%',
                            px: isCartIconClicked ? '16px' : '32px',
                            mb: isRealTime ? '0' : '80px',
                            overflowX: 'hidden'
                          }}
                        >
                          <ServiceCartProductContainer
                            accountId={accountId}
                            isLoading={
                              isCreateOrderQueryLoading ||
                              isCreateOrderRealTimeQueryLoading ||
                              isCreateOrderChargesLoading ||
                              isLoadingPricing
                            }
                            serviceOrderGroupTempId={serviceOrderGroupTempId}
                            onRepairStepViewChange={setRepairStep}
                            onSubmitClick={handleOrderSubmit}
                            isCartIconClicked={isCartIconClicked}
                            setServiceCartSidebarOpen={
                              setServiceCartSidebarOpen
                            }
                            customerPreferences={customerPreferences}
                            customerSelected={customerSelected}
                            customerAddesses={customerAddresses}
                            onRemoveFromCart={handleRemoveFromCart}
                            branchSelected={branchSelected}
                            selectedHub={selectedHub}
                            hasHubSelection={hasHubSelection}
                            onAdditionalAddressSaveClick={
                              handleAdditionalAddress
                            }
                            currentSite={currentSite}
                            realtimeMode={
                              forceRealtimeService.current() ?? false
                            }
                            isToteOrder={isToteOrder}
                          />
                        </Box>
                        {forceRealtimeService.current() &&
                        !isCartIconClicked ? (
                          <Loading
                            isLoading={
                              isCreateOrderQueryLoading ||
                              isCreateOrderRealTimeQueryLoading ||
                              isCreateOrderChargesLoading ||
                              isLoadingPricing ||
                              isLoadingCheckout
                            }
                            fallbackContainerProps={{
                              height: '150px',
                              display: 'block',
                              margin: 'auto'
                            }}
                          >
                            <ServiceOrderPaymentDrawer
                              customer={customerSelected}
                              orderIds={realTimeServiceOrders}
                              onCompleteOrderClick={() =>
                                handleRealTimeOrderSubmit()
                              }
                              paymentStep={paymentStep}
                              group={group}
                              currencyType={currencyType!}
                              totalDue={pricingTax?.total ?? 0}
                              totalTaxDue={pricingTax?.totalTax ?? 0}
                              subTotalDue={pricingTax?.subTotal ?? 0}
                              onRealtimePaymentCompletion={
                                handleRealtimePaymentCompletion
                              }
                              isRealtimeMode
                              isInWarranty={!isDeniedWarrantyForRealtime}
                            />
                          </Loading>
                        ) : (
                          <Box
                            sx={{
                              width: '100%',
                              mt: '100%',
                              position: 'absolute',
                              p: forceRealtimeService.current()
                                ? '24px'
                                : '16px',
                              backgroundColor: 'common.white',
                              bottom: 0
                            }}
                          >
                            {forceRealtimeService.current() && (
                              <Alert
                                severity="warning"
                                icon={
                                  <WarningNotificationIcon
                                    sx={{
                                      color: 'neutral.800'
                                    }}
                                    viewBox="0 0 20 17"
                                  />
                                }
                                sx={{
                                  '& .MuiAlert-icon.MuiAlert-icon': {
                                    color: 'neutral.800',
                                    fontSize: '26px',
                                    mr: '10px'
                                  },
                                  '& .MuiAlert-message': {
                                    fontWeight: 700,
                                    fontSize: '18px'
                                  },
                                  '& .MuiAlert-action': {
                                    p: '0'
                                  },
                                  border: '1px solid',
                                  borderColor: 'warning.600',
                                  backgroundColor: 'warning.100',
                                  borderRadius: '4px',
                                  mb: '16px',
                                  padding: '7px 32px',
                                  alignItems: 'center'
                                }}
                              >
                                {getNotificationText()}
                              </Alert>
                            )}
                            {isCartIconClicked ? (
                              <Button
                                variant="primary"
                                sx={{
                                  width: '100%'
                                }}
                                onClick={handleCheckoutButtonClick}
                                data-testid="continueToCheckoutBtn-testId"
                                size={
                                  forceRealtimeService.current()
                                    ? 'large'
                                    : 'medium'
                                }
                              >
                                {t('features.repair.continueToCheckout')}
                              </Button>
                            ) : (
                              <Button
                                onClick={() => handleOrderSubmit()}
                                sx={{
                                  width: '100%'
                                }}
                                data-testid="submitOrder-testId"
                                disabled={
                                  isCreateOrderQueryLoading ||
                                  !isCustomerComplete ||
                                  !returnLocationIsCompleted
                                }
                              >
                                {t('features.repair.submitOrder')}
                              </Button>
                            )}
                          </Box>
                        )}
                      </SwipeableDrawer>
                      <ServiceOrderSummary
                        open={isOrderSummaryOpened}
                        setIsOpen={setIsOrderSummaryOpened}
                        serviceCartProducts={orderSummary}
                        customerPreferences={customerPreferences}
                        customerSelected={customerSelected}
                      />
                      <Snackbar
                        open={snackbarState.open}
                        message={snackbarState.message}
                        severity={snackbarState.severity}
                        handleClose={handleCloseSnackbar}
                        sx={snackbarState.sx}
                        autoHideDuration={snackbarState.autoHideDuration}
                      />
                    </>
                  ) : (
                    <Box>
                      <ProfileFormModal
                        open={openProfileFormModal}
                        onFormChange={handleProfileModalChange}
                        disableNonEditableFields={false}
                        saveButtonText={t('common.saveAndSelect')}
                      />
                      <SearchCustomerContainer
                        isViewMode={false}
                        isCustomerNotFound={isCustomerNotFound}
                        onCustomerNotFound={setIsCustomerNotFound}
                        selectedBranch={branchSelected}
                        selectedHub={selectedHub}
                        isAccountNotFound={isAccountNotFound}
                        onAccountNotFound={handleAccountNotFound}
                      />
                      {canCreateCustomer && isCustomerNotFound && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'end',
                            mt: '16px',
                            justifyContent: 'flex-end'
                          }}
                        >
                          <Button
                            data-testid="createProfileButtonRepair-testid"
                            size="medium"
                            variant="secondary"
                            startIcon={<PlusIcon viewBox="0 0 16 16" />}
                            onClick={() => setProfileFormModalOpen(true)}
                            disabled={!hasBranchSelection}
                          >
                            {t('features.home.mainButtons.createProfile')}
                          </Button>
                        </Box>
                      )}
                    </Box>
                  )}
                </Box>
              </Loading>
            </PageAreaBox>
          </>
        )
      )}
    </BranchesCheck>
  );
}
