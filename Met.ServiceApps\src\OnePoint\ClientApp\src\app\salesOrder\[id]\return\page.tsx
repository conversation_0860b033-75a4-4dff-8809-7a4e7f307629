'use client';

import { ArrowLeftIcon, CloseIcon } from '@/design/atoms';
import { PageHeaderContainer } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { LocationById, useLazyGetLocationByIdQuery } from '@/features/branch';
import { Product, useLazyGetProductBySkuQuery } from '@/features/product';
import {
  SalesOrderReturn,
  SalesOrderReturnContainer,
  useGetSalesOrderQuery,
  useLazyGetSalesOrderReturnsQuery
} from '@/features/salesOrder';
import { Box, CircularProgress, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface SalesOrderReturnPageProps {
  params: {
    id: string;
  };
}

export default function SalesOrderDetailsPage({
  params: { id }
}: SalesOrderReturnPageProps) {
  const { t } = useTranslation();
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  const {
    data: originalOrder,
    isLoading: isLoadingOrder,
    isError
  } = useGetSalesOrderQuery(id ?? '', { skip: !id });

  const [products, setProducts] = useState<Product[]>([]);
  const [fetchProduct, { isLoading: isLoadingProducts }] =
    useLazyGetProductBySkuQuery();
  const [location, setLocation] = useState<LocationById>();
  const [fetchLocation, { isFetching: isFetchingLocation }] =
    useLazyGetLocationByIdQuery();
  const [returns, setReturns] = useState<SalesOrderReturn[]>();
  const [fetchReturnOrders, { isFetching: isFetchingReturns }] =
    useLazyGetSalesOrderReturnsQuery();

  useEffect(() => {
    const fetchOrderProducts = async () => {
      if (!originalOrder) return;

      const products = await Promise.all(
        originalOrder?.salesOrder.items.map((i) => fetchProduct(i.sku))
      );

      setProducts(
        products.map((p) => p.data).filter((p) => p != null) as Product[]
      );
    };

    fetchOrderProducts();
  }, [originalOrder, isLoadingOrder, fetchProduct]);

  useEffect(() => {
    const fetchOrderLocation = async () => {
      if (!originalOrder?.salesOrder?.locationId) return;

      const location = await fetchLocation(originalOrder.salesOrder.locationId);

      setLocation(location.data);
    };

    fetchOrderLocation();
  }, [originalOrder, isLoadingOrder, fetchLocation]);

  useEffect(() => {
    const fetchReturns = async () => {
      if (!originalOrder) return;

      const returns = await fetchReturnOrders(originalOrder.salesOrder.id);

      setReturns(returns.data);
    };

    fetchReturns();
  }, [originalOrder, isLoadingOrder, fetchReturnOrders]);

  if (
    isLoadingOrder ||
    isLoadingProducts ||
    isFetchingLocation ||
    isFetchingReturns
  ) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (isError || !originalOrder || !location) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}
      >
        <Typography variant="h6">{t('common.pageNotFound')}</Typography>
      </Box>
    );
  }
  const handleRefreshReturns = async () => {
    if (!originalOrder) return;

    const returns = await fetchReturnOrders(originalOrder.salesOrder.id);
    setReturns(returns.data);
  };
  return (
    <>
      <PageHeaderContainer
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box
          data-testid="goBackToSalesOrderDetails-testId"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <>
            <IconButton
              data-testid="orderRepairHistory-testId"
              onClick={handleGoBack}
            >
              <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
            </IconButton>
            <Typography variant="h5" color="neutral.800" textAlign="center">
              {t('features.order.salesOrderReturn')}
            </Typography>
          </>
        </Box>
        <IconButton data-testid="closeBtn-testId" onClick={handleGoBack}>
          <CloseIcon />
        </IconButton>
      </PageHeaderContainer>
      <PageAreaBox
        pageAreaBoxName="salesOrderReturnDetails"
        sx={{
          height: '100%',
          bgcolor: 'background.paper',
          mt: '88px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <SalesOrderReturnContainer
          id={originalOrder.salesOrder.id}
          salesOrder={originalOrder.salesOrder}
          products={products}
          originalOrderLocation={location}
          returns={returns ?? []}
          onRefreshReturns={handleRefreshReturns}
        />
      </PageAreaBox>
    </>
  );
}
