import { PaletteColorOptions, PaletteOptions } from '@mui/material';
import {
  BrandVariables,
  PRIMARY,
  NEUTRAL,
  ERROR,
  WARNING,
  SUCCESS,
  COMMON,
  BA<PERSON><PERSON>GROUND,
  BRAND_VARIABLES
} from './colors';

declare module '@mui/material/styles' {
  interface PaletteColor {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  }

  interface Palette {
    neutral: PaletteColor;
    brandVariables: BrandVariables;
  }

  interface PaletteOptions {
    neutral?: PaletteColorOptions;
    brandVariables?: BrandVariables;
  }
}

export const LIGHT_PALETTE: PaletteOptions = {
  primary: PRIMARY,
  secondary: NEUTRAL,
  neutral: NEUTRAL,
  error: ERROR,
  warning: WARNING,
  info: NEUTRAL,
  success: SUCCESS,
  mode: 'light',
  common: COMMON,
  background: BACKGROUND,
  brandVariables: BRAND_VARIABLES
};

export const DARK_PALETTE: PaletteOptions = {
  ...LIGHT_PALETTE,
  mode: 'dark'
};
