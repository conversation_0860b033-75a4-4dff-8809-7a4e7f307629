"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-export-i18n";
exports.ids = ["vendor-chunks/next-export-i18n"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-export-i18n/index.js":
/*!************************************************!*\
  !*** ./node_modules/next-export-i18n/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar I18N = __webpack_require__(/*! ./../../i18n/index.js */ \"(ssr)/./i18n/index.js\");\nvar Mustache = __webpack_require__(/*! mustache */ \"(ssr)/./node_modules/mustache/mustache.js\");\nvar Link = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\nvar Mustache__default = /*#__PURE__*/ _interopDefaultLegacy(Mustache);\nvar Link__default = /*#__PURE__*/ _interopDefaultLegacy(Link);\nvar LanguageDataStore;\n(function(LanguageDataStore) {\n    LanguageDataStore[\"QUERY\"] = \"query\";\n    LanguageDataStore[\"LOCAL_STORAGE\"] = \"localStorage\";\n})(LanguageDataStore || (LanguageDataStore = {}));\n/**\r\n * The entry files for the separated hooks\r\n */ /**\r\n * Calculates the default language from the user's language setting and the i18n object.\r\n * In case there is a language set in the browser which is also available as translation,\r\n * override the default language setting in the config file.\r\n * @returns string indicating the default language to use\r\n */ const getDefaultLanguage = (userI18n)=>{\n    let browserLang = \"\";\n    if (userI18n.useBrowserDefault && \"undefined\" !== \"undefined\" && 0 && 0 && (0)) {}\n    if (userI18n.useBrowserDefault && browserLang && userI18n.translations[browserLang]) {\n        return browserLang;\n    }\n    return userI18n.defaultLang;\n};\n/**\r\n * Imports the translations addes by the user in \"i18n/index\",\r\n * veryfies the tranlsations and exposes them\r\n * to the custom hooks\r\n * @returns the translations and the default language as defined in \"i18n/index\"\r\n */ const i18n = ()=>{\n    // cast to be typsafe\n    const userI18n = I18N;\n    // Set \"query\" as default\n    if (!userI18n.languageDataStore) {\n        userI18n.languageDataStore = LanguageDataStore.QUERY;\n    }\n    if (Object.keys(userI18n.translations).length < 1) {\n        throw new Error(`Missing translations. Did you import and add the tranlations in 'i18n/index.js'?`);\n    }\n    if (userI18n.defaultLang.length === 0) {\n        throw new Error(`Missing default language. Did you set 'defaultLang' in 'i18n/index.js'?`);\n    }\n    if (!userI18n.translations[userI18n.defaultLang]) {\n        throw new Error(`Invalid default language '${userI18n.defaultLang}'. Check your 'defaultLang' in 'i18n/index.js'?`);\n    }\n    // if (!Object.values(LanguageDataStore).includes(userI18n.languageDataStore)) {\n    //   throw new Error(\n    //     `Invalid language detection '${userI18n.languageDataStore}'. Check your 'languageDataStore' in 'i18n/index.js'?`\n    //   );\n    // }\n    userI18n.defaultLang = getDefaultLanguage(userI18n);\n    return userI18n;\n};\n/**\r\n * Returns a react-state containing the currently selected language.\r\n * @returns [lang as string, setLang as SetStateAction] a react-state containing the currently selected language\r\n */ function useSelectedLanguage() {\n    const i18nObj = i18n();\n    const defaultLang = i18nObj.defaultLang;\n    const translations = i18nObj.translations;\n    const languageDataStore = i18nObj.languageDataStore;\n    const searchParams = navigation.useSearchParams();\n    const langParam = searchParams.get(\"lang\");\n    const [lang, setLang] = React.useState(defaultLang);\n    // set the language if the localStorage value has changed\n    const handleLocalStorageUpdate = ()=>{\n        const storedLang = window.localStorage.getItem(\"next-export-i18n-lang\");\n        if (languageDataStore === LanguageDataStore.LOCAL_STORAGE && storedLang && storedLang !== lang && translations && translations[storedLang]) {\n            setLang(storedLang);\n        }\n    };\n    // Listen for local-storage changes\n    React.useEffect(()=>{\n        handleLocalStorageUpdate();\n        document.addEventListener(\"localStorageLangChange\", ()=>{\n            handleLocalStorageUpdate();\n        });\n        return ()=>{\n            document.removeEventListener(\"localStorageLangChange\", handleLocalStorageUpdate);\n        };\n    }, [\n        handleLocalStorageUpdate\n    ]);\n    // set the language if the query parameter changes\n    React.useEffect(()=>{\n        const storedLang = langParam;\n        if (languageDataStore === LanguageDataStore.QUERY && storedLang && storedLang !== lang && translations && translations[storedLang]) {\n            setLang(storedLang);\n        }\n    }, [\n        lang,\n        langParam,\n        translations,\n        setLang\n    ]);\n    return {\n        lang,\n        setLang\n    };\n}\n/**\r\n * Returns a boolean react-state indicating if the current selected language equals the one passed to the hook.\r\n * @param currentLang string the language to check. Needs to equal the key in i18n/index.\r\n * @returns boolean react-state\r\n */ function useLanguageSwitcherIsActive(currentLang) {\n    const [isActive, setIsActive] = React.useState(false);\n    const i18nObj = i18n();\n    const searchParams = navigation.useSearchParams();\n    const langParam = searchParams.get(\"lang\");\n    const defaultLang = i18nObj.defaultLang;\n    const languageDataStore = i18nObj.languageDataStore;\n    React.useEffect(()=>{\n        if (languageDataStore === LanguageDataStore.QUERY) {\n            let current;\n            if (!langParam) {\n                current = defaultLang === currentLang;\n            } else {\n                current = langParam === currentLang;\n            }\n            setIsActive(current);\n        }\n    }, [\n        currentLang,\n        defaultLang,\n        langParam\n    ]);\n    const handleLocalStorageUpdate = ()=>{\n        if (languageDataStore === LanguageDataStore.LOCAL_STORAGE) {\n            let current;\n            const localStorageLanguage = window.localStorage.getItem(\"next-export-i18n-lang\");\n            current = defaultLang === currentLang;\n            if (localStorageLanguage) {\n                current = localStorageLanguage === currentLang;\n            }\n            setIsActive(current);\n        }\n    };\n    // Listen for local-storage changes\n    React.useEffect(()=>{\n        handleLocalStorageUpdate();\n        document?.addEventListener(\"localStorageLangChange\", ()=>{\n            handleLocalStorageUpdate();\n        });\n        return ()=>{\n            document?.removeEventListener(\"localStorageLangChange\", handleLocalStorageUpdate);\n        };\n    }, [\n        handleLocalStorageUpdate\n    ]);\n    return {\n        isActive\n    };\n}\n/**\r\n * Provides the t() function which returns the value stored for this given key (e.g. \"i18n.ui.headline\")\r\n * in the translation file.\r\n * The return value can be a string, a number, an array or an object.\r\n * In case there is no entry for this key, it returns the key.\r\n * @returns t(key: string): any function\r\n */ const useTranslation = ()=>{\n    let i18nObj;\n    i18nObj = i18n();\n    const translations = i18nObj.translations;\n    const { lang } = useSelectedLanguage();\n    return {\n        /**\r\n         * Returns the value stored for this given key (e.g. \"i18n.ui.headline\")  in the translation file.\r\n         * The return value can be a string, a number, an array or an object.\r\n         * In case there is no entry for this key, it returns the key.\r\n         * @param key the key for looking up the translation\r\n         * @param view the mustache view for interpolating the template string\r\n         * @returns the value stored for this key, could be a string, a number, an array or an object\r\n         */ t: (key, view)=>{\n            let value = key.split(\".\").reduce((previous, current)=>previous && previous[current] || null, translations[lang]);\n            let translation = value || key;\n            try {\n                return Mustache__default[\"default\"].render(translation, view);\n            } catch (e) {\n                return translation;\n            }\n        }\n    };\n};\n/**\r\n * Simple component for switching the language.\r\n * Set the \"lang\" query parameter on click while preserves the current query parameters\r\n * Style it using the\r\n * - [data-language-switcher]\r\n * - [data-is-current=\"true\"]\r\n *  attribute selectors or create your own component.\r\n * @param lang string the language to switch to. Needs to equal the key in i18n/index.\r\n * @param [children] React.nodes\r\n */ const LanguageSwitcher = ({ lang, children })=>{\n    // state indicating if this component's target language matches the currently selected\n    const { isActive: languageSwitcherIsActive } = useLanguageSwitcherIsActive(lang);\n    // necessary for updating the router's query parameter inside the click handler\n    const router = navigation.useRouter();\n    const pathname = navigation.usePathname();\n    const searchParams = navigation.useSearchParams();\n    const i18nObj = i18n();\n    const languageDataStore = i18nObj.languageDataStore;\n    const createQueryString = React.useCallback((name, value)=>{\n        const params = new URLSearchParams(searchParams.toString());\n        params.set(name, value);\n        return params.toString();\n    }, [\n        searchParams\n    ]);\n    /**\r\n     * Updates the router with the currently selected language\r\n     */ const handleLanguageChange = ()=>{\n        if (languageDataStore === LanguageDataStore.QUERY) {\n            router.push(`${pathname}?${createQueryString(\"lang\", lang)}`);\n        }\n        if (languageDataStore === LanguageDataStore.LOCAL_STORAGE) {\n            window.localStorage.setItem(\"next-export-i18n-lang\", lang);\n            const event = new Event(\"localStorageLangChange\");\n            document.dispatchEvent(event);\n        }\n    };\n    // use React.cloneElement to manipulate properties\n    if (React__default[\"default\"].isValidElement(children)) {\n        return React__default[\"default\"].cloneElement(children, {\n            onClick: ()=>{\n                if (children && children.props && typeof children.props.onClick === \"function\") {\n                    children.props.onClick();\n                }\n                // set the language\n                handleLanguageChange();\n            },\n            \"data-language-switcher\": \"true\",\n            // set the current status\n            \"data-is-current\": languageSwitcherIsActive,\n            role: \"button\",\n            \"aria-label\": `set language to ${lang}`\n        });\n    } else {\n        return React__default[\"default\"].createElement(\"span\", {\n            role: \"button\",\n            \"aria-label\": `set language to ${lang}`,\n            \"data-language-switcher\": \"true\",\n            \"data-is-current\": languageSwitcherIsActive,\n            onClick: ()=>{\n                // set the language\n                handleLanguageChange();\n            }\n        }, children);\n    }\n};\n/**\r\n * Simple component wrapper for links with a locale. use it for internal links.\r\n * Either add the lang parameter to the link target in the href-attribute to\r\n * force a specific language (e.g. as a language switcher) or let the component\r\n * add the currently selected languate automatically. Preserves the current query parameters\r\n * @exmaple\r\n *  <LinkWithLocale href={`${t(\"nav.en.about.href\")}?share=social\"}>\r\n *    {t(nav.en.about.text\")}\r\n *  </LinkWithLocale>\r\n * @param href string the value for the href-attribute\r\n * @param [children] string the text to display\r\n */ function LinkWithLocale(props) {\n    const { lang } = useSelectedLanguage();\n    const i18nObj = i18n();\n    const languageDataStore = i18nObj.languageDataStore;\n    const { href, ...rest } = props;\n    const link = React.useMemo(()=>{\n        const inputHref = href.toString();\n        if (inputHref.includes(\"?lang=\") || inputHref.includes(\"&lang=\") || languageDataStore === LanguageDataStore.LOCAL_STORAGE) {\n            return inputHref;\n        }\n        if (inputHref.includes(\"?\")) {\n            return `${inputHref}&lang=${lang}`;\n        } else {\n            return `${inputHref}?lang=${lang}`;\n        }\n    }, [\n        href,\n        lang\n    ]);\n    return React__default[\"default\"].createElement(Link__default[\"default\"], {\n        href: link,\n        ...rest\n    });\n}\nexports.LanguageSwitcher = LanguageSwitcher;\nexports.LinkWithLocale = LinkWithLocale;\nexports[\"default\"] = i18n;\nexports.useLanguageSwitcherIsActive = useLanguageSwitcherIsActive;\nexports.useSelectedLanguage = useSelectedLanguage;\nexports.useTranslation = useTranslation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-export-i18n/index.js\n");

/***/ })

};
;