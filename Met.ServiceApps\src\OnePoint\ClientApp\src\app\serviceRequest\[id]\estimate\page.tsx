'use client';

import { useRoleActions } from '@/auth';
import {
  ArrowLeftIcon,
  CloseIcon,
  PlusIcon,
  ScrewDriverWrenchIcon
} from '@/design/atoms';
import {
  Button,
  Loading,
  PageHeaderContainer,
  Tooltip
} from '@/design/molecules';
import { Page<PERSON>reaBox, Prompt } from '@/design/organisms';
import { useGetAllLocationsDataQuery } from '@/features/branch';
import { CurrencyType } from '@/features/pricing';
import { SelectedProduct } from '@/features/product';
import { SearchByContainer, SearchProductContainer } from '@/features/search';
import {
  AssociatedServiceRequestsHeader,
  AssociateServiceOrderPromptContainer,
  EstimateOptionCard,
  EstimatePageFooter,
  EstimatePageHeader,
  GetEstimatePricingRequest,
  PublishPromptContainer,
  ServiceRequestEstimate,
  ServiceRequestEstimateCart,
  ServiceRequestEstimateCartMapping,
  ServiceRequestEstimateCartOption,
  ServiceRequestEstimateStatusEnum,
  UpdateEstimateLineRequest,
  UpdateEstimateOptionRequest,
  UpdateEstimateRequest,
  useDownloadEstimateMutation,
  useGetPricingMutation,
  useLazyGetEstimateQuery,
  usePublishEstimateMutation,
  useUpdateEstimateMutation
} from '@/features/serviceRequest';
import { showSnackbar } from '@/features/snackbar';
import { useAppDispatch } from '@/store/hooks';
import { getClientId, isLaborUnitOfMeasure } from '@/util';
import { Box, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
interface Props {
  params: {
    id: string;
  };
}

export default function EstimatePage(props: Props) {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const roleActions = useRoleActions();
  const orderId = props.params.id;
  const groupId = searchParams.get('groupId') ?? '';
  const currencyType = searchParams.get('currencyType') ?? '';
  const [openPublishPrompt, setOpenPublishPrompt] = React.useState(false);
  const [openAssociateServiceOrderPrompt, setOpenAssociateServiceOrderPrompt] =
    React.useState(false);
  const [orderIdToAssociate, setOrderIdToAssociate] = React.useState<string>();
  const [lastActiveCart, setLastActiveCart] =
    React.useState<ServiceRequestEstimate>();
  const [cart, setCart] = React.useState<ServiceRequestEstimateCart>(
    {} as ServiceRequestEstimateCart
  );
  const [originalCart, setOriginalCart] =
    React.useState<ServiceRequestEstimateCart>(
      {} as ServiceRequestEstimateCart
    );
  const [displaySearchProducts, setDisplaySearch] = React.useState(false);
  const [displaySearchOrders, setDisplaySearchOrders] = React.useState(false);
  const [selectedOptionClientId, setSelectedOptionClientId] = React.useState<
    string | number
  >();
  const [openPrompt, setOpenPrompt] = React.useState(false);
  const [originBranchCurrency, setOriginBranchCurrency] =
    React.useState<CurrencyType>();
  const { data: locations, isLoading: areLocationsLoading } =
    useGetAllLocationsDataQuery();

  const isCartModified = React.useCallback(() => {
    if (!originalCart || !cart) return false;

    if (originalCart.externalNotes !== cart.externalNotes) return true;

    return (cart.options ?? []).some((currentOption, index) => {
      const originalOption = originalCart.options?.[index];
      if (!originalOption) return true;

      if (
        currentOption.internalNotes !== originalOption.internalNotes ||
        (currentOption.lines?.length || 0) !==
          (originalOption.lines?.length || 0)
      ) {
        return true;
      }

      return (currentOption.lines ?? []).some((currentLine, lineIndex) => {
        const originalLine = originalOption.lines?.[lineIndex];
        if (!originalLine) return true;
        return (
          currentLine.quantity !== originalLine.quantity ||
          currentLine.price !== originalLine.price ||
          currentLine.sku !== originalLine.sku
        );
      });
    });
  }, [cart, originalCart]);

  const hasOptions = (cart.options ?? []).length > 0;
  const isReadOnly = cart.status === ServiceRequestEstimateStatusEnum.Active;
  const isGetPricingButtonDisabled = !cart.externalId || isCartModified();
  const isSaveButtonDisabled =
    (cart.options ?? []).findIndex((x) => (x.lines ?? []).length > 0) < 0 ||
    !isCartModified();

  const isPublishButtonDisabled =
    cart.status === ServiceRequestEstimateStatusEnum.Active ||
    !cart.options?.length ||
    cart.options?.some((o) => !o.lines?.length) ||
    cart.options?.some(
      (o) => !o.externalId || o.lines?.some((l) => !l.externalId || !l.price)
    ) ||
    isCartModified();

  const currentSecondaryMappingIds =
    cart.mappings
      ?.filter((x) => !x.isPrimary)
      .map((x) => x.serviceRequest.id) ?? [];

  const [updateServiceRequestEstimate, { isLoading: isLoadingEstimateUpdate }] =
    useUpdateEstimateMutation();

  const [publishServiceRequestEstimate, { isLoading: isLoadingPublish }] =
    usePublishEstimateMutation();

  const [
    getEstimateQuery,
    { isError: isGetEstimateQueryError, isFetching: isFetchingEstimateQuery }
  ] = useLazyGetEstimateQuery();

  const [fetchPricing, { isLoading: isFetchingPricing }] =
    useGetPricingMutation();

  const [downloadEstimate, { isLoading: isLoadingDownloadEstimate }] =
    useDownloadEstimateMutation();

  const transformEstimate = React.useCallback(
    (estimate: ServiceRequestEstimate): ServiceRequestEstimateCart => {
      return {
        externalId: estimate.externalId,
        version: estimate.version,
        rowVersion: estimate.rowVersion,
        status: estimate.status,
        externalNotes: estimate.externalNotes,
        options: estimate.options?.map((option, optionIndex) => ({
          ...option,
          clientId: getClientId(`option-${optionIndex}`),
          lines: option.lines?.map((line, lineIndex) => ({
            ...line,
            clientId: getClientId(`line-${lineIndex}-${line.sku}`),
            description: estimate.productDataMap?.[line.sku]?.description || '',
            unitOfMeasureCode: line.unitOfMeasure,
            unitOfMeasure: line.unitOfMeasure
          }))
        })),
        mappings: estimate.mappings?.map(
          (x): ServiceRequestEstimateCartMapping => ({
            isPrimary: x.isPrimary,
            groupExternalId: x.groupExternalId,
            serviceRequest: {
              ...x.serviceRequest,
              productDescription:
                estimate.productDataMap?.[x.serviceRequest.sku]?.description ||
                undefined,
              productImage:
                estimate.productDataMap?.[x.serviceRequest.sku]?.image ||
                undefined
            }
          })
        )
      };
    },
    []
  );

  const updateCart = React.useCallback(
    (estimate: ServiceRequestEstimate) => {
      const transformedCart = transformEstimate(estimate);
      setCart(transformedCart);
    },
    [transformEstimate]
  );

  const fetchCurrentEstimate = React.useCallback(async () => {
    const response = await getEstimateQuery({
      externalId: orderId
    });

    if (!isGetEstimateQueryError && response.data) {
      const estimate: ServiceRequestEstimate = response.data;

      const transformedCart: ServiceRequestEstimateCart =
        transformEstimate(estimate);
      setCart(transformedCart);
      setOriginalCart(transformedCart);
    }
  }, [orderId, isGetEstimateQueryError, transformEstimate, getEstimateQuery]);

  const fetchLastActiveEstimate = React.useCallback(async () => {
    const response = await getEstimateQuery({
      externalId: orderId,
      lastActive: true
    });

    if (!isGetEstimateQueryError && response.data) {
      const estimate: ServiceRequestEstimate = response.data;

      setLastActiveCart(estimate);
    }
  }, [orderId, isGetEstimateQueryError, getEstimateQuery]);

  React.useEffect(() => {
    fetchCurrentEstimate();
    fetchLastActiveEstimate();
  }, [fetchCurrentEstimate, fetchLastActiveEstimate]);

  React.useEffect(() => {
    const currency =
      currencyType === '1'
        ? CurrencyType.Usd
        : currencyType === '2'
          ? CurrencyType.Cad
          : CurrencyType.Usd;
    setOriginBranchCurrency(currency);
  }, [currencyType]);

  const handleAddOption = () => {
    const index = cart?.options?.length || 0;

    setCart({
      ...cart,
      options: [
        ...(cart?.options || []),
        { clientId: getClientId(`option-${index}`), lines: [] }
      ]
    });
  };

  const handleUpdateOption = (latest: ServiceRequestEstimateCartOption) => {
    const updatedOptions = cart.options?.map((old) => {
      if (old.clientId === latest.clientId) {
        return latest;
      }
      return old;
    });

    setCart({
      ...cart,
      options: updatedOptions
    });
  };

  const handleProductSearch = (optionClientId: string | number) => {
    setSelectedOptionClientId(optionClientId);
    setDisplaySearch(true);
  };

  const handleOnProductSelected = (
    product: SelectedProduct,
    quantity?: number
  ) => {
    const option = cart.options?.find(
      (x) => x.clientId === selectedOptionClientId
    );

    if (!option) return;

    const index = option.lines?.length || 0;
    const isLabor = isLaborUnitOfMeasure(product.unitOfMeasureCode);
    handleUpdateOption({
      ...option,
      lines: [
        ...(option.lines || []),
        {
          clientId: getClientId(`line-${index}-${product.sku}`),
          quantity: quantity ?? (isLabor ? 0.1 : 1),
          unitOfMeasure: product.unitOfMeasureCode,
          sku: product.sku,
          description: product.description
        }
      ]
    });

    setDisplaySearch(false);
    setSelectedOptionClientId(undefined);
  };

  const handleOnDeleteOption = (clientId: string | number) => {
    setCart((prevCart) => ({
      ...prevCart,
      options: prevCart.options?.filter(
        (option) => option.clientId !== clientId
      )
    }));
  };

  const handleDuplicateOption = (
    optionToDuplicate: ServiceRequestEstimateCartOption
  ) => {
    const index = cart?.options?.length || 0;
    const duplicatedOption: ServiceRequestEstimateCartOption = {
      ...optionToDuplicate,
      clientId: getClientId(`option-${index}`),
      externalId: undefined,
      internalNotes: undefined,
      lines: (optionToDuplicate.lines || []).map((line, index) => ({
        ...line,
        clientId: getClientId(`line-${index}-${line.sku}`),
        externalId: undefined
      }))
    };

    setCart((prevCart) => ({
      ...prevCart,
      options: [...(prevCart.options || []), duplicatedOption]
    }));
  };

  const handlePriceUpdate = async () => {
    const request = {
      externalId: cart.externalId
    } as GetEstimatePricingRequest;
    const response = await fetchPricing(request).unwrap();

    if (response.options?.some((o) => o.lines?.some((l) => l.errorMessage))) {
      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.getPriceErrorMessage',
          severity: 'error'
        })
      );
    }

    updateCart(response);
    const transformedCart: ServiceRequestEstimateCart =
      transformEstimate(response);
    setOriginalCart(transformedCart);
  };

  const handleNavbarBackButtonClick = () => {
    router.push(`/serviceOrder?groupId=${groupId}&orderId=${orderId}`);
  };

  const handleUpdate = async (
    secondaryServiceRequestExternalIds?: string[]
  ) => {
    if (!cart.externalId) {
      return;
    }

    const request: UpdateEstimateRequest = {
      externalId: cart.externalId,
      options: cart.options?.map(
        (option): UpdateEstimateOptionRequest => ({
          externalId: option.externalId,
          internalNotes: option.internalNotes,
          lines: option.lines?.map(
            (line): UpdateEstimateLineRequest => ({
              externalId: line.externalId,
              quantity: line.quantity,
              unitOfMeasure: line.unitOfMeasure,
              sku: line.sku
            })
          )
        })
      ),
      secondaryServiceRequestExternalIds:
        secondaryServiceRequestExternalIds ?? currentSecondaryMappingIds,
      rowVersion: cart.rowVersion,
      externalNotes: cart.externalNotes
    };

    const response = await updateServiceRequestEstimate(request).unwrap();
    const transformedCart: ServiceRequestEstimateCart =
      transformEstimate(response);

    setCart(transformedCart);
    setOriginalCart(transformedCart);
  };

  const handlePublishOpen = () => {
    setOpenPublishPrompt(true);
  };

  const handlePublishClose = () => {
    setOpenPublishPrompt(false);
  };

  const handleAddToAdditionalServiceOrder = () => {
    setDisplaySearchOrders(true);
  };

  const handleCancellationEditing = () => {
    if (isCartModified()) {
      setOpenPrompt(true);
    } else {
      router.push('/');
    }
  };

  const handleAddEstimateToOrder = (orderId: string) => {
    setOpenAssociateServiceOrderPrompt(true);
    setOrderIdToAssociate(orderId);
  };

  const handleAddAssociatedServiceRequest = async () => {
    if (orderIdToAssociate) {
      try {
        await handleUpdate([...currentSecondaryMappingIds, orderIdToAssociate]);
        dispatch(
          showSnackbar({
            messageKey:
              'features.serviceRequestEstimate.addSecondaryServiceRequestSuccess',
            severity: 'success'
          })
        );
        setOrderIdToAssociate(undefined);
        setDisplaySearchOrders(false);
        setOpenAssociateServiceOrderPrompt(false);
      } catch (ex) {
        console.error(ex);
        dispatch(
          showSnackbar({
            messageKey:
              'features.serviceRequestEstimate.addSecondaryServiceRequestError',
            severity: 'error'
          })
        );
      }
    }
  };

  const handleAssociateClose = () => {
    setOpenAssociateServiceOrderPrompt(false);
    setOrderIdToAssociate(undefined);
  };

  const handlePublishConfirm = async () => {
    setOpenPublishPrompt(false);
    if (!cart.externalId) return;
    try {
      const response = await publishServiceRequestEstimate(
        cart.externalId
      ).unwrap();

      updateCart(response);
      fetchLastActiveEstimate();

      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.publishSuccess',
          severity: 'success'
        })
      );
    } catch (err) {
      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.publishError',
          severity: 'error'
        })
      );
    }
  };

  const handleDownloadEstimate = async () => {
    try {
      await downloadEstimate(lastActiveCart?.externalId as string);
      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.downloadEstimateSuccess',
          severity: 'success'
        })
      );
    } catch (err) {
      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.downloadEstimateError',
          severity: 'error'
        })
      );
    }
  };

  const handleRemoveAssociatedServiceRequest = async (
    secondaryServiceRequestExternalId: string
  ) => {
    try {
      const updatedMappings = cart.mappings?.filter(
        (x) => x.serviceRequest.id !== secondaryServiceRequestExternalId
      );

      setCart({
        ...cart,
        mappings: updatedMappings
      });

      await handleUpdate(
        updatedMappings
          ?.filter((x) => !x.isPrimary)
          .map((x) => x.serviceRequest.id)
      );

      dispatch(
        showSnackbar({
          messageKey:
            'features.serviceRequestEstimate.removeSecondaryServiceRequestSuccess',
          severity: 'success'
        })
      );
    } catch (ex) {
      console.error(ex);
      dispatch(
        showSnackbar({
          messageKey:
            'features.serviceRequestEstimate.removeSecondaryServiceRequestError',
          severity: 'error'
        })
      );
    }
  };

  const handleExternalNotesUpdate = (notes: string | undefined) => {
    setCart({
      ...cart,
      externalNotes: notes ?? undefined
    });
  };

  if (displaySearchOrders) {
    return (
      <PageAreaBox pageAreaBoxName="search">
        <Box
          sx={{
            display: 'flex',
            gap: '8px',
            py: '32px'
          }}
        >
          <Tooltip placement="top" title={t('common.back')}>
            <IconButton
              onClick={() => setDisplaySearchOrders(false)}
              data-testid="addToServiceOrderBackBtn-testId"
              sx={{
                mt: { xs: '25px', sm: '29px' },
                height: 'fit-content'
              }}
            >
              <ArrowLeftIcon
                width="16"
                height="16"
                viewBox="0 0 32 27"
                sx={{
                  color: 'primary.500'
                }}
              />
            </IconButton>
          </Tooltip>
          <Box sx={{ width: '100%' }}>
            <SearchByContainer
              locationsQueryResult={{
                locationData: locations,
                isLoading: areLocationsLoading
              }}
              roleActions={roleActions}
              onCustomProcessOrderClick={handleAddEstimateToOrder}
            />
          </Box>
        </Box>
        <AssociateServiceOrderPromptContainer
          open={openAssociateServiceOrderPrompt}
          onPromptConfirm={handleAddAssociatedServiceRequest}
          onPromptClose={handleAssociateClose}
        />
      </PageAreaBox>
    );
  }

  if (displaySearchProducts) {
    return (
      <PageAreaBox pageAreaBoxName="search" sx={{ maxWidth: '1460px' }}>
        <Box
          sx={{
            display: 'flex',
            gap: '8px',
            py: '32px'
          }}
        >
          <Tooltip placement="top" title={t('common.back')}>
            <IconButton
              onClick={() => setDisplaySearch(false)}
              data-testid="addRepairItemsBackBtn-testId"
              sx={{
                mt: { xs: '25px', sm: '29px' },
                height: 'fit-content'
              }}
            >
              <ArrowLeftIcon
                width="16"
                height="16"
                viewBox="0 0 32 27"
                sx={{
                  color: 'primary.500'
                }}
              />
            </IconButton>
          </Tooltip>
          <Box sx={{ width: '100%' }}>
            <SearchProductContainer
              headerText={''}
              shouldFocusInput={true}
              onProductSelected={handleOnProductSelected}
              sx={{
                p: '0px',
                mb: '0px',
                '& .MuiFormControl-root': {
                  boxShadow: 'none'
                }
              }}
            />
          </Box>
        </Box>
      </PageAreaBox>
    );
  }

  return (
    <>
      <Prompt
        open={openPrompt}
        header={t('prompt.serviceRequestEstimate.cancelEstimateEditingHeader')}
        message={t(
          'prompt.serviceRequestEstimate.cancelEstimateEditingMessage'
        )}
        confirmationButtonLabel={t('common.continue')}
        onPromptConfirm={() => router.push('/')}
        cancelButtonLabel={t('common.goBack')}
        onPromptClose={() => setOpenPrompt(false)}
        data-testid="promptExitWithSavedOrderList"
      />
      <PageHeaderContainer sx={{ display: 'flex', gap: 1 }}>
        <IconButton
          data-testid="estimateBackBtn-testId"
          onClick={handleNavbarBackButtonClick}
        >
          <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
        </IconButton>
        <Typography variant="h5" color="neutral.800">
          {t('features.serviceRequestEstimate.navbarTitle')}
        </Typography>
        <Tooltip
          placement="bottom"
          title={t('common.close')}
          sx={{ marginLeft: 'auto' }}
        >
          <IconButton
            onClick={() => handleCancellationEditing()}
            data-testid="closeBtn-testId"
          >
            <CloseIcon />
          </IconButton>
        </Tooltip>
      </PageHeaderContainer>
      <PageAreaBox
        pageAreaBoxName="serviceRequestEstimatePage"
        sx={{
          mt: '88px'
        }}
      >
        <Loading
          isLoading={isFetchingEstimateQuery}
          fallbackContainerProps={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <EstimatePageHeader
            cart={cart}
            lastActiveVersion={lastActiveCart?.version}
            isSaveVisible={
              cart.status === ServiceRequestEstimateStatusEnum.Draft
            }
            isSaveDisabled={isSaveButtonDisabled}
            isLoadingSave={isLoadingEstimateUpdate}
            isPublishDisabled={isPublishButtonDisabled}
            isLoadingPublish={isLoadingPublish}
            onSaveClick={() => handleUpdate()}
            onPublishClick={handlePublishOpen}
            updateCart={updateCart}
          />
          {hasOptions && (
            <>
              <Box
                data-testid="container-testId"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2
                }}
              >
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="secondary"
                    size="medium"
                    onClick={handleAddOption}
                    data-testid="addOptionBtnSecondary-testId"
                    disabled={isReadOnly}
                    startIcon={<PlusIcon viewBox="-1 -1 18 18" />}
                  >
                    {t('features.serviceRequestEstimate.addOption')}
                  </Button>
                  <Button
                    variant="primary"
                    data-testid="getPricingBtn-testId"
                    isLoading={isFetchingPricing}
                    onClick={handlePriceUpdate}
                    disabled={isGetPricingButtonDisabled || isReadOnly}
                  >
                    {t('features.serviceRequestEstimate.getPricing')}
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleDownloadEstimate}
                    isLoading={isLoadingDownloadEstimate}
                    disabled={!lastActiveCart}
                    data-testid="downloadEstimateBtn-testId"
                  >
                    {t('features.serviceRequestEstimate.downloadPdf')}
                  </Button>
                </Box>
                <AssociatedServiceRequestsHeader
                  serviceRequests={cart.mappings
                    ?.filter((x) => !x.isPrimary)
                    .map((x) => x.serviceRequest)}
                  isReadonly={
                    cart.status !== ServiceRequestEstimateStatusEnum.Draft
                  }
                  onRemove={handleRemoveAssociatedServiceRequest}
                  addToAdditionalServiceOrder={
                    handleAddToAdditionalServiceOrder
                  }
                />
              </Box>
              <PublishPromptContainer
                open={openPublishPrompt}
                cancelButtonLabel={t('common.cancel')}
                onPromptConfirm={handlePublishConfirm}
                onPromptClose={handlePublishClose}
              />
            </>
          )}

          <Box sx={{ my: 2 }}>
            {cart.options?.map((option, index) => (
              <EstimateOptionCard
                key={`option-${index}`}
                index={index}
                option={option}
                onOptionUpdate={handleUpdateOption}
                onProductSearch={() => handleProductSearch(option.clientId)}
                onDuplicateOption={() => handleDuplicateOption(option)}
                onDeleteOption={() => handleOnDeleteOption(option.clientId)}
                isReadOnly={isReadOnly}
                currencyType={originBranchCurrency ?? CurrencyType.Usd}
              />
            ))}
          </Box>

          {!hasOptions && (
            <Box
              sx={{
                display: 'flex',
                mt: '18px',
                mb: '24px',
                flexDirection: 'column',
                justifyContent: 'top',
                alignItems: 'center',
                gap: '10px',
                borderRadius: '4px',
                border: '1px solid',
                borderColor: 'neutral.400',
                backgroundColor: 'common.white',
                height: '80vh'
              }}
            >
              <ScrewDriverWrenchIcon
                data-testid="optionsNeededForEstimateIcon-testId"
                sx={{
                  width: '104px',
                  height: '104px',
                  color: 'neutral.200',
                  marginTop: '60px'
                }}
              />
              <Typography
                data-testid="optionsNeededForEstimateText-testId"
                variant="h5"
                color="neutral.500"
                mt="16px"
              >
                {t('features.serviceRequestEstimate.optionsNeededForEstimate')}
              </Typography>
              <Typography
                data-testid="optionsNeededForEstimateSubText-testId"
                variant="p2"
                color="neutral.400"
                mb="10px"
                width="504px"
                align="center"
              >
                {t(
                  'features.serviceRequestEstimate.noOptionsAreAvailableForAnEstimateYet'
                )}
              </Typography>
              <Button
                variant="primary"
                size="medium"
                onClick={handleAddOption}
                data-testid="addOptionBtnPrimary-testId"
                startIcon={<PlusIcon viewBox="-1 -1 18 18" />}
                disabled={isReadOnly}
              >
                {t('features.serviceRequestEstimate.addOption')}
              </Button>
            </Box>
          )}

          <EstimatePageFooter
            cart={cart}
            isReadOnly={isReadOnly}
            handleExternalNotesUpdate={handleExternalNotesUpdate}
          />
        </Loading>
      </PageAreaBox>
    </>
  );
}
