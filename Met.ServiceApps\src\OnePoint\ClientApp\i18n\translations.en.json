{"common": {"milwaukeeTool": "Milwaukee Tool", "onePoint": "OnePoint", "logo": "Logo", "other": "Other", "optional": "optional", "optionalWithParenthesis": "(optional)", "remove": "Remove", "view": "View", "loading": "loading...", "confirm": "Confirm", "greeting": "Hi {{name}}!", "next": "Next", "branch": "Branch", "customer": "Customer", "serviceContact": "Service contact", "orderNumber": "Order #", "edit": "Edit", "id": "ID: {{id}}", "company": "Company", "phone": "Phone", "trades": "Trades", "email": "Email", "inPerson": "In-person", "select": "Select", "selectAll": "Select all", "back": "Back", "pressEnterToAccept": "Press ENTER to accept", "pressEnterToSave": "Press ENTER to save", "save": "Save", "cancel": "Cancel", "unreadable": "Unreadable", "saveAndSelect": "Save & select", "saveAndView": "Save & view", "dateCannotBeFuture": "Date can't be in the future", "required": "*", "savedInformation": "Information saved successfully", "confirmationButton": "Yes, I'm sure", "none": "None", "backToHome": "Back to home", "pageNotFound": "Page not found", "esckeyMessage": "Tap ESC key to go back", "snackBarError": "Oops! We're experiencing some technical issues. Please refresh this page or try back later.", "date": "Date", "saveForLater": "Save for later", "delete": "Delete", "allDone": "All done", "yesExit": "Yes, exit", "areYouSureToExitThisPage": "Are you sure you want to exit this page?", "continue": "Continue", "dotChar": ".", "loadMore": "Load more", "confirmAndSubmit": "Confirm and submit", "submitAndAuthorize": "Submit and authorize", "colonChar": ":", "paymentMethod": "Payment method", "transactionDetails": "Transaction details", "orderFulfillmentDetails": "Order fulfillment details", "resendEmail": "Resend email", "creditCard": "Credit card", "cash": "Cash", "inviteToPay": "Invite to pay", "payAtPickup": "Pay at pickup", "poNumber": "PO number", "itemCount": "Item {{current}} of {{all}}", "payNow": "Pay now", "close": "Close", "clear": "Clear", "scan": "<PERSON><PERSON>", "expand": "Expand", "collapse": "Collapse", "discardChanges": "Discard Changes", "submit": "Submit", "advancedSearchTooltip": "Advanced search", "description": "Description", "serviceCategory": "Service category", "itemPrice": "Item price", "quantityAbbreviation": "Qty", "empty": "Empty", "workInProgress": "Work in progress", "notProvided": "Not provided", "profileIncomplete": "Profile incomplete", "profileIncompleteTooltip": "This profile is missing required information, please update Customer profile to proceed with this order", "addItem": "Add item", "removeItem": "Remove item", "repair": "Repair", "replace": "Replace", "replacement": "Replacement", "sku": "S<PERSON>", "quantity": "Quantity", "notes": "Notes", "total": "Total", "saveAsDraft": "Save as draft", "subtotal": "Subtotal", "guest": "Guest", "amount": "Amount", "moneyQuantity": "${{amount}}", "somethingWentWrong": "Oops, something went wrong", "couldNotLoadCardContent": "We could not load the content for that card", "apply": "Apply", "information": "Information", "goBack": "Go back", "undo": "Undo", "yesText": "Yes", "noText": "No", "taxExempt": "Tax exempt", "n/a": "N/A", "system": "System", "unknown": "Unknown", "unitPrice": "Unit price", "taxes": "Taxes", "accountSuccessfullyUpdated": "Account succesfully updated!", "accountAuthorizationNumberLabel": "Account Authorization Number", "status": "Status", "all": "All"}, "appInsights": {"errorBoundary": {"genericError": "Something went wrong"}}, "auth": {"unauthorized": "Unauthorized", "rolePicker": {"role": "Role", "Admin": "Admin", "BranchManager": "Branch Manager", "BranchAssociate": "Branch Associate", "Hub": "<PERSON><PERSON>", "CX": "CX", "JSS": "JSS", "DigitalCare": "Digital Care", "ProductMarketing": "Product Marketing"}}, "design": {"molecules": {"navbar": {"openSettings": "Open settings"}, "textField": {"charactersRemaining": "{{count}} characters remaining"}}}, "features": {"account": {"search": {"searchResult": {"parentAccountHeader": "Parent account", "siteNumber": "Site number", "serviceContact": "Service contact", "noServiceContact": "No service contacts"}, "searchResultsMoreThan50": "Your search returned too many results, please refine location parameters"}, "details": {"pageTitle": "Account details", "accountName": "Parent account—{{accountName}}", "accountNumber": "Account number: {{accountNumber}}", "siteNumber": "Site number:", "accountNumberSubTitle": "Account number:", "taxExempt": "Tax exempt", "redTote": "Red Tote", "nationalAccountHold": "National account hold", "serviceContact": "Service contact", "serviceContactEmail": "Service program contact", "salesRep": "Sales rep", "billingContact": "Billing contact", "accountManager": "Account manager", "paymentTerms": "Payment terms", "poAccepted": "PO accepted", "creditCardAccepted": "Credit card accepted", "inviteToPayAccepted": "Invite to pay accepted", "transactionHistory": "Transaction history", "accountNotFound": "Account not found", "noServiceProgramBadge": "No service program", "contactDetailsModalTitle": "Service order contact details", "responsibleParty": "Responsible party *", "noUserSelected": "You have no user profile selected for service", "selectProfile": "Select a profile", "dropOffPartyOptional": "Drop-off party (optional)", "dropOffPartyPlaceHolder": "Name *", "profilePageTitle": "Select service order contact", "salesOrderProfilePageTitle": "Select sales order profile", "differentProfile": "Select a different profile", "responsiblePartyTooltip": "Responsible party is the primary contact for service order updates, and person responsible for repair decisions", "dropOffParty": "Drop-off party:"}}, "branch": {"branchModal": {"currentBranch": "Select your current branch", "personalizeExperience": "Personalize your experience and access branch-specific information.", "saveDefault": "Save this location as my default", "selectServiceBranch": "Select a service branch"}, "branchRedirect": "You are being redirected—Please select your current service branch to proceed with this order", "serviceBranch": "Service Branch", "serviceHub": "Service Hub"}, "home": {"mainButtons": {"startRepairOrder": "New Service Request", "startSaleOrder": "Start a sale", "customerLookUp": "Customer lookup", "createProfile": "Create new profile", "newServiceRequest": "New Service Request", "scanQROrSR": "Scan QR or SR", "accountLookup": "Customer lookup", "realtimeService": "Real-time service", "newToteOrder": "New Tote Order", "guestCheckout": "Guest checkout"}, "welcomeModal": {"welcome": "Welcome to OnePoint!", "setup": "Let's get things set up for you."}, "savedOrdersNotification1": "You have 1 or more Saved Orders that were not completed. View your", "savedOrders": "Saved Orders", "savedOrdersNotification2": "to finish your work.", "profile": "Profile", "account": "Account", "dashboard": "Dashboard", "logout": "Logout"}, "search": {"searchBy": "Service order lookup", "selectAProduct": "Select a Product", "searchByCustomer": "Search by email address or phone number", "searchByOrder": "Search by order #, SKU, date", "searchByProduct": "Search by product name, keyword, serial number, or scan by UPC", "recordsFound": "records found", "noRecordsFound": "No records found", "searchByHome": "Search or scan by serial, tracking number or order number", "selectProductToContinue": "Select a product to continue", "selectCustomer": "Select an account or customer", "errorMessage": "Please enter a {{fieldName}}", "serviceRequestLookupTitle": "Service order lookup", "serviceRequestLookupPlaceholder": "Search or scan by serial, tracking number or order number", "advancedSearch": {"titleAdvancedSearch": "Advanced search", "filterRepairOrigin": {"title": "Repair origin", "eServiceOption": "eService", "serviceBranchOption": "Select a repair origin", "oneKeyOption": "OneKey"}, "filterRepairLocation": {"title": "Repair location", "repairLocationOption": "Select a repair location"}, "filterOrderStatus": {"title": "Order status"}, "filterDateRange": {"title": "Date range", "errors": {"startDateInvalid": "Start date is not valid", "endDateInvalid": "End date is not valid", "startDateLater": "Start date cannot be later than end date", "selectStartDate": "Select start date", "selectEndDate": "Select end date"}}, "buttonReset": "Reset", "buttonApplyFilters": "Apply filters"}, "orderSearchFilter": {"title": "Filter by"}}, "order": {"start": "Start a Service Order", "repairItems": "Repair Items", "searchByOrder": "Search by order #, SKU, date", "searchByProduct": "Search by product name, keyword, serial number, or scan by UPC", "eServiceOrderType": "eService Order", "branchOrderTypeGroup": "Service branch group ID", "supportCenterOrderTypeGroup": "eService group ID", "oneKeyOrderTypeGroup": "OneKey group ID", "branchOrderType": "Service branch order", "supportCenterOrderType": "eService order", "oneKeyOrderType": "OneKey order", "eServiceAddress": "Shipping Address", "repairOriginOrderCard": "Repair origin", "dateSubmitted": "Date submitted", "print": "Print", "orderStatus": "Order status", "toolsCounterText": "Tools in this order", "viewOrder": "View Order", "viewDetails": "View details", "processOrder": "Process Order", "requiresAttention": "This order requires your attention", "startRepair": "Start a repair", "startRealtimeRepair": "Real-time service", "addToCart": "Add to cart", "repairDetailsComplete": "Repair details complete", "serviceCartHeader": "Service cart", "repairItemAddedToCart": "Repair item added to cart!", "dateStarted": "Date Started", "completeOrder": "Complete order", "savedOrdersCount": "Saved repair orders ({{itemsCount}})", "savedOrders": "Saved orders", "repairOrigin": "Repair origin", "repairLocation": "Repair location", "shelfLocation": "Shelf location", "deliveryType": "Delivery Method", "customerReferenceId": "Customer Reference ID", "masterTracking": "Master Tracking", "shelfLocationPlaceHolder": "Shelf location (optional)", "masterTrackingNumber": "Master tracking number (optional)", "poNumberInputPlaceHolder": "PO number (optional)", "accountAuthorizationNumberPlaceHolder": "Account Authorization Number (optional)", "accountNumberInputPlaceHolder": "Account number (optional)", "repairCompleted": "Repair completed", "repairTypeSummary": "[RepairType] summary", "warrantyEvaluation": "Our factory trained technician has completed your warranty evaluation. ", "customerDecisionType": "Customer decision Type", "enteredBy": "Entered by", "customerDecisionDate": "Customer decision Date", "decisionDate": "Decision date", "modeOfContact": "Mode of contact", "authorizedBy": "Authorized by", "itemsTableButton": "Itemized table including parts + labor if applicable", "repairOrderDetails": "Repair order details", "editRepairOrderDetails": "Edit repair details", "decisionNotes": "Decision notes:", "amountDue": "Amount due", "totalAmountDue": "Total amount due", "subTotal": "Subtotal", "salesTax": "Sales Tax", "estimatedSaleTax": "Estimated sales tax", "changeAmountDue": "Change due", "confirmPickup": "Confirm pickup", "pickupParty": "Pickup party", "pickupDate": "Pickup date", "pickupPartyInitialsOptional": "Pickup party initials (optional)", "pickupPartyInitialsPlaceholder": "Enter pickup party initials only (optional)", "createEstimate": "Create estimate", "viewEstimate": "View estimate", "viewPrimaryRequest": "View Primary Service Order", "serviceOrder": "Service order", "expandMultiToolOrder": "Expand for multi-tool order details", "replacementItem": "Replacement item", "warrantyApproved": "Warranty approved", "warrantyDenied": "Warranty denied", "warrantyPending": "Warranty pending", "repairByReplacement": "RBR", "emailReceipt": "Email receipt", "emailReceiptMessage": "This receipt will be emailed to:", "emailReceiptPlaceholder": "Email address", "confirmEmailReceipt": "Confirm and send", "receiptSent": "Receipt has been successfully emailed!", "orderCompleted": "Your repair order was successfully marked as complete!", "addedItemsToServiceCart": "You added ({{itemsCount}}) items to your Service Cart!", "orderCompleteHeader": "Order complete", "markOrderComplete": "Mark order complete", "markMultiToolOrdersComplete": "Mark ({{ordersCount}}) orders as complete", "printReceipt": "Print receipt", "startReturn": "Start a return", "printAndEmailReceipt": "Both", "abandonToolPickupQuestion": "Abandon tool pickup?", "deniedWarrantyDescription": "After assessment by our trained and certified repair technicians, we have determined that this tool does not qualify for warranty coverage due to ", "deniedWarrantyChooseAnOption": "Please select an option to proceed with your order:", "deniedWarrantyReturnActionTitle": "Return", "deniedWarrantyReturnActionDescription": "Your tool will be returned to you unrepaired.", "deniedWarrantyRepairActionTitle": "Repair", "deniedWarrantyRepairActionDescription": "Your tool will be fixed at our Lightning Max Repair rate.", "deniedWarrantyRecycleActionTitle": "Recycle", "deniedWarrantyRecycleActionDescription": "Your tool will be recycled at our facility and will not be returned.", "deniedWarrantyWarning1": "Once you have submitted your decision, ", "deniedWarrantyWarning2": "it cannot be changed", "deniedWarrantyWarning3": ". If no decision is made after ", "deniedWarrantyWarning4": "35 days ", "deniedWarrantyWarning5": "and ", "deniedWarrantyWarning6": "(3) attempts ", "deniedWarrantyWarning7": "by Customer Experience to reach you, ", "deniedWarrantyWarning8": "your tool will automatically be recycled", "paymentMethodModalTitle": "Repair tool payment method", "paymentMethodModalInstruction": "Please select a payment method to proceed with repair of this tool.", "paymentMethodModalInputHint": "Select payment method", "paymentMethodLegend1": "Your tool will be repaired by certified Milwaukee Tool technicians. You will be charged using the payment method selected:", "paymentMethodLegend2": "Once a decision has been submitted it cannot be changed. Are you sure you want to submit this decision?", "recipientEmailAddress": "Recipient email address", "customerWarrantyDecisionType": "Customer decision type", "customerWarrantyDecisionEnteredBy": "Entered by", "customerWarrantyDecisionDate": "Customer decision date", "paidOn": "Paid on", "requestedOn": "Requested on", "paymentAuthorizedOn": "Payment authorized on", "nationalAccountNumber": "National account number", "noPaymentMethod": "No payment method provided", "completedOn": "Completed on", "amountPaid": "Amount paid: ", "totalPricePickup": "Multi-tool pickup total: ", "paymentInviteSent": "Payment invite sent: ", "returnModalTitle": "Return tool confirmation", "returnModalDescription": "Your tool will be returned to you disassembled in multiple pieces, unpackaged, and unrepaired. Per <PERSON> Tool's safety policy, we cannot return broken or unrepaired tools in order to avoid harm or death.", "returnModalQuestion": "Once a decision has been submitted it cannot be changed. Are you sure you want to submit this decision?", "recycleModalTitle": "Recycle tool confirmation", "recycleModalDescription": "Your tool will be recycled at our facility and will not be repaired or returned to you.", "recycleModalQuestion": "Once a decision has been submitted it cannot be changed. Are you sure you want to submit this decision?", "repairActionModalTitle": "Repair tool confirmation", "repairActionModalDescription": "Your tool will be repaired by certified Milwaukee Tool technicians. You will be charged using the payment method you select. Would you like to proceed?", "totalEstimate": "Total estimate", "lmrEstimate": "LMR estimate", "taxEstimate": "Est. Tax", "modeOfContactInputHint": "Mode of contact", "authorizedByInputHint": "Authorized by", "notesInputHint": "Notes (optional)", "inRepairStatus": "In repair", "pickupStatus": "Ready for pickup", "unknownStatus": "Unknown", "deniedWarrantyStatusText": "In repair—action required", "processMultipleOrders": "Process multiple orders ({{ordersCheckedLength}})", "bulkOrderPickup": "Bulk order pickup ({{ordersToPickupLength}})", "continueToPayment": "Continue to payment", "cashReceived": "Cash received", "readyForPickup": "Ready for pickup", "operationalReplacement": "OPR", "decisionSnackbarMessage": "{{decisionType}} decision successfully submitted!", "taxesLabel": "Sales tax", "addRepairLines": "Add repair lines", "deniedWarrantyModalTitle": "Denied <PERSON>y <PERSON>", "selectResolution": "Select a resolution", "selectRepairType": "Select a repair type", "selectOpeningStatement": "Select an opening statement", "selectDiagnosisArea": "Select a diagnosis area", "selectDiagnosisCode": "Select a diagnosis code", "selectSymptomArea": "Select a symptom area", "selectSymptomCode": "Select a symptom code", "supportCenterOriginLocation": "eService", "oneKeyOriginLocation": "OneKey", "supportCenterReturnLocation": "Return Location", "editDetails": "Edit Details", "nonWarrantyDetails": "Non-Warranty Details", "repairLinesUpdated": "Repair lines updated successfully", "repairLinesUpdateFailed": "Failed to update repair lines", "orderSubmitted": "Order submitted successfully", "orderSubmitFailed": "Failed to submit order", "orderDetailsSaved": "Order details successfully saved!", "orderSubmittedTitle": "Order submitted", "serviceRequestNumber": "Request number", "insufficientCashError": "please enter a number equal or higher than amount due", "serviceOrderNotCreatedWarning": "Your repair order was not submitted. Please try again", "decisionNotesGenerated": "This is a system-generated decision due to no response from customer after 35 days.", "inboundTrackingNumber": "Tracking number", "outboundTrackingNumber": "Outbound tracking number", "payWithDifferentMethod": "Pay with a different method", "serviceOrderStates": {"onePointStates": {"submittedStatus": "Submitted", "inTransit": "In Transit", "receivedAtHub": "Received At HUB", "inRepairStatus": "In Repair", "shipped": "Shipped", "pickupStatus": "Ready for pickup", "delivered": "Delivered", "orderComplete": "Complete", "pendingStatus": "Pending", "orderCanceled": "Canceled", "actionRequiredStatus": "In repair—action required"}, "eServiceStates": {"submittedStatus": "Submitted", "inTransit": "In Transit", "receivedAtHub": "Received At HUB", "inRepairStatus": "In Repair", "shipped": "Shipped", "delivered": "Delivered", "orderComplete": "Complete", "pickupStatus": "Ready for pickup"}, "oracleStates": {"created": "Pending", "submitted": "Submitted", "new": "New", "shippedtohub": "Ship to Repair Location", "receivedathub": "Recived at Repair location", "onekeyhold": "OneKey Hold", "authorizeforscrap": "Authorize For Scrap", "holdingforparts": "Holding for Parts", "pendingauthorization": "Pending Authorization", "authorizetorepair": "Authorize to Repair", "postedstageforshipment": "Posted Stage for Shipment", "techevaluation": "Tech Evaluation", "awaitingnationalauthorizationnumber": "Awaiting National Authorization Number", "provideestimate": "Provide Estimate", "cancel": "Cancel", "repaircomplete": "Repair Complete", "scrap": "SCRAP", "repaircompleteawaitingcustomerpickup": "Awaiting Customer Pick Up", "repaircompleteshiptocustomer": "Ship to Customer", "srshipwillcall": "Ship - <PERSON>"}}, "completeOrderFailed": "Failed to complete order(s)", "processInviteToPayFailed": "Cannot Process the Invite to Pay at this moment", "processInviteToPayCompleted": "Service Order Pickup Complete", "noServiceOrderNumberLabel": "Pending Service Order Number", "transitDetails": "Order transit details", "decisionDetails": "Non-warrantable decision details", "payments": {"terminalModal": {"modalHeader": "Square terminal selection", "modalDescription": "Select one of the following terminals to process the credit card payment:", "selectAndPay": "Select and pay"}, "payWithSquare": "Pay with Square", "terminalStatusModal": {"processingPayment": "Payment in progress", "pleaseWait": "Please wait...", "paymentCanceled": "Payment Canceled", "paymentCompleted": "Completed", "verifyCheckout": "Verify checkout"}}, "detail": {"uploadTaxExemptCertificate": "Upload tax exemption certificate", "taxExemptCertificateButtonText": "Tax exemption certificate", "uploadDocument": "Upload document", "startServiceCredit": "Service credit", "evaluationOverview": "Evaluation overview", "cart": "Cart ({{count}})", "totalCreditAvailable": "Total credit available:", "itemPrice": "<PERSON><PERSON>", "creditItemAmount": "${{amount}}"}, "toolRetrievedOnGivenDate": "Tool retrieved {{retrievalDate}}", "toolRetrievedComplete": "Complete", "toolRecycledOnGivenDate": "Tool recycled {{recycledDate}}", "toolRecycledComplete": "Complete", "lastUpdated": "Last updated—{{date}}", "toolRecycled": "Tool recycled—{{date}}", "toolRetrieved": "Tool retrieved—{{date}}", "taxResponseError": "Error while fetching tax calculation", "creditCardAuthorized": "Credit card authorized", "creditCardAuthorizationPending": "Authorization pending", "creditCardAuthorizedOn": "Authorization date", "serviceCredit": {"serviceCreditHeader": "Service Credit", "creditAmount": "Credit amount", "applyCredit": "Apply credit", "summary": "Service credit summary", "transaction": "Service credit transaction", "noChargesError": "Failed to fetch service order charges", "applyServiceCreditError": "An error has occurred applying your service credit. If it persists, please contact our support team.", "serviceCreditLimitExceeded": "The current credit amount exceeds the total service credit available. Please review your charge quantities/selections.", "negativeCreditError": "The current credit amount must be greater than 0. Please review your charge quantities/selections."}, "serviceCreditDate": "Service credit {{serviceCreditDate}}", "salesOrderReturn": "Sales return order", "return": {"restock": {"recall": "Recall Product", "recon": "30 Day Satisfaction", "reject": "Return to customer", "scrap": "Scrap Product", "restock": "Return to Inventory", "placeholder": "Restock disposition"}, "paymentMethod": "Payment method", "taxCalculationPending": "pending", "plusTax": "plus tax", "refundAmount": "Refund amount", "confirmReturn": "Confirm return", "completeReturn": "Complete return", "returnItemsSelectedWarning": {"start": "You're selecting ", "items": "{{count}} item(s)", "end": ". Please, review before submitting."}, "error": "An error has occurred trying to create your return sales order. If it persists, please contact our support team.", "returnCreatedSuccess": "Sales return order {{returnOrderNumber}} created successfully"}, "ErrorPricingRequestMessage": "Unable to retrieve pricing for this order, please try again later"}, "product": {"serialNumber": "Serial number", "productSerialNumber": "Product serial number", "realTimeItems": "Real-time service items", "realTimeItemsLabel": "Enable real-time service", "realTimeService": "Real-time service", "realTimeServiceSummary": "Real-time service summary", "inputPlaceholder": "Enter or scan a 7-21 digit serial number", "uploadReceipt": "Upload receipt", "serialNumberUnreadable": "Serial number unreadable", "describeProblem": "Describe the problem with this tool", "selectAllThatApply": "Select all that apply:", "customerReferenceId": "Customer reference ID (optional)", "customerReferenceIdPlaceHolder": "Ex. Fred's Handyman Services", "proofOfPurchase": "Proof of purchase", "proofOfPurchaseIncluded": "Included", "proofOfPurchaseNotIncluded": "Not included", "purcharseDateOptional": "Purchase date (optional)", "yourFileWasUploaded": "Your file was successfully uploaded!", "detectedPurchaseDate": "Purchase date", "inclusionsTitle": "Included items", "lmrText": "Lightning Max Repair Est.", "problemDescription": "Problem description", "inclusionsDescription": "Inclusions", "serialNumberHelperText": "Enter a 7-21 digit serial number OR select 'Serial number unreadable'", "replaceFile": "Replace file", "provideValidPurchaseDate": "Please enter a valid purchase date", "replacementItemSerialRestrictionTooltip": "Replacement Item serial must be recorded to mark order complete", "partialSerialNumberPlaceholder": "Enter an asterisk (*) for unreadable characters", "suggestedItem": "Suggested item", "suggestedReplacementItem": "Suggested replacement item", "selectADifferentItem": "Select a different item", "selectAReplacementItem": "Select a replacement item", "warrantyRequest": "Warranty request", "nonWarranty": "Non-warranty", "deniedWarrantyLMROption": "LMR", "deniedWarrantyQuoteOption": "Quote", "nonWarrantyHelperText": "Please select an non-warranty option", "warranty": "Warranty", "repairRequest": "Repair request: {{repairRequestStatus}}", "editSerialNumber": "Edit serial number", "editProofOfPurchase": "Edit proof of purchase", "selectedItem": "Selected item", "uploadProofOfPurchaseModalTitle": "Upload proof of purchase", "uploadProofOfPurchaseDropzoneText1": "Drag & drop your file here", "uploadProofOfPurchaseDropzoneText2": "OR", "uploadProofOfPurchaseSnackbarMessage": "Receipt successfully attached", "describeProblemPlaceHolder": "Describe the problem or add comments", "problemIsNotProvided": "Problem not provided", "problemIsNotProvidedLabel": "Not provided", "noProductData": "No Product Data", "replacementReasonLabel": "Replacement reason code *", "serviceCategory": "Service category *", "realTimeReplaceSerialNumberLabel": "Product serial number *", "realTimeReplaceSerialNumberPlaceholder": "Enter or scan a 7-21 digit serial number", "replacementReason": "Replacement reason", "serviceCategoryLabel": "Service category", "realTimeReplaceSerialNumberTooltip": "Replacement item serial must be recorded to mark order complete", "realTimeReplaceSerialNumberHelperText": "Enter a 7-21 digit serial number OR enter an asterisk (*) for unreadable characters", "checkoutFieldsTitle": "Checkout fields"}, "customer": {"lookup": "Customer lookup", "profileTabs": {"toolBox": "Toolbox", "serviceHistory": "Service history", "engagement": "Engagements", "notes": "Notes"}, "profileForm": {"firstName": "First name", "lastName": "Last name", "emailAddress": "Email address", "phoneNumber": "Phone number", "billingAddress": "Billing address", "country": "Country", "addressLine1": "Street address", "addressLine2": "Apt, suite, etc (optional)", "addressLine3": "Address line 3", "city": "City", "state": "State or Province", "postalCode": "Postal code", "companyName": "Company name", "businessOrCompanyNameOptional": "Business or company (optional)", "preferredLanguage": "Preferred language", "additionalAddressLine": "+ Additional address line", "preferredContactMethod": "Preferred method of contact", "creationSuccess": "Customer profile successfully created", "duplicateEmail": "An user with this email address already exists", "userNotFound": "User not found", "countryCode": "Code"}, "primaryAddress": "Primary address", "customerDetails": "Customer details", "contactInfo": "Contact info", "customerName": "Customer name", "customerProfile": "Customer profile", "editModalTitle": "Edit profile", "search": {"accountSearchToggle": "Site lookup", "customerSearchToggle": "Profile lookup", "orSeparator": "OR", "customerNameInputPlaceHolder": "First name *", "customerLastNameInputPlaceHolder": "Last name *", "accountSearchInputPlaceHolder": "Search by Site Name or (#)Site Number *", "zipCodeSearchInputPlaceHolder": "Enter city, state, province or postal code", "zipCodeSearchInputEmptyError": "Please, enter city, state, province or postal code", "continueAsGuest": "Continue as guest", "parentAccountNameOptional": "Parent Account name (optional)", "cityOptional": "City (optional)", "postalCodeOptional": "Postal code (optional)", "partialEmailProvided": "Email searching does not support partial search, please enter full email address to get results"}, "details": {"taxExemptLabelmodal": "Tax exempt status"}}, "repair": {"repairItems": "Repair items ({{itemsCount}})", "continueToCheckout": "Continue to checkout", "cartIsEmpty": "Cart is empty", "orderCompleted": "Your repair order was successfully submitted!", "viewServiceAgreement": "View / print service agreement", "printServiceAgreement": "Print service agreement", "printServiceReceipt": "Print service receipt", "nextStep": "Anything else for this customer?", "addRepairItem": "Add repair item", "addItemToTote": "Add another item to the tote", "serviceCartItems": "Service cart items ({{itemsCount}})", "serviceCartItems2": "Service cart ({{itemsCount}})", "orderSummary": "Bulk order summary", "orderSummaryItems": "Items in this group ({{itemsCount}})", "print": {"print": "Print", "zebraTags": "Print Zebra Tags", "error": "Print Error: ", "selectPrinter": "Select a printer", "numberOfCopies": "Number of copies"}, "submitOrder": "Submit order", "repairLocation": "Repair location", "noItemsSelected": "You have no items selected for repair", "noWorkOrdersSelected": "You have no work order items selected", "addItems": "Add repair item", "addWorkOrderItems": "Add work order items", "addMoreItems": "Add more items", "fullSerial": "Full serial", "partialSerial": "Partial serial", "viewCustomerServiceHistory": "View customer service history", "addSelectedItems": "Add selected ({{counter}})", "repairLinesWarning": "Please review selections carefully before submitting.", "confirmRepairDetails": "Confirm repair details", "nonWarrantableItem": "Non-warrantable item", "confirmDetails": "Confirm details", "workOrderItems": "Work order items:", "workOrderItemsNoColon": "Work order items", "attachWorkOrderItems": "Attach work order items", "getPricing": "Get pricing", "returnLocation": {"accordionTitle": "Delivery method", "selectServiceBranch": "Select a service branch *", "willCallLabel": "<PERSON>", "dropShipLabel": "Drop-ship", "shipToLabel": "Ship-To", "internationalShippingLabel": "International Shipping", "branchAddressLabel": "Service branch:", "customerAddressLabel": "Primary address:", "additionalAddressLabel": "Additional address:", "AddNewAddressButton": "Add a new address", "additionalAddressModalTitle": "Additional address", "saveButtonTextAdditionalAddressModal": "Save & Select", "additionalAddressForm": {"addressNameLabel": "Name"}}, "repairLocationAlert": {"verbiage1": "A change in the repair location was detected in item: ", "verbiage2": "{{commaSeparatedListOfItems}}. ", "verbiage3": "You're overriding a business rule. ", "verbiage4": "Please verify this is correct before ", "verbiage5": "submitting the order."}, "subtotal": "Subtotal:", "itemPrice": "<PERSON><PERSON>", "itemPriceTooltipMessage": "Click 'Get pricing' button to display the item price", "calculatePricingPiorContinue": "Calculate pricing and add labor item prior to continuing", "messages": {"warningBeforeProceedToCheckoutHead": "Are all repair details correct?", "warningBeforeProceedToCheckoutContent": "Once you continue to checkout, details cannot be edited."}, "realtime": {"orderComplete": "Service order complete!", "checkoutWarningTitleText": "Are all repair details correct?", "checkoutWarningDetailsText": "Once you continue to checkout, details cannot be edited", "changeDue": "Payment complete—Change due: ${{change}}", "orderSummary": "Order summary", "pickupUpdateError": "Error updating pickup information", "alertManyLaborItemsAddedTitle": "You have added more than one labor item. Only one labor item is allowed to proceed.", "alertManyLaborItemsAddedContent": "Please remove the extra labor item to continue.", "alertReplacementItemChanged": "The replacement item has been changed. Please go back to the work order items cart and recalculate pricing to proceed to the next step.", "addLaborB": "Add Labor B", "addLaborH": "Add Labor H"}, "repairLinesLabel": "Repair Lines"}, "error": {"unauthorizedAlertText": "We failed to authenticate user credentials. Please log in again.", "returnLoginBtnText": "Return to log in", "sessionExpired1": "Ope!", "sessionExpired2": "Please log in again"}, "hub": {"hubModal": {"currentHub": "Select your current hub", "personalizeExperience": "Personalize your experience and access hub-specific information.", "saveDefault": "Save this location as my default", "selectServiceHub": "Select a service hub"}}, "serviceRequestEstimate": {"itemPrice": "Item price", "expectedPrice": "Expected price", "expectedPriceTooltip": "The prices displayed are estimates only and may not reflect the final cost.", "removeOption": "Remove option", "navbarTitle": "Service request estimate", "addOption": "Add option", "getPricing": "Get pricing", "downloadPdf": "Download PDF", "optionsNeededForEstimate": "Options needed for estimate", "noOptionsAreAvailableForAnEstimateYet": "No options are available for an estimate yet. Please provide at least one option to save a draft.", "noItemsAreAvailableForAnOptionYet": "An item is needed to proceed with this new estimate option.", "duplicateOption": "Duplicate option", "publishEstimate": "Publish", "addToServiceOrder": "Add to other service order", "publishSuccess": "Estimate published successfully", "publishError": "Error at publishing estimate. Please try again.", "createNewEstimate": "Create new estimate", "createNewVersion": "Create new version", "createNewVersionSuccess": "New version created successfully", "createNewVersionError": "Error at creating new version. Please try again.", "currentVersion": "Current version: {{version}}", "lastActiveVersion": "Published version: {{version}}", "downloadEstimateSuccess": "Estimate PDF downloaded successfully", "downloadEstimateError": "Error at downloading estimate PDF. Please try again.", "downloadAgreementSuccess": "Service agreement PDF downloaded successfully", "downloadAgreementError": "Error at downloading service agreement PDF. Please try again.", "associatedServiceRequests": "Associated service orders: {{count}}", "linkSV": "Link", "addSecondaryServiceRequestSuccess": "Service order associated successfully", "addSecondaryServiceRequestError": "Error at associating service order. Please try again.", "removeSecondaryServiceRequestSuccess": "Service order removed successfully", "removeSecondaryServiceRequestError": "Error at removing service order. Please try again.", "errorMessage": "Error Message", "getPriceErrorMessage": "Unfortunately, something went wrong when retrieving the pricing for some of your items. You will not be able to generate the estimate without the latest pricing", "internalNotes": "Internal notes", "estimateOptionNumber": "Estimate option #{{number}}", "estimateQuote": "Estimate quote", "associatedProblemDescription": "Problem", "externalNotes": "External notes", "addNote": "Add note", "saveNote": "Save note", "editNote": "Edit note", "removeNote": "Remove note", "estimateNotePlaceholder": "Note (optional)", "noteTitle": "Note"}, "salesOrder": {"startSale": "Start a sale", "cart": "Cart ({{count}})", "noProductsSelected": "You have no products in your cart", "addProduct": "Add a product", "continueToCheckout": "Continue to checkout", "enterPromoCode": "Enter promo code", "reviewAndPay": "Review + pay", "orderSummary": "Order summary", "counterSale": "Counter sale", "getPrice": "Get price", "unitPrice": "Unit price", "orderItems": "Order items", "paymentDetails": "Payment details", "salesOrderDetails": "Sales Order Details", "orderDetailsIdentifier": "Sales order", "statuses": {"created": "Created", "paymentPending": "Payment pending", "submitted": "Submitted"}, "orderCompleted": {"title": "Order submitted", "text": "Your payment was successful and your order has been submitted!", "pendingPaymentText": "Your order has been submitted and it's pending payment.", "orderIdentifier": "Sales order "}, "returnOrder": {"title": "Return order", "created": "Sales return order created successfully.", "error": "An error occurred trying to create your return sales order. If it persists, please context our support team.", "transaction": "Return order transaction", "orderIdentifier": "Sales order return", "returnNotice": "Item(s) in this order are set to be returned"}, "refundAmount": "Refund amount", "estPoReturnAmount": "Estimated PO return amount", "promoCodeInvalid": "Promo code invalid", "promoCodeApplied": "Promo code applied:", "availableInventory": "Inventory: {{availableInventory}}", "inventoryCheckError": "Failed to fetch inventory for SKU: {{sku}}"}}, "prompt": {"orderRepair": {"confirmationHeader": "Quit repair order?", "confirmationMessage": "Are you sure you want to quit this repair order?"}, "removeOrderItem": {"confirmationHeader": "Remove repair item?", "confirmationMessage": "Are you sure you want to remove this repair item? This action may cause you to lose your work."}, "removeCartItem": {"confirmationHeader": "Remove from cart?", "confirmationMessage": "Are you sure you want to remove this repair item from your cart and start over?"}, "removeSavedOrder": {"confirmationHeader": "Delete saved order?", "confirmationMessage": "Are you sure you want to delete this from your saved orders?"}, "serviceRequestEstimate": {"confirmationHeader": "Publish estimate?", "confirmationMessage": "Are you sure you want to publish estimate?", "newVersionHeader": "Create a new version", "newVersionMessage": "Are you sure you want to create a new version of this estimate?", "associateSVHeader": "Add service order", "associateSVMessage": "Are you sure you want to associate service order to this estimate?", "removeSecondaryServiceRequestHeader": "Remove service order", "removeSecondaryServiceRequestMessage": "Are you sure you want to remove this service order from the estimate?", "cancelEstimateEditingHeader": "Cancel estimate editing?", "cancelEstimateEditingMessage": "You have unsaved changes. If you cancel now, all changes made to this estimate will be lost."}, "endBulkOrderSession": {"confirmationHeader": "Are you done adding items to this bulk order?", "confirmationMessage": "You will not be able to add more items to the order if you proceed."}}, "pages": {"customer": {"serviceHistory": "Service history", "salesHistory": "Sales history", "engagements": "Engagements", "reason": "Reason", "type": "Type", "status": "Status", "viewDetailsButton": "View details"}}}