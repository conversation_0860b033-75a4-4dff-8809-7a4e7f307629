import { Box, SxProps, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';

interface Props {
  id: string;
  parentAccountName: string;
  parentAccountNumber?: string;
  'data-testid'?: string;
  sx?: SxProps;
}

export const AccountSearchItemHeader = (props: Props) => {
  const { t } = useTranslation();
  const colonSymbol = ':';

  return (
    <Box
      data-testid={props['data-testid']}
      sx={{
        border: '1px solid',
        borderColor: 'neutral.400',
        padding: '12px 16px',
        display: 'inline-flex',
        alignItems: 'center',
        gap: 0.5,
        width: '100%',
        backgroundColor: 'neutral.50',
        ...props.sx
      }}
    >
      <Box sx={{ display: 'inline-flex', gap: 1 }}>
        <Box
          sx={{
            display: 'flex',
            gap: 0.5,
            alignItems: 'center'
          }}
        >
          <Typography
            variant="p2"
            color="neutral.500"
            fontWeight={600}
            noWrap={true}
            data-testid={`parentAccountNameTitle-${props.id}-testId`}
          >
            {t('features.account.search.searchResult.parentAccountHeader')}
            {colonSymbol}
          </Typography>
          <Typography
            variant="p2"
            color="neutral.500"
            fontWeight={600}
            noWrap={true}
            data-testid={`parentAccountName-${props.id}-testId`}
          >
            {props.parentAccountName}
          </Typography>
          <Typography
            variant="p2"
            color="neutral.500"
            fontWeight={600}
            noWrap={true}
            data-testid={`parentAccountNumber-${props.id}-testId`}
          >
            {props.parentAccountNumber}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
