import { SxProps } from '@mui/material';
import LinearProgress, {
  linearProgressClasses
} from '@mui/material/LinearProgress';

interface Props {
  value: number;
  sx?: SxProps;
}

export const ProgressBar = (props: Props) => {
  return (
    <LinearProgress
      variant="determinate"
      value={props.value}
      data-testid="progressBar-testId"
      sx={{
        height: 8,
        borderRadius: 8,
        border: 'none',
        backgroundColor: 'brandVariables.wshrGray',
        [`& .${linearProgressClasses.bar}`]: {
          borderRadius: 8,
          backgroundColor: 'success.400'
        },
        ...props.sx
      }}
    />
  );
};
