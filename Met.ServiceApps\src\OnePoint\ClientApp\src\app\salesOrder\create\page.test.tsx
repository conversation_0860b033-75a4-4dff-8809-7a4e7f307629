import { renderWithProviders } from '@/../__test-utils__/renderWithProviders';
import {
  salesOrderCreationView,
  SalesOrderProduct
} from '@/features/salesOrder';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';
import SalesOrderCreationPage from './page';

jest.mock('@/../store/hooks');

describe('SalesOrderCreationPage', () => {
  describe('Current view is set to CustomerSelection', () => {
    it('should render the customer search', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = undefined;
      const currentView = salesOrderCreationView.CustomerSelection;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-customer-search-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });
  });

  describe('Current view is set to CartSummary', () => {
    it('should render the customer information and the empty cart', () => {
      const selectedProducts = [] as unknown[];
      const selectedCustomer = { ...mockSelectedCustomer };
      const salesOrderCreationProfile = { ...mockSalesOrderCreationProfile };
      const currentView = salesOrderCreationView.CartSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile,
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-selected-customer-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-empty-cart-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-list-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-guest-selected-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });

    it('should render the customer information and the list of products', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = { ...mockSelectedCustomer };
      const salesOrderCreationProfile = { ...mockSalesOrderCreationProfile };
      const currentView = salesOrderCreationView.CartSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile,
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-selected-customer-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-product-list-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-button-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-empty-cart-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-guest-selected-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });

    it('should render as guest and the empty cart', () => {
      const selectedProducts = [] as unknown[];
      const selectedCustomer = undefined;
      const currentView = salesOrderCreationView.CartSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile: {
          ...mockSalesOrderCreationProfile,
          isGuest: true
        },
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-guest-selected-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-empty-cart-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-list-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-selected-customer-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });

    it('should render as guest and the list of products', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = undefined;
      const currentView = salesOrderCreationView.CartSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile: {
          ...mockSalesOrderCreationProfile,
          isGuest: true
        },
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-guest-selected-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-product-list-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-cart-button-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-empty-cart-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-selected-customer-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });
  });

  describe('ProductSearch', () => {
    it('should render the product search when the current view is set to ProductSearch', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = { ...mockSelectedCustomer };
      const salesOrderCreationProfile = { ...mockSalesOrderCreationProfile };
      const currentView = salesOrderCreationView.ProductSearch;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile,
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.queryByText(selectedCustomer.emailAddress)
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-product-search-wrapper')
      ).toBeInTheDocument();
    });
  });

  describe('OrderSummary', () => {
    it('should render the customer section, the list of products and the additional information section', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = { ...mockSelectedCustomer };
      const salesOrderCreationProfile = { ...mockSalesOrderCreationProfile };
      const currentView = salesOrderCreationView.OrderSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile,
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-selected-customer-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-product-list-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-summary-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-cart-header-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-empty-cart-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-guest-selected-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });

    it('should render as guest, the list of products and the additional information section', () => {
      const selectedProducts = [...mockSelectedProducts];
      const selectedCustomer = undefined;
      const currentView = salesOrderCreationView.OrderSummary;
      (useAppDispatch as jest.Mock).mockReturnValue(jest.fn());
      (useAppSelector as jest.Mock).mockReturnValue({
        selectedProducts,
        selectedCustomer,
        salesOrderCreationProfile: {
          ...mockSalesOrderCreationProfile,
          isGuest: true
        },
        currentView
      });
      renderWithProviders(<SalesOrderCreationPage />);

      expect(
        screen.getByTestId('sales-order-guest-selected-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-product-list-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('sales-order-summary-header-wrapper')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-cart-header-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-empty-cart-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-selected-customer-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-customer-search-wrapper')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('sales-order-product-search-wrapper')
      ).not.toBeInTheDocument();
    });
  });
});

const mockSalesOrderCreationProfile = {
  isGuest: false,
  accountNumber: '980729',
  isTaxExempt: false
};

const mockSelectedCustomer = {
  auth0Id: '',
  emailAddress: '<EMAIL>',
  userId: '0c56719b-e341-4f58-887c-0548ab5f2715',
  address: {
    addressLine1: '4710 Farwell St',
    addressLine2: '11122',
    city: 'McFarland',
    countryCode: 'USA',
    postalCode: '53558',
    state: 'WI'
  },
  firstName: 'Test',
  lastName: 'Test',
  phoneNumber: '**********'
};

const mockSelectedProducts: SalesOrderProduct[] = [
  {
    product: {
      id: 1,
      isRbr: false,
      sku: '300420',
      skuAlias: '3004-20',
      description: 'M18 FUEL™ HATCHET™ 8" Pruning Saw',
      hasValidIncludedItems: false,
      isHeatedGear: false,
      isBranchRepair: false
    },
    quantity: 1,
    availableQuantity: 10,
    inventoryLoading: false
  },
  {
    product: {
      id: 2,
      isRbr: false,
      sku: '093020',
      skuAlias: '0930-20',
      description: 'M18 Fuel 12 Gallon Db Wet/Dry Vacuum',
      isLifetimeWarranty: false,
      warrantyDescription: '5 Year Limited Warranty',
      hasValidIncludedItems: false,
      isHeatedGear: false,
      isBranchRepair: false
    },
    quantity: 1,
    availableQuantity: 10,
    inventoryLoading: false
  }
];
