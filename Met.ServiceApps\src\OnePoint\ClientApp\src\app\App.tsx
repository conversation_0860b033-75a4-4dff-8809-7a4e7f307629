'use client';

import { LanguageProvider } from '@/api';
import { AppInsightsTrackingProvider, ErrorBoundary } from '@/appInsights';
import { AuthProvider, PageAuthorization } from '@/auth';
import { ConfigProvider } from '@/config';
import { AppWrapper } from '@/design/AppWrapper';
import { LIGHT_THEME } from '@/design/atoms';
import { Snackbar as GlobalSnackbar } from '@/design/molecules';
import { PageLoadingProvider } from '@/design/organisms';
import { Unauthorized, selectErrorState } from '@/features/error';
import { hideSnackbar, selectSnackbarState } from '@/features/snackbar';
import { StoreProvider } from '@/store';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { useTranslation } from 'next-export-i18n';
import { usePathname } from 'next/navigation';
import React, { ReactNode } from 'react';
import TagManager from 'react-gtm-module';

//IOS requires webkit-fill since it handles vh different than browsers
interface AppProps {
  children: ReactNode;
}

const ResolvePath: React.FC<AppProps> = ({ children }: AppProps) => {
  const pathname = usePathname() ?? '';
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { open, message, messageKey, severity, isApiError } =
    useAppSelector(selectSnackbarState);
  const { unauthorizedScreenOpen } = useAppSelector(selectErrorState);
  const snackbarMessage: string = messageKey ? t(messageKey) : message;

  TagManager.dataLayer({
    dataLayer: {
      event: 'pageChanged',
      url: pathname
    }
  });

  return (
    <>
      <GlobalSnackbar
        open={open}
        message={snackbarMessage}
        severity={severity}
        isApiError={isApiError}
        handleClose={() => dispatch(hideSnackbar())}
      />
      {unauthorizedScreenOpen ? <Unauthorized /> : children}
    </>
  );
};

export function App({ children }: AppProps) {
  return (
    <AppInsightsTrackingProvider>
      <StoreProvider>
        <ThemeProvider theme={LIGHT_THEME}>
          <CssBaseline enableColorScheme />
          <ErrorBoundary>
            <PageLoadingProvider>
              <ConfigProvider>
                <LanguageProvider>
                  <AuthProvider>
                    <AppWrapper>
                      <PageAuthorization>
                        <ResolvePath>{children}</ResolvePath>
                      </PageAuthorization>
                    </AppWrapper>
                  </AuthProvider>
                </LanguageProvider>
              </ConfigProvider>
            </PageLoadingProvider>
          </ErrorBoundary>
        </ThemeProvider>
      </StoreProvider>
    </AppInsightsTrackingProvider>
  );
}
