import { UsersAccountListIcon } from '@/design/atoms';
import { Button } from '@/design/molecules';

import { AccountSearchItemHeader, SiteItem } from '@/features/account';
import {
  Box,
  Card,
  CardContent,
  Divider,
  Grid,
  Typography
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'next-export-i18n';

interface Props {
  site: SiteItem;
  isViewMode: boolean;
  onAccountSelected: (siteNumber: string, site: SiteItem) => void;
  searchText?: string;
}

export const AccountSearchItem = (props: Props) => {
  const colonSymbol = ':';
  const verticalBar = '|';
  const { site } = props;
  const { t } = useTranslation();

  const handleAccountButtonClick = (siteNumber: string) => {
    props.onAccountSelected(siteNumber, site);
  };
  return (
    <Grid
      key={site.id}
      data-testid={site.id}
      container
      sx={{ flexDirection: 'column' }}
    >
      <Divider sx={{ color: 'neutral.50' }} />
      <AccountSearchItemHeader
        id={site.id}
        parentAccountName={site.parentAccount.accountDescription}
        parentAccountNumber={site.parentAccount.accountNumber}
      />

      <AccountSearchItemWrapper
        sx={{
          borderRadius: 'unset',
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: 'neutral.200',
            '& .iconServiceContactNotFound': {
              color: 'neutral.400'
            }
          },
          padding: '0px 16px',
          borderRight: '1px Solid',
          borderLeft: '1px Solid',
          borderColor: 'neutral.400'
        }}
      >
        <Grid container direction="column">
          {/* Account information */}
          <CardContent
            data-testid="customerCardContent-testId"
            sx={{
              flexGrow: 1,
              px: 'unset',
              py: '16px',
              cursor: 'pointer',
              width: '100%'
            }}
            onClick={() =>
              props.isViewMode
                ? handleAccountButtonClick(site.siteNumber)
                : props.onAccountSelected(site.siteNumber ?? '', site)
            }
          >
            <Grid container>
              <Grid
                item
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '16px',
                  flex: '1 0 0',
                  justifyContent: 'space-between'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    gap: '16px'
                  }}
                >
                  <UsersAccountListIcon
                    data-testid="usersAccountListIcon-testId"
                    viewBox="0 0 56 56"
                    sx={{
                      width: '56px',
                      height: '56px',
                      color: 'primary.700'
                    }}
                  />
                  <Box
                    style={{
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <Box
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        display: 'flex',
                        gap: '8px'
                      }}
                    >
                      <Typography
                        variant="p3"
                        color="neutral.800"
                        data-testid={`accountName${site.id}-testId`}
                        sx={{ fontWeight: '600' }}
                        noWrap={true}
                      >
                        {site.siteName}
                      </Typography>
                      <Typography
                        variant="p3"
                        color="neutral.800"
                        sx={{ fontWeight: '400' }}
                        noWrap={true}
                      >
                        {verticalBar}
                      </Typography>
                      <Typography
                        variant="p3"
                        color="neutral.800"
                        sx={{ fontWeight: '400' }}
                        noWrap={true}
                      >
                        {t('features.account.search.searchResult.siteNumber')}
                        {colonSymbol}
                      </Typography>
                      <Typography
                        variant="p3"
                        color="neutral.800"
                        data-testid={`accountNumber${site.id}-testId`}
                        sx={{ fontWeight: '600' }}
                        noWrap={true}
                      >
                        {site.siteNumber}
                      </Typography>
                    </Box>
                    <Typography
                      variant="p2"
                      color="neutral.500"
                      sx={{ fontWeight: '400' }}
                      data-testid={`accountAddress-${site.id}`}
                      noWrap={true}
                    >
                      {site.addressLine1 && `${site.addressLine1}, `}
                      {site.city && `${site.city}, `}
                      {site.state && `${site.state} `}
                      {site.postalCode}
                    </Typography>
                  </Box>
                </Box>
                <Button
                  data-testid="selectAccountButton-testId"
                  size="medium"
                  variant="secondary"
                >
                  {props.isViewMode ? t('common.view') : t('common.select')}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Grid>
      </AccountSearchItemWrapper>
    </Grid>
  );
};

export const AccountSearchItemWrapper = styled(Card)(({ theme }) => ({
  ...theme.typography.body2,
  background: 'none',
  color: theme.palette.text.secondary,
  display: 'flex',
  alignItems: 'center',
  alignSelf: 'stretch',
  '&:hover .iconServiceContactNotFound': {
    color: 'neutral.400'
  }
}));
