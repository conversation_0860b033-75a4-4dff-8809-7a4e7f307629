"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_azure_msal-browser_dist_controllers_NestedAppAuthController_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthController: function() { return /* binding */ NestedAppAuthController; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/crypto/ICrypto.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/telemetry/performance/PerformanceEvent.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/utils/TimeUtils.mjs\");\n/* harmony import */ var _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/BrowserConstants.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/utils/BrowserConstants.mjs\");\n/* harmony import */ var _crypto_CryptoOps_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../crypto/CryptoOps.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/crypto/CryptoOps.mjs\");\n/* harmony import */ var _naa_mapping_NestedAppAuthAdapter_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../naa/mapping/NestedAppAuthAdapter.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs\");\n/* harmony import */ var _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/NestedAppAuthError.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs\");\n/* harmony import */ var _event_EventHandler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../event/EventHandler.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/event/EventHandler.mjs\");\n/* harmony import */ var _event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../event/EventType.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/event/EventType.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n\n\n\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nclass NestedAppAuthController {\n    constructor(operatingContext) {\n        this.operatingContext = operatingContext;\n        const proxy = this.operatingContext.getBridgeProxy();\n        if (proxy !== undefined) {\n            this.bridgeProxy = proxy;\n        }\n        else {\n            throw new Error(\"unexpected: bridgeProxy is undefined\");\n        }\n        // Set the configuration.\n        this.config = operatingContext.getConfig();\n        // Initialize logger\n        this.logger = this.operatingContext.getLogger();\n        // Initialize performance client\n        this.performanceClient = this.config.telemetry.client;\n        // Initialize the crypto class.\n        this.browserCrypto = operatingContext.isBrowserEnvironment()\n            ? new _crypto_CryptoOps_mjs__WEBPACK_IMPORTED_MODULE_0__.CryptoOps(this.logger, this.performanceClient)\n            : _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_CRYPTO_IMPLEMENTATION;\n        this.eventHandler = new _event_EventHandler_mjs__WEBPACK_IMPORTED_MODULE_2__.EventHandler(this.logger, this.browserCrypto);\n        this.nestedAppAuthAdapter = new _naa_mapping_NestedAppAuthAdapter_mjs__WEBPACK_IMPORTED_MODULE_3__.NestedAppAuthAdapter(this.config.auth.clientId, this.config.auth.clientCapabilities, this.browserCrypto, this.logger);\n    }\n    getBrowserStorage() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    getEventHandler() {\n        return this.eventHandler;\n    }\n    static async createController(operatingContext) {\n        const controller = new NestedAppAuthController(operatingContext);\n        return Promise.resolve(controller);\n    }\n    initialize() {\n        // do nothing not required by this controller\n        return Promise.resolve();\n    }\n    async acquireTokenInteractive(request) {\n        this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_START, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, request);\n        const atPopupMeasurement = this.performanceClient.startMeasurement(_azure_msal_common__WEBPACK_IMPORTED_MODULE_7__.PerformanceEvents.AcquireTokenPopup, request.correlationId);\n        atPopupMeasurement?.add({ nestedAppAuthRequest: true });\n        try {\n            const naaRequest = this.nestedAppAuthAdapter.toNaaTokenRequest(request);\n            const reqTimestamp = _azure_msal_common__WEBPACK_IMPORTED_MODULE_8__.TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenInteractive(naaRequest);\n            const result = this.nestedAppAuthAdapter.fromNaaTokenResponse(naaRequest, response, reqTimestamp);\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_SUCCESS, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, result);\n            atPopupMeasurement.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n            atPopupMeasurement.end({\n                success: true,\n                requestId: result.requestId,\n            });\n            return result;\n        }\n        catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_FAILURE, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, null, e);\n            atPopupMeasurement.end({\n                errorCode: error.errorCode,\n                subErrorCode: error.subError,\n                success: false,\n            });\n            throw error;\n        }\n    }\n    async acquireTokenSilentInternal(request) {\n        this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_START, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, request);\n        const ssoSilentMeasurement = this.performanceClient.startMeasurement(_azure_msal_common__WEBPACK_IMPORTED_MODULE_7__.PerformanceEvents.SsoSilent, request.correlationId);\n        ssoSilentMeasurement?.increment({\n            visibilityChangeCount: 0,\n        });\n        ssoSilentMeasurement?.add({\n            nestedAppAuthRequest: true,\n        });\n        try {\n            const naaRequest = this.nestedAppAuthAdapter.toNaaTokenRequest(request);\n            const reqTimestamp = _azure_msal_common__WEBPACK_IMPORTED_MODULE_8__.TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenSilent(naaRequest);\n            const result = this.nestedAppAuthAdapter.fromNaaTokenResponse(naaRequest, response, reqTimestamp);\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_SUCCESS, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, result);\n            ssoSilentMeasurement?.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n            ssoSilentMeasurement?.end({\n                success: true,\n                requestId: result.requestId,\n            });\n            return result;\n        }\n        catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_FAILURE, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, null, e);\n            ssoSilentMeasurement?.end({\n                errorCode: error.errorCode,\n                subErrorCode: error.subError,\n                success: false,\n            });\n            throw error;\n        }\n    }\n    async acquireTokenPopup(request) {\n        return this.acquireTokenInteractive(request);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenRedirect(request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    async acquireTokenSilent(silentRequest) {\n        return this.acquireTokenSilentInternal(silentRequest);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenByCode(request // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenNative(request, apiId, // eslint-disable-line @typescript-eslint/no-unused-vars\n    accountId // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenByRefreshToken(commonRequest, // eslint-disable-line @typescript-eslint/no-unused-vars\n    silentRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    /**\n     * Adds event callbacks to array\n     * @param callback\n     */\n    addEventCallback(callback) {\n        return this.eventHandler.addEventCallback(callback);\n    }\n    /**\n     * Removes callback with provided id from callback array\n     * @param callbackId\n     */\n    removeEventCallback(callbackId) {\n        this.eventHandler.removeEventCallback(callbackId);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    addPerformanceCallback(callback) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    removePerformanceCallback(callbackId) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    enableAccountStorageEvents() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    disableAccountStorageEvents() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getAccount(accountFilter) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n        // TODO: Look at standard implementation\n    }\n    getAccountByHomeId(homeAccountId) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.homeAccountId === homeAccountId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAccountByLocalId(localId) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.localAccountId === localId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAccountByUsername(userName) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.username === userName) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAllAccounts() {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return [\n                this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount),\n            ];\n        }\n        else {\n            return [];\n        }\n    }\n    handleRedirectPromise(hash // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    loginPopup(request // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        if (request !== undefined) {\n            return this.acquireTokenInteractive(request);\n        }\n        else {\n            throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    loginRedirect(request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    logout(logoutRequest) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    logoutRedirect(logoutRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    logoutPopup(logoutRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    ssoSilent(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    request) {\n        return this.acquireTokenSilentInternal(request);\n    }\n    getTokenCache() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    /**\n     * Returns the logger instance\n     */\n    getLogger() {\n        return this.logger;\n    }\n    /**\n     * Replaces the default logger set in configurations with new Logger with new configurations\n     * @param logger Logger instance\n     */\n    setLogger(logger) {\n        this.logger = logger;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setActiveAccount(account) {\n        /*\n         * StandardController uses this to allow the developer to set the active account\n         * in the nested app auth scenario the active account is controlled by the app hosting the nested app\n         */\n        this.logger.warning(\"nestedAppAuth does not support setActiveAccount\");\n        return;\n    }\n    getActiveAccount() {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n        }\n        else {\n            return null;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    initializeWrapperLibrary(sku, version) {\n        /*\n         * Standard controller uses this to set the sku and version of the wrapper library in the storage\n         * we do nothing here\n         */\n        return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setNavigationClient(navigationClient) {\n        this.logger.warning(\"setNavigationClient is not supported in nested app auth\");\n    }\n    getConfiguration() {\n        return this.config;\n    }\n    isBrowserEnv() {\n        return this.operatingContext.isBrowserEnvironment();\n    }\n    getBrowserCrypto() {\n        return this.browserCrypto;\n    }\n    getPerformanceClient() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    getRedirectResponse() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    preflightBrowserEnvironmentCheck(interactionType, // eslint-disable-line @typescript-eslint/no-unused-vars\n    setInteractionInProgress // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async clearCache(logoutRequest) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async hydrateCache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    result, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthController.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthError: function() { return /* binding */ NestedAppAuthError; },\n/* harmony export */   NestedAppAuthErrorMessage: function() { return /* binding */ NestedAppAuthErrorMessage; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/AuthError.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n/**\n * NestedAppAuthErrorMessage class containing string constants used by error codes and messages.\n */\nconst NestedAppAuthErrorMessage = {\n    unsupportedMethod: {\n        code: \"unsupported_method\",\n        desc: \"The PKCE code challenge and verifier could not be generated.\",\n    },\n};\nclass NestedAppAuthError extends _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthError {\n    constructor(errorCode, errorMessage) {\n        super(errorCode, errorMessage);\n        Object.setPrototypeOf(this, NestedAppAuthError.prototype);\n        this.name = \"NestedAppAuthError\";\n    }\n    static createUnsupportedError() {\n        return new NestedAppAuthError(NestedAppAuthErrorMessage.unsupportedMethod.code, NestedAppAuthErrorMessage.unsupportedMethod.desc);\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthError.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBridgeError: function() { return /* binding */ isBridgeError; }\n/* harmony export */ });\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nfunction isBridgeError(error) {\n    return error.status !== undefined;\n}\n\n\n//# sourceMappingURL=BridgeError.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYXp1cmUvbXNhbC1icm93c2VyL2Rpc3QvbmFhL0JyaWRnZUVycm9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV5QjtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGF6dXJlL21zYWwtYnJvd3Nlci9kaXN0L25hYS9CcmlkZ2VFcnJvci5tanM/ZTE4NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEgQGF6dXJlL21zYWwtYnJvd3NlciB2My42LjAgMjAyMy0xMi0wMSAqL1xuJ3VzZSBzdHJpY3QnO1xuLypcbiAqIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuICovXG5mdW5jdGlvbiBpc0JyaWRnZUVycm9yKGVycm9yKSB7XG4gICAgcmV0dXJuIGVycm9yLnN0YXR1cyAhPT0gdW5kZWZpbmVkO1xufVxuXG5leHBvcnQgeyBpc0JyaWRnZUVycm9yIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1CcmlkZ2VFcnJvci5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BridgeStatusCode: function() { return /* binding */ BridgeStatusCode; }\n/* harmony export */ });\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nvar BridgeStatusCode;\n(function (BridgeStatusCode) {\n    BridgeStatusCode[\"USER_INTERACTION_REQUIRED\"] = \"USER_INTERACTION_REQUIRED\";\n    BridgeStatusCode[\"USER_CANCEL\"] = \"USER_CANCEL\";\n    BridgeStatusCode[\"NO_NETWORK\"] = \"NO_NETWORK\";\n    BridgeStatusCode[\"TRANSIENT_ERROR\"] = \"TRANSIENT_ERROR\";\n    BridgeStatusCode[\"PERSISTENT_ERROR\"] = \"PERSISTENT_ERROR\";\n    BridgeStatusCode[\"DISABLED\"] = \"DISABLED\";\n    BridgeStatusCode[\"ACCOUNT_UNAVAILABLE\"] = \"ACCOUNT_UNAVAILABLE\";\n    BridgeStatusCode[\"NESTED_APP_AUTH_UNAVAILABLE\"] = \"NESTED_APP_AUTH_UNAVAILABLE\";\n})(BridgeStatusCode || (BridgeStatusCode = {}));\n\n\n//# sourceMappingURL=BridgeStatusCode.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYXp1cmUvbXNhbC1icm93c2VyL2Rpc3QvbmFhL0JyaWRnZVN0YXR1c0NvZGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsNENBQTRDOztBQUVqQjtBQUM1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGF6dXJlL21zYWwtYnJvd3Nlci9kaXN0L25hYS9CcmlkZ2VTdGF0dXNDb2RlLm1qcz83Njk5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qISBAYXp1cmUvbXNhbC1icm93c2VyIHYzLjYuMCAyMDIzLTEyLTAxICovXG4ndXNlIHN0cmljdCc7XG4vKlxuICogQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG4gKi9cbnZhciBCcmlkZ2VTdGF0dXNDb2RlO1xuKGZ1bmN0aW9uIChCcmlkZ2VTdGF0dXNDb2RlKSB7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIlVTRVJfSU5URVJBQ1RJT05fUkVRVUlSRURcIl0gPSBcIlVTRVJfSU5URVJBQ1RJT05fUkVRVUlSRURcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiVVNFUl9DQU5DRUxcIl0gPSBcIlVTRVJfQ0FOQ0VMXCI7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIk5PX05FVFdPUktcIl0gPSBcIk5PX05FVFdPUktcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiVFJBTlNJRU5UX0VSUk9SXCJdID0gXCJUUkFOU0lFTlRfRVJST1JcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiUEVSU0lTVEVOVF9FUlJPUlwiXSA9IFwiUEVSU0lTVEVOVF9FUlJPUlwiO1xuICAgIEJyaWRnZVN0YXR1c0NvZGVbXCJESVNBQkxFRFwiXSA9IFwiRElTQUJMRURcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiQUNDT1VOVF9VTkFWQUlMQUJMRVwiXSA9IFwiQUNDT1VOVF9VTkFWQUlMQUJMRVwiO1xuICAgIEJyaWRnZVN0YXR1c0NvZGVbXCJORVNURURfQVBQX0FVVEhfVU5BVkFJTEFCTEVcIl0gPSBcIk5FU1RFRF9BUFBfQVVUSF9VTkFWQUlMQUJMRVwiO1xufSkoQnJpZGdlU3RhdHVzQ29kZSB8fCAoQnJpZGdlU3RhdHVzQ29kZSA9IHt9KSk7XG5cbmV4cG9ydCB7IEJyaWRnZVN0YXR1c0NvZGUgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUJyaWRnZVN0YXR1c0NvZGUubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthAdapter: function() { return /* binding */ NestedAppAuthAdapter; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/request/RequestParameterBuilder.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/utils/StringUtils.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/utils/Constants.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/ClientAuthError.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/ClientAuthErrorCodes.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/account/AuthToken.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/ServerError.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/InteractionRequiredAuthError.mjs\");\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @azure/msal-common */ \"(app-pages-browser)/./node_modules/@azure/msal-common/dist/error/AuthError.mjs\");\n/* harmony import */ var _BridgeError_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../BridgeError.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs\");\n/* harmony import */ var _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../BridgeStatusCode.mjs */ \"(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nclass NestedAppAuthAdapter {\n    constructor(clientId, clientCapabilities, crypto, logger) {\n        this.clientId = clientId;\n        this.clientCapabilities = clientCapabilities;\n        this.crypto = crypto;\n        this.logger = logger;\n    }\n    toNaaTokenRequest(request) {\n        let extraParams;\n        if (request.extraQueryParameters === undefined) {\n            extraParams = new Map();\n        }\n        else {\n            extraParams = new Map(Object.entries(request.extraQueryParameters));\n        }\n        const requestBuilder = new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.RequestParameterBuilder();\n        const claims = requestBuilder.addClientCapabilitiesToClaims(request.claims, this.clientCapabilities);\n        const tokenRequest = {\n            userObjectId: request.account?.homeAccountId,\n            clientId: this.clientId,\n            authority: request.authority,\n            scope: request.scopes.join(\" \"),\n            correlationId: request.correlationId !== undefined\n                ? request.correlationId\n                : this.crypto.createNewGuid(),\n            nonce: request.nonce,\n            claims: !_azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.StringUtils.isEmptyObj(claims) ? claims : undefined,\n            state: request.state,\n            authenticationScheme: request.authenticationScheme || _azure_msal_common__WEBPACK_IMPORTED_MODULE_2__.AuthenticationScheme.BEARER,\n            extraParameters: extraParams,\n        };\n        return tokenRequest;\n    }\n    fromNaaTokenResponse(request, response, reqTimestamp) {\n        if (!response.id_token || !response.access_token) {\n            throw (0,_azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.createClientAuthError)(_azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.nullOrEmptyToken);\n        }\n        const expiresOn = new Date((reqTimestamp + (response.expires_in || 0)) * 1000);\n        const idTokenClaims = _azure_msal_common__WEBPACK_IMPORTED_MODULE_5__.extractTokenClaims(response.id_token, this.crypto.base64Decode);\n        const account = this.fromNaaAccountInfo(response.account, idTokenClaims);\n        const authenticationResult = {\n            authority: response.authority || account.environment,\n            uniqueId: account.localAccountId,\n            tenantId: account.tenantId,\n            scopes: response.scope.split(\" \"),\n            account,\n            idToken: response.id_token !== undefined ? response.id_token : \"\",\n            idTokenClaims,\n            accessToken: response.access_token,\n            fromCache: true,\n            expiresOn: expiresOn,\n            tokenType: request.authenticationScheme || _azure_msal_common__WEBPACK_IMPORTED_MODULE_2__.AuthenticationScheme.BEARER,\n            correlationId: request.correlationId,\n            extExpiresOn: expiresOn,\n            state: request.state,\n        };\n        return authenticationResult;\n    }\n    /*\n     *  export type AccountInfo = {\n     *     homeAccountId: string;\n     *     environment: string;\n     *     tenantId: string;\n     *     username: string;\n     *     localAccountId: string;\n     *     name?: string;\n     *     idToken?: string;\n     *     idTokenClaims?: TokenClaims & {\n     *         [key: string]:\n     *             | string\n     *             | number\n     *             | string[]\n     *             | object\n     *             | undefined\n     *             | unknown;\n     *     };\n     *     nativeAccountId?: string;\n     *     authorityType?: string;\n     * };\n     */\n    fromNaaAccountInfo(fromAccount, idTokenClaims) {\n        const effectiveIdTokenClaims = idTokenClaims || fromAccount.idTokenClaims;\n        const localAccountId = fromAccount.localAccountId ||\n            effectiveIdTokenClaims?.oid ||\n            effectiveIdTokenClaims?.sub ||\n            \"\";\n        const tenantId = fromAccount.tenantId || effectiveIdTokenClaims?.tid || \"\";\n        const homeAccountId = fromAccount.homeAccountId || `${localAccountId}.${tenantId}`;\n        const username = fromAccount.username ||\n            effectiveIdTokenClaims?.preferred_username ||\n            \"\";\n        const name = fromAccount.name || effectiveIdTokenClaims?.name;\n        const account = {\n            homeAccountId,\n            environment: fromAccount.environment,\n            tenantId,\n            username,\n            localAccountId,\n            name,\n            idToken: fromAccount.idToken,\n            idTokenClaims: effectiveIdTokenClaims,\n        };\n        return account;\n    }\n    /**\n     *\n     * @param error BridgeError\n     * @returns AuthError, ClientAuthError, ClientConfigurationError, ServerError, InteractionRequiredError\n     */\n    fromBridgeError(error) {\n        if ((0,_BridgeError_mjs__WEBPACK_IMPORTED_MODULE_6__.isBridgeError)(error)) {\n            switch (error.status) {\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.USER_CANCEL:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.userCanceled);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.NO_NETWORK:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.noNetworkConnectivity);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.ACCOUNT_UNAVAILABLE:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.noAccountFound);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.DISABLED:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.nestedAppAuthBridgeDisabled);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.NESTED_APP_AUTH_UNAVAILABLE:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_3__.ClientAuthError(error.code ||\n                        _azure_msal_common__WEBPACK_IMPORTED_MODULE_4__.nestedAppAuthBridgeDisabled, error.description);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.TRANSIENT_ERROR:\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.PERSISTENT_ERROR:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_8__.ServerError(error.code, error.description);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_7__.BridgeStatusCode.USER_INTERACTION_REQUIRED:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_9__.InteractionRequiredAuthError(error.code, error.description);\n                default:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_10__.AuthError(error.code, error.description);\n            }\n        }\n        else {\n            return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_10__.AuthError(\"unknown_error\", \"An unknown error occurred\");\n        }\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthAdapter.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs\n"));

/***/ })

}]);