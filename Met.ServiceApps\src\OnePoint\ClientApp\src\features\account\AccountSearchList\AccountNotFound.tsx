import { UserSearchIcon } from '@/design/atoms';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';

export const AccountNotFound = () => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: 'flex',
        mt: '24px',
        marginBottom: '24px',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '12px'
      }}
    >
      <UserSearchIcon
        data-testid="noRecordsFoundIcon-testId"
        sx={{
          width: '48px',
          height: '38px'
        }}
      />
      <Typography
        data-testid="noRecordsFoundText-testId"
        variant="p3"
        color="neutral.500"
      >
        {t('features.search.noRecordsFound')}
      </Typography>
    </Box>
  );
};
