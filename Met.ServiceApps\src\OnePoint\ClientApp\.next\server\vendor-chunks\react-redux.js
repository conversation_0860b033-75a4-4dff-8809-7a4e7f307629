"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-redux";
exports.ids = ["vendor-chunks/react-redux"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-redux/dist/react-redux.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/react-redux/dist/react-redux.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider_default),\n/* harmony export */   ReactReduxContext: () => (/* binding */ ReactReduxContext),\n/* harmony export */   batch: () => (/* binding */ batch2),\n/* harmony export */   connect: () => (/* binding */ connect_default),\n/* harmony export */   createDispatchHook: () => (/* binding */ createDispatchHook),\n/* harmony export */   createSelectorHook: () => (/* binding */ createSelectorHook),\n/* harmony export */   createStoreHook: () => (/* binding */ createStoreHook),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   useDispatch: () => (/* binding */ useDispatch),\n/* harmony export */   useSelector: () => (/* binding */ useSelector),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/with-selector.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n// src/index.ts\n\n\n// src/utils/reactBatchedUpdates.ts\n\n// src/utils/batch.ts\nfunction defaultNoopBatch(callback) {\n    callback();\n}\nvar batch = defaultNoopBatch;\nvar setBatch = (newBatch)=>batch = newBatch;\nvar getBatch = ()=>batch;\n// src/utils/react.ts\n\nvar React = // prettier-ignore\n// @ts-ignore\n true ? react__WEBPACK_IMPORTED_MODULE_0__ : /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)));\n// src/components/Context.ts\nvar ContextKey = Symbol.for(`react-redux-context`);\nvar gT = typeof globalThis !== \"undefined\" ? globalThis : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {};\nfunction getContext() {\n    if (!React.createContext) return {};\n    const contextMap = gT[ContextKey] ?? (gT[ContextKey] = /* @__PURE__ */ new Map());\n    let realContext = contextMap.get(React.createContext);\n    if (!realContext) {\n        realContext = React.createContext(null);\n        if (true) {\n            realContext.displayName = \"ReactRedux\";\n        }\n        contextMap.set(React.createContext, realContext);\n    }\n    return realContext;\n}\nvar ReactReduxContext = /* @__PURE__ */ getContext();\n// src/utils/useSyncExternalStore.ts\nvar notInitialized = ()=>{\n    throw new Error(\"uSES not initialized!\");\n};\n// src/hooks/useReduxContext.ts\nfunction createReduxContextHook(context = ReactReduxContext) {\n    return function useReduxContext2() {\n        const contextValue = React.useContext(context);\n        if ( true && !contextValue) {\n            throw new Error(\"could not find react-redux context value; please ensure the component is wrapped in a <Provider>\");\n        }\n        return contextValue;\n    };\n}\nvar useReduxContext = /* @__PURE__ */ createReduxContextHook();\n// src/hooks/useSelector.ts\nvar useSyncExternalStoreWithSelector = notInitialized;\nvar initializeUseSelector = (fn)=>{\n    useSyncExternalStoreWithSelector = fn;\n};\nvar refEquality = (a, b)=>a === b;\nfunction createSelectorHook(context = ReactReduxContext) {\n    const useReduxContext2 = context === ReactReduxContext ? useReduxContext : createReduxContextHook(context);\n    return function useSelector2(selector, equalityFnOrOptions = {}) {\n        const { equalityFn = refEquality, devModeChecks = {} } = typeof equalityFnOrOptions === \"function\" ? {\n            equalityFn: equalityFnOrOptions\n        } : equalityFnOrOptions;\n        if (true) {\n            if (!selector) {\n                throw new Error(`You must pass a selector to useSelector`);\n            }\n            if (typeof selector !== \"function\") {\n                throw new Error(`You must pass a function as a selector to useSelector`);\n            }\n            if (typeof equalityFn !== \"function\") {\n                throw new Error(`You must pass a function as an equality function to useSelector`);\n            }\n        }\n        const { store, subscription, getServerState, stabilityCheck, identityFunctionCheck } = useReduxContext2();\n        const firstRun = React.useRef(true);\n        const wrappedSelector = React.useCallback({\n            [selector.name] (state) {\n                const selected = selector(state);\n                if (true) {\n                    const { identityFunctionCheck: finalIdentityFunctionCheck, stabilityCheck: finalStabilityCheck } = {\n                        stabilityCheck,\n                        identityFunctionCheck,\n                        ...devModeChecks\n                    };\n                    if (finalStabilityCheck === \"always\" || finalStabilityCheck === \"once\" && firstRun.current) {\n                        const toCompare = selector(state);\n                        if (!equalityFn(selected, toCompare)) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned a different result when called with the same parameters. This can lead to unnecessary rerenders.\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization\", {\n                                state,\n                                selected,\n                                selected2: toCompare,\n                                stack\n                            });\n                        }\n                    }\n                    if (finalIdentityFunctionCheck === \"always\" || finalIdentityFunctionCheck === \"once\" && firstRun.current) {\n                        if (selected === state) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned the root state when called. This can lead to unnecessary rerenders.\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.\", {\n                                stack\n                            });\n                        }\n                    }\n                    if (firstRun.current) firstRun.current = false;\n                }\n                return selected;\n            }\n        }[selector.name], [\n            selector,\n            stabilityCheck,\n            devModeChecks.stabilityCheck\n        ]);\n        const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n        React.useDebugValue(selectedState);\n        return selectedState;\n    };\n}\nvar useSelector = /* @__PURE__ */ createSelectorHook();\n// src/utils/react-is.ts\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.element\");\nvar REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\nvar REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nvar REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\");\nvar REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\nvar REACT_PROVIDER_TYPE = Symbol.for(\"react.provider\");\nvar REACT_CONTEXT_TYPE = Symbol.for(\"react.context\");\nvar REACT_SERVER_CONTEXT_TYPE = Symbol.for(\"react.server_context\");\nvar REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\");\nvar REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\");\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\");\nvar REACT_MEMO_TYPE = Symbol.for(\"react.memo\");\nvar REACT_LAZY_TYPE = Symbol.for(\"react.lazy\");\nvar REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\");\nvar REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nfunction isValidElementType(type) {\n    if (typeof type === \"string\" || typeof type === \"function\") {\n        return true;\n    }\n    if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE) {\n        return true;\n    }\n    if (typeof type === \"object\" && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n        // types supported by any Flight configuration anywhere since\n        // we don't know which Flight build this will end up being used\n        // with.\n        type.$$typeof === REACT_CLIENT_REFERENCE || type.getModuleId !== void 0) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction typeOf(object) {\n    if (typeof object === \"object\" && object !== null) {\n        const $$typeof = object.$$typeof;\n        switch($$typeof){\n            case REACT_ELEMENT_TYPE:\n                {\n                    const type = object.type;\n                    switch(type){\n                        case REACT_FRAGMENT_TYPE:\n                        case REACT_PROFILER_TYPE:\n                        case REACT_STRICT_MODE_TYPE:\n                        case REACT_SUSPENSE_TYPE:\n                        case REACT_SUSPENSE_LIST_TYPE:\n                            return type;\n                        default:\n                            {\n                                const $$typeofType = type && type.$$typeof;\n                                switch($$typeofType){\n                                    case REACT_SERVER_CONTEXT_TYPE:\n                                    case REACT_CONTEXT_TYPE:\n                                    case REACT_FORWARD_REF_TYPE:\n                                    case REACT_LAZY_TYPE:\n                                    case REACT_MEMO_TYPE:\n                                    case REACT_PROVIDER_TYPE:\n                                        return $$typeofType;\n                                    default:\n                                        return $$typeof;\n                                }\n                            }\n                    }\n                }\n            case REACT_PORTAL_TYPE:\n                {\n                    return $$typeof;\n                }\n        }\n    }\n    return void 0;\n}\nfunction isContextConsumer(object) {\n    return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isMemo(object) {\n    return typeOf(object) === REACT_MEMO_TYPE;\n}\n// src/utils/warning.ts\nfunction warning(message) {\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (e) {}\n}\n// src/connect/verifySubselectors.ts\nfunction verify(selector, methodName) {\n    if (!selector) {\n        throw new Error(`Unexpected value for ${methodName} in connect.`);\n    } else if (methodName === \"mapStateToProps\" || methodName === \"mapDispatchToProps\") {\n        if (!Object.prototype.hasOwnProperty.call(selector, \"dependsOnOwnProps\")) {\n            warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n        }\n    }\n}\nfunction verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n    verify(mapStateToProps, \"mapStateToProps\");\n    verify(mapDispatchToProps, \"mapDispatchToProps\");\n    verify(mergeProps, \"mergeProps\");\n}\n// src/connect/selectorFactory.ts\nfunction pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, { areStatesEqual, areOwnPropsEqual, areStatePropsEqual }) {\n    let hasRunAtLeastOnce = false;\n    let state;\n    let ownProps;\n    let stateProps;\n    let dispatchProps;\n    let mergedProps;\n    function handleFirstCall(firstState, firstOwnProps) {\n        state = firstState;\n        ownProps = firstOwnProps;\n        stateProps = mapStateToProps(state, ownProps);\n        dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        hasRunAtLeastOnce = true;\n        return mergedProps;\n    }\n    function handleNewPropsAndNewState() {\n        stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewProps() {\n        if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewState() {\n        const nextStateProps = mapStateToProps(state, ownProps);\n        const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n        stateProps = nextStateProps;\n        if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleSubsequentCalls(nextState, nextOwnProps) {\n        const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n        const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n        state = nextState;\n        ownProps = nextOwnProps;\n        if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n        if (propsChanged) return handleNewProps();\n        if (stateChanged) return handleNewState();\n        return mergedProps;\n    }\n    return function pureFinalPropsSelector(nextState, nextOwnProps) {\n        return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n    };\n}\nfunction finalPropsSelectorFactory(dispatch, { initMapStateToProps, initMapDispatchToProps, initMergeProps, ...options }) {\n    const mapStateToProps = initMapStateToProps(dispatch, options);\n    const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n    const mergeProps = initMergeProps(dispatch, options);\n    if (true) {\n        verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n    }\n    return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}\n// src/utils/bindActionCreators.ts\nfunction bindActionCreators(actionCreators, dispatch) {\n    const boundActionCreators = {};\n    for(const key in actionCreators){\n        const actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = (...args)=>dispatch(actionCreator(...args));\n        }\n    }\n    return boundActionCreators;\n}\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    let proto = Object.getPrototypeOf(obj);\n    if (proto === null) return true;\n    let baseProto = proto;\n    while(Object.getPrototypeOf(baseProto) !== null){\n        baseProto = Object.getPrototypeOf(baseProto);\n    }\n    return proto === baseProto;\n}\n// src/utils/verifyPlainObject.ts\nfunction verifyPlainObject(value, displayName, methodName) {\n    if (!isPlainObject(value)) {\n        warning(`${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`);\n    }\n}\n// src/connect/wrapMapToProps.ts\nfunction wrapMapToPropsConstant(getConstant) {\n    return function initConstantSelector(dispatch) {\n        const constant = getConstant(dispatch);\n        function constantSelector() {\n            return constant;\n        }\n        constantSelector.dependsOnOwnProps = false;\n        return constantSelector;\n    };\n}\nfunction getDependsOnOwnProps(mapToProps) {\n    return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n}\nfunction wrapMapToPropsFunc(mapToProps, methodName) {\n    return function initProxySelector(dispatch, { displayName }) {\n        const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n            return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, void 0);\n        };\n        proxy.dependsOnOwnProps = true;\n        proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n            proxy.mapToProps = mapToProps;\n            proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n            let props = proxy(stateOrDispatch, ownProps);\n            if (typeof props === \"function\") {\n                proxy.mapToProps = props;\n                proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n                props = proxy(stateOrDispatch, ownProps);\n            }\n            if (true) verifyPlainObject(props, displayName, methodName);\n            return props;\n        };\n        return proxy;\n    };\n}\n// src/connect/invalidArgFactory.ts\nfunction createInvalidArgFactory(arg, name) {\n    return (dispatch, options)=>{\n        throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n    };\n}\n// src/connect/mapDispatchToProps.ts\nfunction mapDispatchToPropsFactory(mapDispatchToProps) {\n    return mapDispatchToProps && typeof mapDispatchToProps === \"object\" ? wrapMapToPropsConstant((dispatch)=>// @ts-ignore\n        bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant((dispatch)=>({\n            dispatch\n        })) : typeof mapDispatchToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapDispatchToProps, \"mapDispatchToProps\") : createInvalidArgFactory(mapDispatchToProps, \"mapDispatchToProps\");\n}\n// src/connect/mapStateToProps.ts\nfunction mapStateToPropsFactory(mapStateToProps) {\n    return !mapStateToProps ? wrapMapToPropsConstant(()=>({})) : typeof mapStateToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapStateToProps, \"mapStateToProps\") : createInvalidArgFactory(mapStateToProps, \"mapStateToProps\");\n}\n// src/connect/mergeProps.ts\nfunction defaultMergeProps(stateProps, dispatchProps, ownProps) {\n    return {\n        ...ownProps,\n        ...stateProps,\n        ...dispatchProps\n    };\n}\nfunction wrapMergePropsFunc(mergeProps) {\n    return function initMergePropsProxy(dispatch, { displayName, areMergedPropsEqual }) {\n        let hasRunOnce = false;\n        let mergedProps;\n        return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n            const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n            if (hasRunOnce) {\n                if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n            } else {\n                hasRunOnce = true;\n                mergedProps = nextMergedProps;\n                if (true) verifyPlainObject(mergedProps, displayName, \"mergeProps\");\n            }\n            return mergedProps;\n        };\n    };\n}\nfunction mergePropsFactory(mergeProps) {\n    return !mergeProps ? ()=>defaultMergeProps : typeof mergeProps === \"function\" ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, \"mergeProps\");\n}\n// src/utils/Subscription.ts\nfunction createListenerCollection() {\n    const batch3 = getBatch();\n    let first = null;\n    let last = null;\n    return {\n        clear () {\n            first = null;\n            last = null;\n        },\n        notify () {\n            batch3(()=>{\n                let listener = first;\n                while(listener){\n                    listener.callback();\n                    listener = listener.next;\n                }\n            });\n        },\n        get () {\n            let listeners = [];\n            let listener = first;\n            while(listener){\n                listeners.push(listener);\n                listener = listener.next;\n            }\n            return listeners;\n        },\n        subscribe (callback) {\n            let isSubscribed = true;\n            let listener = last = {\n                callback,\n                next: null,\n                prev: last\n            };\n            if (listener.prev) {\n                listener.prev.next = listener;\n            } else {\n                first = listener;\n            }\n            return function unsubscribe() {\n                if (!isSubscribed || first === null) return;\n                isSubscribed = false;\n                if (listener.next) {\n                    listener.next.prev = listener.prev;\n                } else {\n                    last = listener.prev;\n                }\n                if (listener.prev) {\n                    listener.prev.next = listener.next;\n                } else {\n                    first = listener.next;\n                }\n            };\n        }\n    };\n}\nvar nullListeners = {\n    notify () {},\n    get: ()=>[]\n};\nfunction createSubscription(store, parentSub) {\n    let unsubscribe;\n    let listeners = nullListeners;\n    let subscriptionsAmount = 0;\n    let selfSubscribed = false;\n    function addNestedSub(listener) {\n        trySubscribe();\n        const cleanupListener = listeners.subscribe(listener);\n        let removed = false;\n        return ()=>{\n            if (!removed) {\n                removed = true;\n                cleanupListener();\n                tryUnsubscribe();\n            }\n        };\n    }\n    function notifyNestedSubs() {\n        listeners.notify();\n    }\n    function handleChangeWrapper() {\n        if (subscription.onStateChange) {\n            subscription.onStateChange();\n        }\n    }\n    function isSubscribed() {\n        return selfSubscribed;\n    }\n    function trySubscribe() {\n        subscriptionsAmount++;\n        if (!unsubscribe) {\n            unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n            listeners = createListenerCollection();\n        }\n    }\n    function tryUnsubscribe() {\n        subscriptionsAmount--;\n        if (unsubscribe && subscriptionsAmount === 0) {\n            unsubscribe();\n            unsubscribe = void 0;\n            listeners.clear();\n            listeners = nullListeners;\n        }\n    }\n    function trySubscribeSelf() {\n        if (!selfSubscribed) {\n            selfSubscribed = true;\n            trySubscribe();\n        }\n    }\n    function tryUnsubscribeSelf() {\n        if (selfSubscribed) {\n            selfSubscribed = false;\n            tryUnsubscribe();\n        }\n    }\n    const subscription = {\n        addNestedSub,\n        notifyNestedSubs,\n        handleChangeWrapper,\n        isSubscribed,\n        trySubscribe: trySubscribeSelf,\n        tryUnsubscribe: tryUnsubscribeSelf,\n        getListeners: ()=>listeners\n    };\n    return subscription;\n}\n// src/utils/useIsomorphicLayoutEffect.ts\nvar canUseDOM = !!( false && 0);\nvar useIsomorphicLayoutEffect = canUseDOM ? React.useLayoutEffect : React.useEffect;\n// src/utils/shallowEqual.ts\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || y !== 0 || 1 / x === 1 / y;\n    } else {\n        return x !== x && y !== y;\n    }\n}\nfunction shallowEqual(objA, objB) {\n    if (is(objA, objB)) return true;\n    if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n        return false;\n    }\n    const keysA = Object.keys(objA);\n    const keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) return false;\n    for(let i = 0; i < keysA.length; i++){\n        if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n            return false;\n        }\n    }\n    return true;\n}\n// src/utils/hoistStatics.ts\nvar REACT_STATICS = {\n    childContextTypes: true,\n    contextType: true,\n    contextTypes: true,\n    defaultProps: true,\n    displayName: true,\n    getDefaultProps: true,\n    getDerivedStateFromError: true,\n    getDerivedStateFromProps: true,\n    mixins: true,\n    propTypes: true,\n    type: true\n};\nvar KNOWN_STATICS = {\n    name: true,\n    length: true,\n    prototype: true,\n    caller: true,\n    callee: true,\n    arguments: true,\n    arity: true\n};\nvar FORWARD_REF_STATICS = {\n    $$typeof: true,\n    render: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true\n};\nvar MEMO_STATICS = {\n    $$typeof: true,\n    compare: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true,\n    type: true\n};\nvar TYPE_STATICS = {\n    [ForwardRef]: FORWARD_REF_STATICS,\n    [Memo]: MEMO_STATICS\n};\nfunction getStatics(component) {\n    if (isMemo(component)) {\n        return MEMO_STATICS;\n    }\n    return TYPE_STATICS[component[\"$$typeof\"]] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent) {\n    if (typeof sourceComponent !== \"string\") {\n        if (objectPrototype) {\n            const inheritedComponent = getPrototypeOf(sourceComponent);\n            if (inheritedComponent && inheritedComponent !== objectPrototype) {\n                hoistNonReactStatics(targetComponent, inheritedComponent);\n            }\n        }\n        let keys = getOwnPropertyNames(sourceComponent);\n        if (getOwnPropertySymbols) {\n            keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n        }\n        const targetStatics = getStatics(targetComponent);\n        const sourceStatics = getStatics(sourceComponent);\n        for(let i = 0; i < keys.length; ++i){\n            const key = keys[i];\n            if (!KNOWN_STATICS[key] && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n                const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n                try {\n                    defineProperty(targetComponent, key, descriptor);\n                } catch (e) {}\n            }\n        }\n    }\n    return targetComponent;\n}\n// src/components/connect.tsx\nvar useSyncExternalStore = notInitialized;\nvar initializeConnect = (fn)=>{\n    useSyncExternalStore = fn;\n};\nvar NO_SUBSCRIPTION_ARRAY = [\n    null,\n    null\n];\nvar stringifyComponent = (Comp)=>{\n    try {\n        return JSON.stringify(Comp);\n    } catch (err) {\n        return String(Comp);\n    }\n};\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n    useIsomorphicLayoutEffect(()=>effectFunc(...effectArgs), dependencies);\n}\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n    lastWrapperProps.current = wrapperProps;\n    renderIsScheduled.current = false;\n    if (childPropsFromStoreUpdate.current) {\n        childPropsFromStoreUpdate.current = null;\n        notifyNestedSubs();\n    }\n}\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, additionalSubscribeListener) {\n    if (!shouldHandleStateChanges) return ()=>{};\n    let didUnsubscribe = false;\n    let lastThrownError = null;\n    const checkForUpdates = ()=>{\n        if (didUnsubscribe || !isMounted.current) {\n            return;\n        }\n        const latestStoreState = store.getState();\n        let newChildProps, error;\n        try {\n            newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n        } catch (e) {\n            error = e;\n            lastThrownError = e;\n        }\n        if (!error) {\n            lastThrownError = null;\n        }\n        if (newChildProps === lastChildProps.current) {\n            if (!renderIsScheduled.current) {\n                notifyNestedSubs();\n            }\n        } else {\n            lastChildProps.current = newChildProps;\n            childPropsFromStoreUpdate.current = newChildProps;\n            renderIsScheduled.current = true;\n            additionalSubscribeListener();\n        }\n    };\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    const unsubscribeWrapper = ()=>{\n        didUnsubscribe = true;\n        subscription.tryUnsubscribe();\n        subscription.onStateChange = null;\n        if (lastThrownError) {\n            throw lastThrownError;\n        }\n    };\n    return unsubscribeWrapper;\n}\nfunction strictEqual(a, b) {\n    return a === b;\n}\nvar hasWarnedAboutDeprecatedPureOption = false;\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, { // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n// @ts-ignore\npure, areStatesEqual = strictEqual, areOwnPropsEqual = shallowEqual, areStatePropsEqual = shallowEqual, areMergedPropsEqual = shallowEqual, // use React's forwardRef to expose a ref of the wrapped component\nforwardRef = false, // the context consumer to use\ncontext = ReactReduxContext } = {}) {\n    if (true) {\n        if (pure !== void 0 && !hasWarnedAboutDeprecatedPureOption) {\n            hasWarnedAboutDeprecatedPureOption = true;\n            warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n        }\n    }\n    const Context = context;\n    const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n    const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n    const initMergeProps = mergePropsFactory(mergeProps);\n    const shouldHandleStateChanges = Boolean(mapStateToProps);\n    const wrapWithConnect = (WrappedComponent)=>{\n        if (true) {\n            const isValid = /* @__PURE__ */ isValidElementType(WrappedComponent);\n            if (!isValid) throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n        }\n        const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n        const displayName = `Connect(${wrappedComponentName})`;\n        const selectorFactoryOptions = {\n            shouldHandleStateChanges,\n            displayName,\n            wrappedComponentName,\n            WrappedComponent,\n            // @ts-ignore\n            initMapStateToProps,\n            // @ts-ignore\n            initMapDispatchToProps,\n            initMergeProps,\n            areStatesEqual,\n            areStatePropsEqual,\n            areOwnPropsEqual,\n            areMergedPropsEqual\n        };\n        function ConnectFunction(props) {\n            const [propsContext, reactReduxForwardedRef, wrapperProps] = React.useMemo(()=>{\n                const { reactReduxForwardedRef: reactReduxForwardedRef2, ...wrapperProps2 } = props;\n                return [\n                    props.context,\n                    reactReduxForwardedRef2,\n                    wrapperProps2\n                ];\n            }, [\n                props\n            ]);\n            const ContextToUse = React.useMemo(()=>{\n                let ResultContext = Context;\n                if (propsContext?.Consumer) {\n                    if (true) {\n                        const isValid = /* @__PURE__ */ isContextConsumer(// @ts-ignore\n                        /* @__PURE__ */ React.createElement(propsContext.Consumer, null));\n                        if (!isValid) {\n                            throw new Error(\"You must pass a valid React context consumer as `props.context`\");\n                        }\n                        ResultContext = propsContext;\n                    }\n                }\n                return ResultContext;\n            }, [\n                propsContext,\n                Context\n            ]);\n            const contextValue = React.useContext(ContextToUse);\n            const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n            const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n            if ( true && !didStoreComeFromProps && !didStoreComeFromContext) {\n                throw new Error(`Could not find \"store\" in the context of \"${displayName}\". Either wrap the root component in a <Provider>, or pass a custom React context provider to <Provider> and the corresponding React context consumer to ${displayName} in connect options.`);\n            }\n            const store = didStoreComeFromProps ? props.store : contextValue.store;\n            const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n            const childPropsSelector = React.useMemo(()=>{\n                return finalPropsSelectorFactory(store.dispatch, selectorFactoryOptions);\n            }, [\n                store\n            ]);\n            const [subscription, notifyNestedSubs] = React.useMemo(()=>{\n                if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY;\n                const subscription2 = createSubscription(store, didStoreComeFromProps ? void 0 : contextValue.subscription);\n                const notifyNestedSubs2 = subscription2.notifyNestedSubs.bind(subscription2);\n                return [\n                    subscription2,\n                    notifyNestedSubs2\n                ];\n            }, [\n                store,\n                didStoreComeFromProps,\n                contextValue\n            ]);\n            const overriddenContextValue = React.useMemo(()=>{\n                if (didStoreComeFromProps) {\n                    return contextValue;\n                }\n                return {\n                    ...contextValue,\n                    subscription\n                };\n            }, [\n                didStoreComeFromProps,\n                contextValue,\n                subscription\n            ]);\n            const lastChildProps = React.useRef();\n            const lastWrapperProps = React.useRef(wrapperProps);\n            const childPropsFromStoreUpdate = React.useRef();\n            const renderIsScheduled = React.useRef(false);\n            const isProcessingDispatch = React.useRef(false);\n            const isMounted = React.useRef(false);\n            const latestSubscriptionCallbackError = React.useRef();\n            useIsomorphicLayoutEffect(()=>{\n                isMounted.current = true;\n                return ()=>{\n                    isMounted.current = false;\n                };\n            }, []);\n            const actualChildPropsSelector = React.useMemo(()=>{\n                const selector = ()=>{\n                    if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n                        return childPropsFromStoreUpdate.current;\n                    }\n                    return childPropsSelector(store.getState(), wrapperProps);\n                };\n                return selector;\n            }, [\n                store,\n                wrapperProps\n            ]);\n            const subscribeForReact = React.useMemo(()=>{\n                const subscribe = (reactListener)=>{\n                    if (!subscription) {\n                        return ()=>{};\n                    }\n                    return subscribeUpdates(shouldHandleStateChanges, store, subscription, // @ts-ignore\n                    childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n                };\n                return subscribe;\n            }, [\n                subscription\n            ]);\n            useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\n                lastWrapperProps,\n                lastChildProps,\n                renderIsScheduled,\n                wrapperProps,\n                childPropsFromStoreUpdate,\n                notifyNestedSubs\n            ]);\n            let actualChildProps;\n            try {\n                actualChildProps = useSyncExternalStore(// TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n                subscribeForReact, // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n                // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n                actualChildPropsSelector, getServerState ? ()=>childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n            } catch (err) {\n                if (latestSubscriptionCallbackError.current) {\n                    ;\n                    err.message += `\nThe error may be correlated with this previous error:\n${latestSubscriptionCallbackError.current.stack}\n\n`;\n                }\n                throw err;\n            }\n            useIsomorphicLayoutEffect(()=>{\n                latestSubscriptionCallbackError.current = void 0;\n                childPropsFromStoreUpdate.current = void 0;\n                lastChildProps.current = actualChildProps;\n            });\n            const renderedWrappedComponent = React.useMemo(()=>{\n                return(// @ts-ignore\n                /* @__PURE__ */ React.createElement(WrappedComponent, {\n                    ...actualChildProps,\n                    ref: reactReduxForwardedRef\n                }));\n            }, [\n                reactReduxForwardedRef,\n                WrappedComponent,\n                actualChildProps\n            ]);\n            const renderedChild = React.useMemo(()=>{\n                if (shouldHandleStateChanges) {\n                    return /* @__PURE__ */ React.createElement(ContextToUse.Provider, {\n                        value: overriddenContextValue\n                    }, renderedWrappedComponent);\n                }\n                return renderedWrappedComponent;\n            }, [\n                ContextToUse,\n                renderedWrappedComponent,\n                overriddenContextValue\n            ]);\n            return renderedChild;\n        }\n        const _Connect = React.memo(ConnectFunction);\n        const Connect = _Connect;\n        Connect.WrappedComponent = WrappedComponent;\n        Connect.displayName = ConnectFunction.displayName = displayName;\n        if (forwardRef) {\n            const _forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n                return /* @__PURE__ */ React.createElement(Connect, {\n                    ...props,\n                    reactReduxForwardedRef: ref\n                });\n            });\n            const forwarded = _forwarded;\n            forwarded.displayName = displayName;\n            forwarded.WrappedComponent = WrappedComponent;\n            return /* @__PURE__ */ hoistNonReactStatics(forwarded, WrappedComponent);\n        }\n        return /* @__PURE__ */ hoistNonReactStatics(Connect, WrappedComponent);\n    };\n    return wrapWithConnect;\n}\nvar connect_default = connect;\n// src/components/Provider.tsx\nfunction Provider({ store, context, children, serverState, stabilityCheck = \"once\", identityFunctionCheck = \"once\" }) {\n    const contextValue = React.useMemo(()=>{\n        const subscription = createSubscription(store);\n        return {\n            store,\n            subscription,\n            getServerState: serverState ? ()=>serverState : void 0,\n            stabilityCheck,\n            identityFunctionCheck\n        };\n    }, [\n        store,\n        serverState,\n        stabilityCheck,\n        identityFunctionCheck\n    ]);\n    const previousState = React.useMemo(()=>store.getState(), [\n        store\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        const { subscription } = contextValue;\n        subscription.onStateChange = subscription.notifyNestedSubs;\n        subscription.trySubscribe();\n        if (previousState !== store.getState()) {\n            subscription.notifyNestedSubs();\n        }\n        return ()=>{\n            subscription.tryUnsubscribe();\n            subscription.onStateChange = void 0;\n        };\n    }, [\n        contextValue,\n        previousState\n    ]);\n    const Context = context || ReactReduxContext;\n    return /* @__PURE__ */ React.createElement(Context.Provider, {\n        value: contextValue\n    }, children);\n}\nvar Provider_default = Provider;\n// src/hooks/useStore.ts\nfunction createStoreHook(context = ReactReduxContext) {\n    const useReduxContext2 = // @ts-ignore\n    context === ReactReduxContext ? useReduxContext : // @ts-ignore\n    createReduxContextHook(context);\n    return function useStore2() {\n        const { store } = useReduxContext2();\n        return store;\n    };\n}\nvar useStore = /* @__PURE__ */ createStoreHook();\n// src/hooks/useDispatch.ts\nfunction createDispatchHook(context = ReactReduxContext) {\n    const useStore2 = // @ts-ignore\n    context === ReactReduxContext ? useStore : createStoreHook(context);\n    return function useDispatch2() {\n        const store = useStore2();\n        return store.dispatch;\n    };\n}\nvar useDispatch = /* @__PURE__ */ createDispatchHook();\n// src/index.ts\ninitializeUseSelector(use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithSelector);\ninitializeConnect(react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore);\nsetBatch(react_dom__WEBPACK_IMPORTED_MODULE_2__.unstable_batchedUpdates);\nvar batch2 = react_dom__WEBPACK_IMPORTED_MODULE_2__.unstable_batchedUpdates;\n //# sourceMappingURL=react-redux.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-redux/dist/react-redux.mjs\n");

/***/ })

};
;