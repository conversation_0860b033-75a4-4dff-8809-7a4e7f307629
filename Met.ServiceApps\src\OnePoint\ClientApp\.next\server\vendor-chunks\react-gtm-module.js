"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-gtm-module";
exports.ids = ["vendor-chunks/react-gtm-module"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-gtm-module/dist/Snippets.js":
/*!********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/Snippets.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar _warn = __webpack_require__(/*! ./utils/warn */ \"(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js\");\nvar _warn2 = _interopRequireDefault(_warn);\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n// https://developers.google.com/tag-manager/quickstart\nvar Snippets = {\n    tags: function tags(_ref) {\n        var id = _ref.id, events = _ref.events, dataLayer = _ref.dataLayer, dataLayerName = _ref.dataLayerName, preview = _ref.preview, auth = _ref.auth;\n        var gtm_auth = \"&gtm_auth=\" + auth;\n        var gtm_preview = \"&gtm_preview=\" + preview;\n        if (!id) (0, _warn2.default)(\"GTM Id is required\");\n        var iframe = '\\n      <iframe src=\"https://www.googletagmanager.com/ns.html?id=' + id + gtm_auth + gtm_preview + '&gtm_cookies_win=x\"\\n        height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\" id=\"tag-manager\"></iframe>';\n        var script = \"\\n      (function(w,d,s,l,i){w[l]=w[l]||[];\\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js', \" + JSON.stringify(events).slice(1, -1) + \"});\\n        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';\\n        j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'\" + gtm_auth + gtm_preview + \"&gtm_cookies_win=x';\\n        f.parentNode.insertBefore(j,f);\\n      })(window,document,'script','\" + dataLayerName + \"','\" + id + \"');\";\n        var dataLayerVar = this.dataLayer(dataLayer, dataLayerName);\n        return {\n            iframe: iframe,\n            script: script,\n            dataLayerVar: dataLayerVar\n        };\n    },\n    dataLayer: function dataLayer(_dataLayer, dataLayerName) {\n        return \"\\n      window.\" + dataLayerName + \" = window.\" + dataLayerName + \" || [];\\n      window.\" + dataLayerName + \".push(\" + JSON.stringify(_dataLayer) + \")\";\n    }\n};\nmodule.exports = Snippets;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/Snippets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/TagManager.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/TagManager.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar _Snippets = __webpack_require__(/*! ./Snippets */ \"(ssr)/./node_modules/react-gtm-module/dist/Snippets.js\");\nvar _Snippets2 = _interopRequireDefault(_Snippets);\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nvar TagManager = {\n    dataScript: function dataScript(dataLayer) {\n        var script = document.createElement(\"script\");\n        script.innerHTML = dataLayer;\n        return script;\n    },\n    gtm: function gtm(args) {\n        var snippets = _Snippets2.default.tags(args);\n        var noScript = function noScript() {\n            var noscript = document.createElement(\"noscript\");\n            noscript.innerHTML = snippets.iframe;\n            return noscript;\n        };\n        var script = function script() {\n            var script = document.createElement(\"script\");\n            script.innerHTML = snippets.script;\n            return script;\n        };\n        var dataScript = this.dataScript(snippets.dataLayerVar);\n        return {\n            noScript: noScript,\n            script: script,\n            dataScript: dataScript\n        };\n    },\n    initialize: function initialize(_ref) {\n        var gtmId = _ref.gtmId, _ref$events = _ref.events, events = _ref$events === undefined ? {} : _ref$events, dataLayer = _ref.dataLayer, _ref$dataLayerName = _ref.dataLayerName, dataLayerName = _ref$dataLayerName === undefined ? \"dataLayer\" : _ref$dataLayerName, _ref$auth = _ref.auth, auth = _ref$auth === undefined ? \"\" : _ref$auth, _ref$preview = _ref.preview, preview = _ref$preview === undefined ? \"\" : _ref$preview;\n        var gtm = this.gtm({\n            id: gtmId,\n            events: events,\n            dataLayer: dataLayer || undefined,\n            dataLayerName: dataLayerName,\n            auth: auth,\n            preview: preview\n        });\n        if (dataLayer) document.head.appendChild(gtm.dataScript);\n        document.head.insertBefore(gtm.script(), document.head.childNodes[0]);\n        document.body.insertBefore(gtm.noScript(), document.body.childNodes[0]);\n    },\n    dataLayer: function dataLayer(_ref2) {\n        var _dataLayer = _ref2.dataLayer, _ref2$dataLayerName = _ref2.dataLayerName, dataLayerName = _ref2$dataLayerName === undefined ? \"dataLayer\" : _ref2$dataLayerName;\n        if (window[dataLayerName]) return window[dataLayerName].push(_dataLayer);\n        var snippets = _Snippets2.default.dataLayer(_dataLayer, dataLayerName);\n        var dataScript = this.dataScript(snippets);\n        document.head.insertBefore(dataScript, document.head.childNodes[0]);\n    }\n};\nmodule.exports = TagManager;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/TagManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar _TagManager = __webpack_require__(/*! ./TagManager */ \"(ssr)/./node_modules/react-gtm-module/dist/TagManager.js\");\nvar _TagManager2 = _interopRequireDefault(_TagManager);\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nmodule.exports = _TagManager2.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsY0FBY0MsbUJBQU9BLENBQUM7QUFFMUIsSUFBSUMsZUFBZUMsdUJBQXVCSDtBQUUxQyxTQUFTRyx1QkFBdUJDLEdBQUc7SUFBSSxPQUFPQSxPQUFPQSxJQUFJQyxVQUFVLEdBQUdELE1BQU07UUFBRUUsU0FBU0Y7SUFBSTtBQUFHO0FBRTlGRyxPQUFPQyxPQUFPLEdBQUdOLGFBQWFJLE9BQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbmVwb2ludC13ZWIvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L2luZGV4LmpzP2E4YzQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgX1RhZ01hbmFnZXIgPSByZXF1aXJlKCcuL1RhZ01hbmFnZXInKTtcblxudmFyIF9UYWdNYW5hZ2VyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQoX1RhZ01hbmFnZXIpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikgeyByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTsgfVxuXG5tb2R1bGUuZXhwb3J0cyA9IF9UYWdNYW5hZ2VyMi5kZWZhdWx0OyJdLCJuYW1lcyI6WyJfVGFnTWFuYWdlciIsInJlcXVpcmUiLCJfVGFnTWFuYWdlcjIiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iiwib2JqIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-gtm-module/dist/utils/warn.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar warn = function warn(s) {\n    console.warn(\"[react-gtm]\", s);\n};\nexports[\"default\"] = warn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L3V0aWxzL3dhcm4uanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQUEsOENBQTZDO0lBQzNDRyxPQUFPO0FBQ1QsQ0FBQyxFQUFDO0FBQ0YsSUFBSUMsT0FBTyxTQUFTQSxLQUFLQyxDQUFDO0lBQ3hCQyxRQUFRRixJQUFJLENBQUMsZUFBZUM7QUFDOUI7QUFFQUgsa0JBQWUsR0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbmVwb2ludC13ZWIvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ3RtLW1vZHVsZS9kaXN0L3V0aWxzL3dhcm4uanM/MWQzMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG52YXIgd2FybiA9IGZ1bmN0aW9uIHdhcm4ocykge1xuICBjb25zb2xlLndhcm4oJ1tyZWFjdC1ndG1dJywgcyk7XG59O1xuXG5leHBvcnRzLmRlZmF1bHQgPSB3YXJuOyJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIndhcm4iLCJzIiwiY29uc29sZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gtm-module/dist/utils/warn.js\n");

/***/ })

};
;