import { useRoleActions } from '@/auth';
import { BarcodeIcon, EyeIcon, PrintIcon } from '@/design/atoms';
import { Button } from '@/design/molecules';
import { Account } from '@/features/account';
import { Location } from '@/features/branch';
import { Customer, CustomerCard } from '@/features/customer';
import { CurrencyType, PricingItem } from '@/features/pricing';
import {
  ProductDescription,
  RealTimeItemsDetails,
  ReplacementItem,
  SelectedProduct
} from '@/features/product';
import { PrintSVButtonAndModal } from '@/features/repair';
import {
  DecisionDetails,
  DeliveryType,
  EmployeeResponse,
  GroupResponse,
  OrderCreditButton,
  OrderCreditDetails,
  OrderType,
  PaymentConfirmation,
  PaymentDue,
  selectedServiceOrderState,
  ServiceOrder,
  ServiceOrderFooter,
  ServiceOrderHeader,
  ServiceOrderOracleStatus,
  ServiceOrderPickup,
  ServiceOrderProduct,
  ServiceOrderStatusCode,
  StatusCard,
  TransitDetails,
  useCreateOrderReceiptMutation,
  WarrantyActionCode,
  WarrantyActionMetadata,
  WarrantyStatusFromRepairLines,
  WarrantySummary
} from '@/features/serviceOrder';
import { useDownloadAgreementMutation } from '@/features/serviceRequest';
import { showSnackbar } from '@/features/snackbar';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { getCurrencyType, getIsStatusReadonly, getPriceAsString } from '@/util';
import { Box, Card, CardContent, Link } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import React, { useCallback, useMemo } from 'react';
import { OrderFulfillmentDetails } from './OrderFulfillmentDetails';

interface Props {
  product: SelectedProduct;
  customer: Customer;
  customerAccount: Account | undefined;
  orderId: string;
  orderDateSubmitted?: string;
  serviceOrderNumber: string;
  branch: Location | undefined;
  isRepairByReplacement: boolean;
  pricingStrategy?: string | null;
  isOperationalReplacement: boolean;
  hasDeniedWarranty?: boolean;
  isOutOfWarranty: boolean;
  statusActiveId: number;
  group?: GroupResponse;
  showDecisionNotification?: () => void;
  onConfirmPickupClick: () => void;
  onCreateEstimateClick: () => void;
  employee?: EmployeeResponse;
  onProductChange: (product: SelectedProduct, orderId: string) => void;
  isLmrPricingFetching?: boolean;
  lmrPricing?: PricingItem;
  customerReferenceId?: string;
  deliveryType?: DeliveryType;
  masterTrackingId?: string;
  warrantyStatusFromRepairLines?: WarrantyStatusFromRepairLines;
  estimateButtonText?: string;
  order?: ServiceOrder;
  serviceRequestPickup?: ServiceOrderPickup;
  currencyType: CurrencyType;
  onPoNumberSave: (poNumber: string, orderId?: string) => void;
  purchaseNumber?: string;
  taxExemptCertLink?: string;
  chargeProductDescriptions?: ProductDescription[];
  showCreditCardAuthorized: boolean;
  showCreditCardAuthorizationPending: boolean;
  creditCardAuthorizedDate?: string;
  inviteToPayProcessed?: boolean;
  authorizedCreditCardLast4?: string;
  authorizedCreditCardBrand?: string;
  showBreakdownPayment?: boolean;
  isInviteToPayWithoutPickup?: boolean;
  inviteToPayWithoutPickupDateRequested?: string;
}

export const ServiceOrderDetails = ({
  product,
  customer,
  customerAccount,
  orderId,
  orderDateSubmitted,
  serviceOrderNumber,
  branch,
  isRepairByReplacement,
  pricingStrategy,
  isOperationalReplacement,
  hasDeniedWarranty,
  isOutOfWarranty,
  onConfirmPickupClick,
  onCreateEstimateClick,
  statusActiveId,
  group,
  employee,
  onProductChange,
  isLmrPricingFetching,
  lmrPricing,
  customerReferenceId,
  deliveryType,
  masterTrackingId,
  warrantyStatusFromRepairLines,
  estimateButtonText,
  order,
  onPoNumberSave,
  purchaseNumber,
  taxExemptCertLink,
  chargeProductDescriptions,
  currencyType,
  showCreditCardAuthorized,
  creditCardAuthorizedDate,
  showCreditCardAuthorizationPending,
  authorizedCreditCardLast4,
  authorizedCreditCardBrand,
  showBreakdownPayment = true,
  isInviteToPayWithoutPickup,
  inviteToPayWithoutPickupDateRequested
}: Props) => {
  const { hasBranchSelection } = useRoleActions();
  const { t } = useTranslation();

  const pricingTax = useAppSelector(selectedServiceOrderState)?.pricingTax;

  const [
    createOrderReceiptMutation,
    { isLoading: isLoadingDownloadServiceReceipt }
  ] = useCreateOrderReceiptMutation();

  const orderComplete =
    statusActiveId === ServiceOrderStatusCode.OrderComplete ||
    order?.serviceOrderPickup != undefined;

  const isStatusReadonly = getIsStatusReadonly(statusActiveId);

  const employeeNameOrDecisionNotesHasValue =
    employee?.name || product?.deniedWarrantyDecision?.notes;

  const showFields = employeeNameOrDecisionNotesHasValue
    ? employeeNameOrDecisionNotesHasValue.length > 0
    : false;

  const handleProductChange = (product: SelectedProduct) => {
    onProductChange(product, orderId);
  };

  const getLmrEstimateText = useCallback(() => {
    return lmrPricing?.svcNetAmount
      ? getPriceAsString(
          lmrPricing.svcNetAmount,
          getCurrencyType(lmrPricing?.currency)
        )
      : 'Not available';
  }, [lmrPricing?.currency, lmrPricing?.svcNetAmount]);

  const isOrderScrappedFromOracle = useMemo(() => {
    return (
      !order?.repairDecision &&
      (order?.status === ServiceOrderOracleStatus.AuthorizeForScrap ||
        order?.status === ServiceOrderOracleStatus.Scrap)
    );
  }, [order]);

  const ServiceRequestEstimateActions = (): React.ReactElement => {
    return (
      <ServiceOrderFooter
        confirmButtonText={t('features.order.confirmPickup')}
        createEstimateButtonText={estimateButtonText}
        onConfirmButtonClick={onConfirmPickupClick}
        onCreateEstimateClick={onCreateEstimateClick}
        data-testid="serviceOrderFooter-testId"
        isConfirmButtonDisabled={true}
        showOrderCompleteBtn={false}
        pricingStrategy={pricingStrategy}
        sx={{ pt: 2.5 }}
      />
    );
  };

  const [downloadAgreement, { isLoading: isLoadingDownloadAgreement }] =
    useDownloadAgreementMutation();

  const dispatch = useAppDispatch();

  const handleDownloadAgreement = async () => {
    try {
      await downloadAgreement(orderId);
      dispatch(
        showSnackbar({
          messageKey:
            'features.serviceRequestEstimate.downloadAgreementSuccess',
          severity: 'success'
        })
      );
    } catch (err) {
      console.error('Error downloading agreement:', err);
      dispatch(
        showSnackbar({
          messageKey: 'features.serviceRequestEstimate.downloadAgreementError',
          severity: 'error'
        })
      );
    }
  };

  const downloadServiceReceipt = async () => {
    if (orderId) {
      await createOrderReceiptMutation(orderId);
    }
  };

  const getMostRecentInboundTrackingNumber = (): string | null => {
    const lastItem = group?.inboundShipments?.at(-1);
    return lastItem ?? null;
  };

  const PdfSectionButtons = (): React.ReactElement => {
    return (
      <Box
        sx={{
          borderTop: '1px solid',
          borderBottom: '1px solid',
          borderBottomColor: 'neutral.200',
          borderTopColor: 'neutral.200'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            p: 2,
            pt: 2
          }}
        >
          <Box sx={{ display: 'flex', gap: 2 }}>
            <PrintSVButtonAndModal
              svNumber={serviceOrderNumber}
              variant="secondary"
              sx={{ flexGrow: 1 }}
              startIcon={<BarcodeIcon viewBox="0 0 24 20" />}
              data-testid="printZebraTags-testid"
            >
              {t('features.repair.print.zebraTags')}
            </PrintSVButtonAndModal>

            <Button
              variant="secondary"
              sx={{ flexGrow: 1 }}
              startIcon={
                <PrintIcon width="25" height="25" viewBox="0 0 25 25" />
              }
              data-testid="viewCustomerServiceHistory-testId"
              onClick={handleDownloadAgreement}
              isLoading={isLoadingDownloadAgreement}
            >
              {t('features.repair.printServiceAgreement')}
            </Button>
          </Box>
        </Box>
      </Box>
    );
  };

  const Summary = (systemOrigin: number): React.ReactElement => {
    const deliveryTypeId =
      deliveryType === DeliveryType.DropShip && !order?.dropShipAddressId
        ? DeliveryType.ShipTo
        : deliveryType;

    const shippingAddress =
      deliveryTypeId !== DeliveryType.ShipTo
        ? order?.dropShipAddress ?? customer?.address
        : undefined;

    if (orderComplete) {
      return (
        <>
          {product.realTimeReplacementProduct && (
            <Box
              sx={{
                borderBottom: '1px solid',
                borderBottomColor: 'neutral.200',
                backgroundColor: 'white',
                p: '16px 0px 24px 24px'
              }}
            >
              <RealTimeItemsDetails selectedProduct={product} />
            </Box>
          )}
          <TransitDetails
            branch={branch}
            systemOrigin={systemOrigin}
            shelfLocation={product.shelfLocation}
            deliveryType={deliveryTypeId}
            customerReferenceId={customerReferenceId}
            masterTrackingId={masterTrackingId}
            repairLocationName={product.repairLocation}
            data-testid="TransitDetails-testid"
            shippingAddress={shippingAddress}
            poNumber={purchaseNumber}
            paymentTerms={order?.shipToMetadata?.paymentTerms}
            onPoNumberEnterPressed={(poNumber: string) =>
              onPoNumberSave(poNumber, order?.id)
            }
            inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
            countryCode={order?.dropShipAddress?.countryCode}
          />
          {serviceOrderNumber && <PdfSectionButtons />}
          {hasDeniedWarranty && (
            <>
              <DecisionDetails
                employee={employee}
                repairDecision={product.deniedWarrantyDecision}
                showFields={showFields}
                data-testid="decisionDetails-testid"
                oracleStatusId={
                  order?.status ?? ServiceOrderOracleStatus.Unknown
                }
                dateRecycled={order?.lastStatusUpdateDate}
              />
            </>
          )}

          {order?.serviceOrderPickup && (
            <>
              <Box
                sx={{
                  borderBottom: '1px solid',
                  borderBottomColor: 'neutral.200'
                }}
              >
                <OrderFulfillmentDetails
                  serviceRequestPickup={order?.serviceOrderPickup}
                  serviceRequestCharges={order?.serviceRequestCharges}
                  currencyType={currencyType}
                  chargeProductDescriptions={chargeProductDescriptions}
                  outboundTrackingNumber={order?.outboundTrackingNumber}
                  shippingAddress={order?.dropShipAddress}
                />
              </Box>
              <Box
                sx={{
                  borderBottom: '1px solid',
                  borderBottomColor: 'neutral.200'
                }}
              >
                <PaymentConfirmation
                  serviceRequestPickup={order?.serviceOrderPickup}
                  purchaseOrder={order?.purchaseOrder}
                  data-testid="PaymentConfirmation-testid"
                  currencyType={currencyType}
                  accountAuthorizationNumber={order?.accountAuthorizationNumber}
                  showCreditCardAuthorized={showCreditCardAuthorized}
                  creditCardAuthorizedDate={creditCardAuthorizedDate}
                  estimateTax={pricingTax?.totalTax}
                  showCreditCardAuthorizationPending={
                    showCreditCardAuthorizationPending
                  }
                  creditCardBrand={authorizedCreditCardBrand}
                  creditCardLast4={authorizedCreditCardLast4}
                  isRealTime={order?.isRealTime}
                  isInviteToPayWithoutPickup={isInviteToPayWithoutPickup}
                  inviteToPayWithoutPickupDateRequested={
                    inviteToPayWithoutPickupDateRequested
                  }
                />
              </Box>
            </>
          )}

          <ServiceRequestEstimateActions />
          <Box
            sx={{
              borderTop: '1px solid',
              borderTopColor: 'neutral.200'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                p: 3,
                pt: 2
              }}
            >
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="secondary"
                  sx={{ flexGrow: 1 }}
                  startIcon={
                    <PrintIcon width="25" height="25" viewBox="0 0 25 25" />
                  }
                  data-testid="downloadServiceReceipt-testId"
                  onClick={() => {
                    downloadServiceReceipt();
                  }}
                  isLoading={isLoadingDownloadServiceReceipt}
                >
                  {t('features.repair.printServiceReceipt')}
                </Button>
                <OrderCreditButton
                  groupId={group?.id ?? ''}
                  orderId={orderId}
                  disabled={
                    !!order?.serviceOrderCredit ||
                    (!hasDeniedWarranty &&
                      order?.serviceOrderPickup?.paymentMethodId === 0)
                  }
                />
              </Box>
            </Box>
          </Box>
        </>
      );
    }

    if (
      statusActiveId === ServiceOrderStatusCode.Pending ||
      statusActiveId === ServiceOrderStatusCode.Submitted ||
      statusActiveId === ServiceOrderStatusCode.InTransit ||
      statusActiveId === ServiceOrderStatusCode.ReceivedAtHub
    ) {
      return (
        <>
          <TransitDetails
            branch={branch}
            systemOrigin={systemOrigin}
            shelfLocation={product.shelfLocation}
            deliveryType={deliveryTypeId}
            customerReferenceId={customerReferenceId}
            masterTrackingId={masterTrackingId}
            repairLocationName={product.repairLocation}
            data-testid="TransitDetails-testid"
            shippingAddress={shippingAddress}
            poNumber={purchaseNumber}
            paymentTerms={order?.shipToMetadata?.paymentTerms}
            onPoNumberEnterPressed={(poNumber: string) =>
              onPoNumberSave(poNumber, order?.id)
            }
            inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
            countryCode={order?.dropShipAddress?.countryCode}
          />
          {serviceOrderNumber ? <PdfSectionButtons /> : null}
          <ServiceRequestEstimateActions />
        </>
      );
    }

    if (statusActiveId === ServiceOrderStatusCode.Shipped) {
      return (
        <>
          {hasDeniedWarranty ? (
            <>
              <TransitDetails
                branch={branch}
                systemOrigin={systemOrigin}
                shelfLocation={product.shelfLocation}
                deliveryType={deliveryTypeId}
                customerReferenceId={customerReferenceId}
                masterTrackingId={masterTrackingId}
                repairLocationName={product.repairLocation}
                data-testid="transitDetails-testid"
                shippingAddress={shippingAddress}
                poNumber={purchaseNumber}
                paymentTerms={order?.shipToMetadata?.paymentTerms}
                onPoNumberEnterPressed={(poNumber: string) =>
                  onPoNumberSave(poNumber, order?.id)
                }
                inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
                countryCode={order?.dropShipAddress?.countryCode}
              />
              {serviceOrderNumber ? <PdfSectionButtons /> : null}
              <WarrantyActionMetadata
                employee={employee}
                deniedWarrantyDecision={product.deniedWarrantyDecision}
                showFields={showFields}
              />
              <PaymentDue
                data-testid="paymentDue-testid"
                currencyType={getCurrencyType(lmrPricing?.currency)}
              />
            </>
          ) : (
            <>
              <TransitDetails
                branch={branch}
                systemOrigin={systemOrigin}
                shelfLocation={product.shelfLocation}
                deliveryType={deliveryType}
                customerReferenceId={customerReferenceId}
                masterTrackingId={masterTrackingId}
                repairLocationName={product.repairLocation}
                data-testid="transitDetails-testid"
                shippingAddress={order?.dropShipAddress ?? customer?.address}
                poNumber={purchaseNumber}
                paymentTerms={order?.shipToMetadata?.paymentTerms}
                onPoNumberEnterPressed={(poNumber: string) =>
                  onPoNumberSave(poNumber, order?.id)
                }
                inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
                countryCode={order?.dropShipAddress?.countryCode}
              />
              {serviceOrderNumber ? <PdfSectionButtons /> : null}
            </>
          )}
          <ServiceRequestEstimateActions />
        </>
      );
    }

    if (
      hasDeniedWarranty &&
      product.deniedWarrantyDecision &&
      (statusActiveId === ServiceOrderStatusCode.InRepair ||
        statusActiveId === ServiceOrderStatusCode.ActionRequired)
    ) {
      return (
        <>
          <TransitDetails
            branch={branch}
            systemOrigin={systemOrigin}
            shelfLocation={product.shelfLocation}
            deliveryType={deliveryTypeId}
            customerReferenceId={customerReferenceId}
            masterTrackingId={masterTrackingId}
            repairLocationName={product.repairLocation}
            data-testid="transitDetails-testid"
            shippingAddress={shippingAddress}
            poNumber={purchaseNumber}
            paymentTerms={order?.shipToMetadata?.paymentTerms}
            onPoNumberEnterPressed={(poNumber: string) =>
              onPoNumberSave(poNumber, order?.id)
            }
            inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
            countryCode={order?.dropShipAddress?.countryCode}
          />
          {serviceOrderNumber ? <PdfSectionButtons /> : null}
          <WarrantyActionMetadata
            employee={employee}
            deniedWarrantyDecision={product.deniedWarrantyDecision}
            showFields={showFields}
          />
          {(showCreditCardAuthorizationPending || showCreditCardAuthorized) && (
            <>
              <PaymentConfirmation
                serviceRequestPickup={order?.serviceOrderPickup}
                purchaseOrder={order?.purchaseOrder}
                data-testid="PaymentConfirmation-testid"
                currencyType={currencyType}
                showCreditCardAuthorized={showCreditCardAuthorized}
                showCreditCardAuthorizationPending={
                  showCreditCardAuthorizationPending
                }
                creditCardAuthorizedDate={creditCardAuthorizedDate}
                creditCardBrand={authorizedCreditCardBrand}
                creditCardLast4={authorizedCreditCardLast4}
                lmrEstimateText={getLmrEstimateText()}
                estimateTax={pricingTax?.totalTax}
                isInviteToPayWithoutPickup={isInviteToPayWithoutPickup}
                inviteToPayWithoutPickupDateRequested={
                  inviteToPayWithoutPickupDateRequested
                }
              />
              {showCreditCardAuthorized && (
                <ServiceOrderFooter
                  confirmButtonText={t('features.order.confirmPickup')}
                  data-testid="serviceOrderFooter-testId"
                  isConfirmButtonDisabled={!hasBranchSelection}
                  pricingStrategy={pricingStrategy}
                  showOrderCompleteBtn={
                    !isStatusReadonly && !order?.serviceOrderPickup
                  }
                  createEstimateButtonText={estimateButtonText}
                  onConfirmButtonClick={onConfirmPickupClick}
                  onCreateEstimateClick={onCreateEstimateClick}
                  customerAccount={customerAccount}
                  isGenericAccount={order?.isGenericAccount}
                  showPickupButton={
                    order?.onepointStatusId === ServiceOrderStatusCode.Pickup
                  }
                />
              )}
            </>
          )}
        </>
      );
    }

    if (
      hasDeniedWarranty &&
      (systemOrigin === OrderType.BranchOrder ||
        systemOrigin === OrderType.EService ||
        systemOrigin === OrderType.OneKey) &&
      !product.deniedWarrantyDecision &&
      statusActiveId === ServiceOrderStatusCode.ActionRequired
    ) {
      return (
        <>
          <TransitDetails
            branch={branch}
            systemOrigin={systemOrigin}
            shelfLocation={product.shelfLocation}
            deliveryType={deliveryTypeId}
            customerReferenceId={customerReferenceId}
            masterTrackingId={masterTrackingId}
            repairLocationName={product.repairLocation}
            data-testid="transitDetails-testid"
            shippingAddress={shippingAddress}
            poNumber={purchaseNumber}
            paymentTerms={order?.shipToMetadata?.paymentTerms}
            onPoNumberEnterPressed={(poNumber: string) =>
              onPoNumberSave(poNumber, order?.id)
            }
            inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
            countryCode={order?.dropShipAddress?.countryCode}
          />
          {serviceOrderNumber ? <PdfSectionButtons /> : null}
          {deliveryType !== undefined && (
            <WarrantySummary
              customer={customer}
              serviceOrderNumber={serviceOrderNumber}
              disabledAllActionButton={!hasBranchSelection}
              employee={employee}
              product={product}
              onProductChange={handleProductChange}
              deliveryType={deliveryType}
              data-testid="warrantySummary-testid"
              accountAuthorizationNumber={order?.accountAuthorizationNumber}
              orderId={orderId}
              lmrEstimate={getLmrEstimateText()}
              showFields={showFields}
              poNumber={order?.purchaseOrder}
            />
          )}
          <ServiceRequestEstimateActions />
        </>
      );
    }

    if (
      statusActiveId === ServiceOrderStatusCode.InRepair &&
      hasDeniedWarranty
    ) {
      return (
        <>
          <TransitDetails
            branch={branch}
            systemOrigin={systemOrigin}
            shelfLocation={product.shelfLocation}
            deliveryType={deliveryTypeId}
            customerReferenceId={customerReferenceId}
            masterTrackingId={masterTrackingId}
            repairLocationName={product.repairLocation}
            data-testid="transitDetails-testid"
            shippingAddress={shippingAddress}
            poNumber={purchaseNumber}
            paymentTerms={order?.shipToMetadata?.paymentTerms}
            onPoNumberEnterPressed={(poNumber: string) =>
              onPoNumberSave(poNumber, order?.id)
            }
            inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
            countryCode={order?.dropShipAddress?.countryCode}
          />
          {serviceOrderNumber ? <PdfSectionButtons /> : null}
          <DecisionDetails
            employee={employee}
            repairDecision={product.deniedWarrantyDecision}
            data-testid="decisionDetails-testid"
            showFields={showFields}
            dateRecycled={order?.lastStatusUpdateDate}
            oracleStatusId={order?.status}
          />
          <PaymentDue
            data-testid="paymentDue-testid"
            currencyType={currencyType}
          />
          <ServiceRequestEstimateActions />
        </>
      );
    }
    const isInRepair = statusActiveId === ServiceOrderStatusCode.InRepair;
    const isBranchOrder = systemOrigin === OrderType.BranchOrder;
    const hasNonWarrantyRepairLineForPickup =
      statusActiveId === ServiceOrderStatusCode.Pickup &&
      warrantyStatusFromRepairLines !== WarrantyStatusFromRepairLines.Approved;
    const hasCharges = order?.serviceRequestCharges.some(
      (x) => x.serviceActivity === 'Chargeable'
    );
    const shouldShowPayment =
      hasNonWarrantyRepairLineForPickup ||
      statusActiveId === ServiceOrderStatusCode.OrderComplete ||
      hasDeniedWarranty ||
      hasCharges;
    const showPaymentConfirmation =
      deliveryType === DeliveryType.DropShip || order?.serviceOrderPickup;

    const showFooter =
      ((statusActiveId === ServiceOrderStatusCode.Pickup &&
        !order?.serviceOrderPickup) ||
        pricingStrategy === 'Estimate') &&
      showBreakdownPayment;

    return (
      <>
        <TransitDetails
          branch={branch}
          systemOrigin={systemOrigin}
          shelfLocation={product.shelfLocation}
          deliveryType={deliveryTypeId}
          customerReferenceId={customerReferenceId}
          masterTrackingId={masterTrackingId}
          repairLocationName={product.repairLocation}
          data-testid="transitDetails-testid"
          shippingAddress={shippingAddress}
          poNumber={purchaseNumber}
          paymentTerms={order?.shipToMetadata?.paymentTerms}
          onPoNumberEnterPressed={(poNumber: string) =>
            onPoNumberSave(poNumber, order?.id)
          }
          inboundTrackingNumber={getMostRecentInboundTrackingNumber()}
          countryCode={order?.dropShipAddress?.countryCode}
        />
        {serviceOrderNumber ? <PdfSectionButtons /> : null}
        {isInRepair ? (
          <ServiceRequestEstimateActions />
        ) : (
          <>
            {isBranchOrder && (
              <>
                {hasDeniedWarranty && (
                  <>
                    <DecisionDetails
                      employee={employee}
                      repairDecision={product.deniedWarrantyDecision}
                      data-testid="decisionDetails-testid"
                      showFields={false}
                    />
                  </>
                )}
                {hasCharges && (
                  <Box
                    sx={{
                      borderBottom: '1px solid',
                      borderBottomColor: 'neutral.200'
                    }}
                  >
                    <OrderFulfillmentDetails
                      serviceRequestPickup={order?.serviceOrderPickup}
                      serviceRequestCharges={order?.serviceRequestCharges}
                      currencyType={currencyType}
                      chargeProductDescriptions={chargeProductDescriptions}
                      outboundTrackingNumber={order?.outboundTrackingNumber}
                      shippingAddress={order?.dropShipAddress}
                    />
                  </Box>
                )}
                {showBreakdownPayment &&
                  (shouldShowPayment ||
                    showCreditCardAuthorized ||
                    showCreditCardAuthorizationPending) && (
                    <>
                      {showPaymentConfirmation ||
                      showCreditCardAuthorized ||
                      showCreditCardAuthorizationPending ? (
                        <PaymentConfirmation
                          serviceRequestPickup={order?.serviceOrderPickup}
                          purchaseOrder={order?.purchaseOrder}
                          data-testid="PaymentConfirmation-testid"
                          currencyType={currencyType}
                          showCreditCardAuthorized={showCreditCardAuthorized}
                          showCreditCardAuthorizationPending={
                            showCreditCardAuthorizationPending
                          }
                          creditCardAuthorizedDate={creditCardAuthorizedDate}
                          creditCardBrand={authorizedCreditCardBrand}
                          creditCardLast4={authorizedCreditCardLast4}
                          lmrEstimateText={getLmrEstimateText()}
                          estimateTax={pricingTax?.totalTax}
                          isInviteToPayWithoutPickup={
                            isInviteToPayWithoutPickup
                          }
                          inviteToPayWithoutPickupDateRequested={
                            inviteToPayWithoutPickupDateRequested
                          }
                        />
                      ) : (
                        <PaymentDue
                          data-testid="paymentDue-testid"
                          currencyType={currencyType}
                        />
                      )}
                    </>
                  )}
                {showFooter && (
                  <ServiceOrderFooter
                    confirmButtonText={t('features.order.confirmPickup')}
                    data-testid="serviceOrderFooter-testId"
                    isConfirmButtonDisabled={!hasBranchSelection}
                    pricingStrategy={pricingStrategy}
                    showOrderCompleteBtn={
                      !isStatusReadonly && !order?.serviceOrderPickup
                    }
                    createEstimateButtonText={estimateButtonText}
                    onConfirmButtonClick={onConfirmPickupClick}
                    onCreateEstimateClick={onCreateEstimateClick}
                    customerAccount={customerAccount}
                    isGenericAccount={order?.isGenericAccount}
                    showPickupButton={
                      order?.onepointStatusId === ServiceOrderStatusCode.Pickup
                    }
                  />
                )}
              </>
            )}
          </>
        )}
      </>
    );
  };

  return (
    <>
      <Box
        data-testid="repairOrderDetail-testId"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          my: 0.5,
          marginX: 'auto',
          justifySelf: 'center',
          justifyContent: 'center',
          maxWidth: {
            xl: '1400px'
          }
        }}
      >
        <Card
          variant="outlined"
          sx={{
            border: '1px solid',
            borderColor: 'neutral.300',
            boxShadow: 1
          }}
        >
          <CardContent
            sx={{
              flexGrow: 1,
              padding: '0 !important',
              flexDirection: 'column',
              backgroundColor: 'common.white'
            }}
          >
            <ServiceOrderHeader
              id={orderId}
              serviceOrderNumber={serviceOrderNumber}
              orderDateSubmitted={orderDateSubmitted}
              systemOrigin={group?.systemOrigin ?? 0}
            />
            {group && order && (
              <>
                <CustomerCard
                  customer={customer}
                  containerSxProps={{
                    borderBottom: '1px solid',
                    borderBottomColor: 'neutral.200',
                    pt: 0
                  }}
                  dropOffParty={group.dropOffParty}
                />
                <StatusCard
                  systemOrigin={group.systemOrigin}
                  orderId={order.id}
                  deliveryType={order.deliveryType}
                  statusId={statusActiveId}
                  oracleStatusId={order.status}
                  dateRecycled={
                    order.repairDecision?.repairDecisionId ===
                    WarrantyActionCode.Recycle
                      ? order.repairDecision?.createdAt
                      : undefined
                  }
                  forceCompleteStatus={isOrderScrappedFromOracle}
                  dateRetrieved={order.serviceOrderPickup?.paymentDateTime}
                  dateUpdated={order.lastStatusUpdateDate}
                />
              </>
            )}
            <ServiceOrderProduct
              product={product}
              orderId={orderId}
              isRepairByReplacement={isRepairByReplacement}
              isOperationalReplacement={isOperationalReplacement}
              isOutOfWarranty={isOutOfWarranty}
              onProductChange={handleProductChange}
              systemOrigin={group?.systemOrigin ?? 0}
              statusActiveId={statusActiveId}
              isLmrPricingFetching={isLmrPricingFetching}
              lmrPricing={lmrPricing}
              warrantyStatusFromRepairLines={warrantyStatusFromRepairLines}
            />
            {(isRepairByReplacement || isOperationalReplacement) && (
              <ReplacementItem product={product} />
            )}
            {taxExemptCertLink && (
              <Box
                sx={{
                  p: '24px',
                  borderBottom: '1px solid',
                  borderBottomColor: 'neutral.200'
                }}
              >
                <Link
                  data-testid="taxExemptCertificateLink-testId"
                  target="_blank"
                  href={taxExemptCertLink}
                  sx={{
                    whiteSpace: 'nowrap',
                    width: '100%',
                    display: 'flex',
                    textDecoration: 'none'
                  }}
                >
                  <Button
                    variant="secondary"
                    size="large"
                    sx={{
                      width: '100%',
                      gap: '8px'
                    }}
                  >
                    <EyeIcon viewBox="0 0 22 17" />
                    {t('features.order.detail.taxExemptCertificateButtonText')}
                  </Button>
                </Link>
              </Box>
            )}
            {Summary(group?.systemOrigin ?? 0)}
          </CardContent>
        </Card>
        <OrderCreditDetails
          serviceCredit={order?.serviceOrderCredit}
          currencyType={currencyType}
        />
      </Box>
    </>
  );
};
