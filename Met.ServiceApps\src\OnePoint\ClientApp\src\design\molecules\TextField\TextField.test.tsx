import {
  CreateProfileSchemaType,
  defaultProfileValuesSchema
} from '@/features/customer';
import '@testing-library/jest-dom';
import { fireEvent, render, renderHook } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { TextField } from './TextField';

import { ChangeEvent, useState } from 'react';

const defaultValues: CreateProfileSchemaType = {
  ...defaultProfileValuesSchema,
  firstName: 'First name',
  postalCode: '$845'
};

const { result } = renderHook(() =>
  useForm<CreateProfileSchemaType>({ defaultValues })
);
const { control } = result.current;

interface TextFieldWrapperProps {
  initialTextValue: string;
  maxCharacters: number;
  maxCharactersHelperText: boolean;
  ariaLabel: string;
}

const TextFieldWrapper: React.FC<TextFieldWrapperProps> = ({
  initialTextValue,
  maxCharacters,
  maxCharactersHelperText,
  ariaLabel
}: TextFieldWrapperProps) => {
  const [textValue, setTextValue] = useState<string>(initialTextValue);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setTextValue(event.target.value);
  };

  return (
    <TextField
      textValue={textValue}
      maxCharacters={maxCharacters}
      maxCharactersHelperText={maxCharactersHelperText}
      ariaLabel={ariaLabel}
      onChange={handleChange}
      data-testid={'textField-input-testId'}
    />
  );
};

describe('TextField Component', () => {
  it('should renders controller textfield value', () => {
    const { getByRole } = render(
      <TextField
        form={{
          name: 'firstName',
          control: control
        }}
      />
    );

    const input = getByRole('textbox', { name: 'firstName' });
    expect(input).toHaveValue('First name');
  });
  it('should renders uncontrolled textfield value', () => {
    const { getByRole } = render(
      <TextField textValue="example" ariaLabel="example" />
    );

    const input = getByRole('textbox', { name: 'example' });
    expect(input).toHaveValue('example');
  });
  it('should renders controlled textfield value with prefix', () => {
    const { getByRole } = render(
      <TextField
        form={{
          name: 'firstName',
          control: control
        }}
        prefix="$"
      />
    );

    const input = getByRole('textbox', { name: 'firstName' });
    expect(input).toHaveValue('$First name');
  });
  it('should renders uncontrolled textfield value with prefix', () => {
    const { getByRole } = render(
      <TextField prefix="$" textValue="example" ariaLabel="example" />
    );

    const input = getByRole('textbox', { name: 'example' });
    expect(input).toHaveValue('$example');
  });
  it('should renders controlled textfield value with prefix and validation of number', () => {
    const { getByRole } = render(
      <TextField
        form={{
          name: 'postalCode',
          control: control
        }}
        prefix="$"
        validateNaN={true}
      />
    );

    const input = getByRole('textbox', { name: 'postalCode' });
    expect(input).toHaveValue('$845');
  });
  it('should renders uncontrolled textfield value with prefix and validation of number', () => {
    const { getByRole } = render(
      <TextField
        prefix="$"
        textValue="example"
        ariaLabel="example"
        validateNaN={true}
      />
    );

    const input = getByRole('textbox', { name: 'example' });
    expect(input).not.toHaveValue('$example');
  });

  it('should display remaining characters when maxCharactersHelperText is enabled', async () => {
    const { getByRole, getByText, findByText } = render(
      <TextFieldWrapper
        initialTextValue=""
        maxCharacters={10}
        maxCharactersHelperText={true}
        ariaLabel="remainingTest"
      />
    );

    const input = getByRole('textbox', { name: 'remainingTest' });
    expect(getByText('10 characters remaining')).toBeInTheDocument();

    fireEvent.change(input, { target: { value: 'test' } });
    expect(await findByText('6 characters remaining')).toBeInTheDocument();
  });

  it('should have size small when size prop is not passed', async () => {
    const { getByRole } = render(
      <TextField
        prefix="$"
        textValue="example"
        ariaLabel="example"
        validateNaN={true}
      />
    );

    const input = getByRole('textbox', { name: 'example' });
    expect(input).toHaveClass('MuiInputBase-inputSizeSmall');
  });

  it('should not have size small when size prop is passed with medium as value', async () => {
    const { getByRole } = render(
      <TextField
        prefix="$"
        textValue="example"
        ariaLabel="example"
        validateNaN={true}
        size="medium"
      />
    );

    const input = getByRole('textbox', { name: 'example' });
    expect(input).not.toHaveClass('MuiInputBase-inputSizeSmall');
  });
});
