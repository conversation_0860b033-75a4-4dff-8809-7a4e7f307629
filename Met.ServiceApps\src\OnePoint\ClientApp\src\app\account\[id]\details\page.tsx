'use client';
import { useRoleActions } from '@/auth';
import { ArrowLeftIcon, CloseIcon, PlusIcon } from '@/design/atoms';
import {
  Button,
  Loading,
  PageHeaderContainer,
  Snackbar
} from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import {
  AccountDetailsContainer,
  CustomerAccount,
  onRedirectToPagePathChange,
  onSelectCustomerHandler,
  onSelectProfileClick,
  onShowContactDetailsModalChange,
  selectedAccountPageState,
  setShowProfileSearch,
  SiteItem,
  useGetAccount,
  useGetCdlSiteBySiteNumberQuery
} from '@/features/account';
import { selectBranch } from '@/features/branch';
import {
  Customer,
  CustomerParams,
  ProfileFormModal,
  setSalesOrderCreationProfileFromAccount,
  useLazyGetCustomersQuery,
  useLazySearchCustomersQuery
} from '@/features/customer';
import { MainButtonContainer } from '@/features/home';
import { selectHub } from '@/features/hub';
import { onCustomerAccountChange } from '@/features/repair';
import { setSalesOrderReturnToPath } from '@/features/salesOrder';
import { ProfileSearchBy, SearchCustomerContainer } from '@/features/search';
import {
  DeliveryType,
  GetGroupsResponse,
  MultiToolOrders,
  MultiToolPickup,
  onPaymentMethodsChange,
  OrderHistory,
  OrderListDisplayOrigin,
  ServiceOrderOracleStatus,
  ServiceOrderPaymentProvider,
  updateReturnTo,
  useCalculatePaymentMethods
} from '@/features/serviceOrder';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { SnackbarState } from '@/util';
import { Box, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
const serviceOrderGroups: GetGroupsResponse = {
  serviceOrderGroups: {
    page: 1,
    totalItems: 10,
    totalPages: 1,
    data: [
      {
        id: '67e467bf-72f0-495a-858d-d973dd3ea93e',
        createdAt: '01/01/2020',
        serviceOrders: [
          {
            id: '530fd634-f5d5-42ed-bf80-1f732da00811',
            serialNumberUnreadable: false,
            sku: '250420',
            status: ServiceOrderOracleStatus.Submitted,
            serialNumber: '11111',
            serviceRequestNumber: '22222',
            toolNickname: '',
            deliveryType: DeliveryType.WillCall,
            isDeniedWarranty: false,
            lastStatusUpdateDate: '',
            shipToSiteNumber: '980726',
            isGenericAccount: true
          }
        ],
        systemOrigin: 1,
        inboundShipments: []
      },
      {
        id: '67e467bf-72f0-495a-858d-d973dd3ea93e',
        createdAt: '01/01/2020',
        serviceOrders: [
          {
            id: '530fd634-f5d5-42ed-bf80-1f732da00811',
            serialNumberUnreadable: false,
            sku: '250420',
            status: ServiceOrderOracleStatus.SrShipWillCall,
            serialNumber: '11111',
            serviceRequestNumber: '22222',
            toolNickname: '',
            deliveryType: DeliveryType.WillCall,
            isDeniedWarranty: false,
            lastStatusUpdateDate: '',
            shipToSiteNumber: '980726',
            isGenericAccount: true
          }
        ],
        systemOrigin: 1,
        inboundShipments: []
      }
    ]
  },
  productDataMap: {
    '250420': {
      image: '/image-sku-250420',
      description: '250420-description',
      skuAlias: '',
      warrantyDescription: '',
      isRbr: false,
      pricingStrategy: '',
      isBranchRepair: false
    },
    '10011': {
      image: null,
      description: '10011-description',
      skuAlias: '',
      warrantyDescription: '',
      isRbr: false,
      pricingStrategy: '',
      isBranchRepair: false
    },
    '250421': {
      image: null,
      description: '250421-description',
      skuAlias: '',
      warrantyDescription: '',
      isRbr: false,
      pricingStrategy: '',
      isBranchRepair: false
    }
  }
};

interface Props {
  params: {
    id: string;
  };
  searchParams?: {
    spc?: string;
  };
}

export default function AccountDetailsPage(props: Props) {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const siteNumber = props.params.id;
  const [siteCityStateProvinceOrPostalCode] = useState(props.searchParams?.spc);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const currentFullPath = `${pathname}?${searchParams}`;
  const searchByAccountParams = useMemo(
    () => ({
      siteNameOrNumber: searchParams.get('st') ?? '',
      stateOrProvince: searchParams.get('sp') ?? 'All',
      parentAccountName: searchParams.get('pa') ?? '',
      city: searchParams.get('c') ?? '',
      postalCode: searchParams.get('pc') ?? ''
    }),
    [searchParams]
  );
  const roleActions = useRoleActions();
  const [isCustomerNotFound, setIsCustomerNotFound] = useState(false);
  const [isAccountNotFound, setIsAccountNotFound] = useState(false);
  const selectedBranch = useAppSelector(selectBranch);
  const selectedHub = useAppSelector(selectHub);
  const [openProfileFormModal, setProfileFormModalOpen] = useState(false);
  const [selectedMultiToolOrders, setSelectedMultiToolOrders] = useState<
    MultiToolOrders[]
  >([]);
  const [showMultiToolScreen, setShowMultiToolScreen] = useState(false);
  const [isSalesOrder, setIsSalesOrder] = useState(false);
  const [serviceContactEmail, setServiceContactEmail] = useState<string>();
  const [serviceContactProfile, setServiceContactProfile] = useState<
    Customer | undefined
  >();
  const handleProfileModalChange = (value: boolean) => {
    setProfileFormModalOpen(value);
  };
  const [profileSnackBarState, setProfileSnackBarState] =
    useState<SnackbarState>({
      open: false,
      message: t('features.account.details.userNotFound'),
      severity: 'error'
    });

  const { showProfileSearch, isLoading: isProfileLoading } = useAppSelector(
    selectedAccountPageState
  );

  const handleCustomerNotFound = (value: boolean) => {
    setIsCustomerNotFound(value);
  };

  const handleCustomeSelectedrNotFound = () => {
    setProfileSnackBarState((prevState) => ({
      ...prevState,
      open: true
    }));
  };

  const handleAccountNotFound = (value: boolean) => {
    setIsAccountNotFound(value);
  };

  const [snackBarState, setSnackBarState] = useState<SnackbarState>({
    open: true,
    message: t('features.account.details.accountNotFound'),
    severity: 'error'
  });
  const [selectedCustomerMultitool, setSelectedCustomerMultitool] = useState<
    Customer | undefined
  >(undefined);
  const [siteDetail, setSiteDetail] = useState<SiteItem | undefined>();
  const { calculatePaymentMethodsFromPaymentTerms } =
    useCalculatePaymentMethods();

  // #region Queries
  const [searchCustomerQuery] = useLazySearchCustomersQuery();

  const {
    getSite,
    isSearchCustomersCdlError,
    isSearchCustomersCdlFetching: isSearchAccountCdlFetching
  } = useGetAccount();

  const {
    data: shipToSiteMetaData,
    isFetching: isGetSiteShipToMetaDataFetching
  } = useGetCdlSiteBySiteNumberQuery(siteNumber);

  const [getCustomerTrigger, { isFetching: isGetCustomerFetching }] =
    useLazyGetCustomersQuery();
  // #endregion Queries

  // #region Effect Hooks
  useEffect(() => {
    const getSiteData = async () => {
      const site = await getSite(
        siteNumber,
        siteCityStateProvinceOrPostalCode!
      );

      if (site) {
        setSiteDetail(site);
      }
    };

    if (
      !isSearchCustomersCdlError &&
      !isSearchAccountCdlFetching &&
      !siteDetail
    ) {
      getSiteData();
    }
  }, [
    isSearchAccountCdlFetching,
    isSearchCustomersCdlError,
    siteNumber,
    getSite,
    siteCityStateProvinceOrPostalCode,
    siteDetail
  ]);

  useEffect(() => {
    if (siteDetail) {
      dispatch(
        onPaymentMethodsChange(
          calculatePaymentMethodsFromPaymentTerms(siteDetail?.paymentTerms)
        )
      );
    }
  }, [dispatch, calculatePaymentMethodsFromPaymentTerms, siteDetail]);

  useEffect(() => {
    return () => {
      dispatch(setShowProfileSearch(false));
    };
  }, [dispatch]);

  useEffect(() => {
    const getServiceContactProfile = async (serviceContactEmail: string) => {
      const params: CustomerParams = {
        pageNumber: 1,
        pageSize: 1,
        email: serviceContactEmail
      };
      const response = await getCustomerTrigger(params);

      if (response && response?.data?.users?.data) {
        const customer = response.data.users.data[0];
        const customerPayload: Customer = {
          auth0Id: '',
          emailAddress: customer.emailAddress,
          userId: customer.userId,
          address: {
            addressLine1: customer.address?.addressLine1 ?? '',
            addressLine2: customer.address?.addressLine2 ?? '',
            city: customer.address?.city ?? '',
            countryCode: customer.address?.countryCode ?? '',
            postalCode: customer.address?.postalCode ?? '',
            state: customer.address?.state ?? ''
          },
          firstName: customer.firstName ?? '',
          lastName: customer.lastName ?? '',
          phoneNumber: customer.phoneNumber
        };

        setServiceContactProfile(customerPayload);
      }
    };
    if (
      shipToSiteMetaData &&
      shipToSiteMetaData?.shipToSite?.serviceContactEmail
    ) {
      getServiceContactProfile(
        shipToSiteMetaData?.shipToSite?.serviceContactEmail
      );
      setServiceContactEmail(
        shipToSiteMetaData?.shipToSite?.serviceContactEmail
      );
    }
  }, [shipToSiteMetaData, getCustomerTrigger, dispatch]);
  // #endregion Effect Hooks

  const handleBackButtonClick = () => {
    const {
      siteNameOrNumber,
      stateOrProvince,
      parentAccountName,
      city,
      postalCode
    } = searchByAccountParams;
    const query = new URLSearchParams();

    if (siteNameOrNumber != undefined) query.set('st', siteNameOrNumber);
    if (stateOrProvince != undefined) query.set('sp', stateOrProvince);
    if (parentAccountName != undefined) query.set('pa', parentAccountName);
    if (city != undefined) query.set('c', city);
    if (postalCode != undefined) query.set('pc', postalCode);

    router.push(`/customer?${query.toString()}`);
  };

  const handleProfileSearchBackButtonClick = () => {
    if (isSalesOrder) {
      dispatch(setShowProfileSearch(false));
    } else {
      dispatch(onSelectProfileClick(false));
    }
    setIsSalesOrder(false);
  };

  const handleNewServiceRequestClick = (redirectTo: string) => {
    setIsSalesOrder(false);
    dispatch(updateReturnTo(currentFullPath));
    if (serviceContactProfile) {
      dispatch(onSelectCustomerHandler(serviceContactProfile));
    }
    dispatch(onCustomerAccountChange());
    dispatch(onShowContactDetailsModalChange(true));
    dispatch(onRedirectToPagePathChange(redirectTo));
  };

  const customerSelectedHandler = (customer: Customer) => {
    if (isSalesOrder) {
      dispatch(
        setSalesOrderCreationProfileFromAccount({
          profile: {
            accountNumber: siteNumber,
            site: siteDetail
          },
          customer
        })
      );
      dispatch(setSalesOrderReturnToPath(currentFullPath));
      router.push('/salesOrder/create');
    } else {
      dispatch(onSelectCustomerHandler(customer));
    }
  };

  const handleMultiToolProcess = async (multiToolOrders: MultiToolOrders[]) => {
    setSelectedMultiToolOrders(multiToolOrders);
    const customerId = multiToolOrders.at(0)?.customerId;
    if (customerId !== undefined) {
      const response = await searchCustomerQuery(
        {
          ids: [customerId]
        },
        true
      );
      if (response.isSuccess) {
        setSelectedCustomerMultitool(response.data.users.at(0));
        if (siteDetail) {
          dispatch(
            onCustomerAccountChange({
              accountNumber: siteDetail?.siteNumber,
              accountDescription: siteDetail.siteName,
              accountAddress: siteDetail
            } as CustomerAccount)
          );
        }
      }
    }
    setShowMultiToolScreen(true);
  };

  const handleRemovePickupOrder = (orderId: string) => {
    setSelectedMultiToolOrders(
      selectedMultiToolOrders.filter((x) => x.orderId !== orderId)
    );
    if (selectedMultiToolOrders.length === 1) {
      setShowMultiToolScreen(false);
      dispatch(onCustomerAccountChange());
    }
  };

  const handleStartSalesOrderClick = () => {
    setIsSalesOrder(true);
    dispatch(setShowProfileSearch(true));
  };

  const handleContinueAsGuest = () => {
    dispatch(
      setSalesOrderCreationProfileFromAccount({
        profile: {
          accountNumber: siteNumber,
          isGuest: true,
          site: siteDetail
        },
        customer: undefined
      })
    );
    dispatch(setSalesOrderReturnToPath(currentFullPath));
    router.push('/salesOrder/create');
  };

  const multiToolBackClick = useCallback(() => {
    setShowMultiToolScreen(false);
    dispatch(onCustomerAccountChange());
  }, [dispatch]);

  if (showMultiToolScreen)
    return (
      <>
        <Loading
          isLoading={selectedCustomerMultitool === undefined}
          fallbackContainerProps={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <ServiceOrderPaymentProvider>
            <MultiToolPickup
              customer={selectedCustomerMultitool!}
              onBackClick={multiToolBackClick}
              multiTools={selectedMultiToolOrders}
              removePickupOrder={handleRemovePickupOrder}
            />
          </ServiceOrderPaymentProvider>
        </Loading>
      </>
    );
  return (
    <>
      {showProfileSearch ? (
        <Box>
          {profileSnackBarState.open && (
            <Snackbar
              open={true}
              message={profileSnackBarState.message}
              handleClose={() =>
                setSnackBarState({ ...profileSnackBarState, open: false })
              }
              severity={profileSnackBarState.severity}
              autoHideDuration={10000}
            />
          )}
          <PageHeaderContainer
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton
                onClick={handleProfileSearchBackButtonClick}
                data-testid="backBtn-testId"
              >
                <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
              </IconButton>
              <Typography variant="h5">
                {t(
                  isSalesOrder
                    ? 'features.account.details.salesOrderProfilePageTitle'
                    : 'features.account.details.profilePageTitle'
                )}
              </Typography>
            </Box>
            <IconButton
              onClick={() => router.push('/')}
              data-testid="closeBtn-testId"
            >
              <CloseIcon />
            </IconButton>
          </PageHeaderContainer>
          <PageAreaBox
            sx={{
              height: '100%',
              bgcolor: 'background.paper',
              maxWidth: '1460px',
              padding: `0px 32px 0px 32px`,
              mt: '97px'
            }}
            pageAreaBoxName="ProfileCustomerLookup"
          >
            <ProfileFormModal
              open={openProfileFormModal}
              onFormChange={handleProfileModalChange}
              disableNonEditableFields={false}
              saveButtonText={t('common.saveAndSelect')}
              onCustomerSave={customerSelectedHandler}
            />
            <SearchCustomerContainer
              isViewMode={false}
              onCustomerNotFound={handleCustomerNotFound}
              isCustomerNotFound={isCustomerNotFound}
              selectedBranch={selectedBranch}
              selectedHub={selectedHub}
              onCustomerSelectedNotFound={handleCustomeSelectedrNotFound}
              isAccountNotFound={isAccountNotFound}
              onAccountNotFound={handleAccountNotFound}
              showCustomerToggle={false}
              profileSearchBy={ProfileSearchBy.CustomerInfo}
              customerSelectedHandler={customerSelectedHandler}
              allowGuest={isSalesOrder}
              onContinueAsGuestClick={handleContinueAsGuest}
            />
            {isCustomerNotFound && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'end',
                  mt: '16px',
                  justifyContent: 'flex-end'
                }}
              >
                <Button
                  data-testid="createProfileButtonRepair-testid"
                  size="medium"
                  variant="secondary"
                  startIcon={<PlusIcon viewBox="0 0 16 16" />}
                  onClick={() => setProfileFormModalOpen(true)}
                >
                  {t('features.home.mainButtons.createProfile')}
                </Button>
              </Box>
            )}
          </PageAreaBox>
        </Box>
      ) : (
        <Box>
          {isSearchCustomersCdlError && snackBarState.open && (
            <Snackbar
              open={true}
              message={snackBarState.message}
              handleClose={() =>
                setSnackBarState({ ...snackBarState, open: false })
              }
              severity={snackBarState.severity}
              autoHideDuration={10000}
            />
          )}
          <PageHeaderContainer
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <IconButton
                onClick={handleBackButtonClick}
                data-testid="backBtn-testId"
              >
                <ArrowLeftIcon width="32" height="27" viewBox="0 0 32 27" />
              </IconButton>
              <Typography variant="h5">
                {t('features.account.details.pageTitle')}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex'
              }}
            >
              <IconButton
                onClick={() => router.push('/')}
                data-testid="closeBtn-testId"
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </PageHeaderContainer>
          <PageAreaBox
            pageAreaBoxName="account"
            sx={{
              height: '100%',
              bgcolor: 'background.paper',
              maxWidth: '1460px',
              padding: `0px 32px 0px 32px`,
              mt: '88px'
            }}
          >
            <Loading
              isLoading={isSearchAccountCdlFetching || isProfileLoading!}
              fallbackContainerProps={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <AccountDetailsContainer
                siteData={siteDetail}
                serviceContactEmail={serviceContactEmail}
                serviceContactProfile={serviceContactProfile}
                fetchingServiceContactEmail={isGetSiteShipToMetaDataFetching}
                fetchingServiceContactProfile={isGetCustomerFetching}
              />
              <MainButtonContainer
                onCustomerLookupClick={() => router.push('/customer')}
                onStartRepairClick={() =>
                  handleNewServiceRequestClick(
                    `/serviceOrder/repair?aid=${siteNumber}&spc=${siteCityStateProvinceOrPostalCode}`
                  )
                }
                onStartSalesOrderClick={handleStartSalesOrderClick}
                onRealtimeServiceClick={() =>
                  handleNewServiceRequestClick(
                    `/serviceOrder/repair?realtime=true&aid=${siteNumber}&spc=${siteCityStateProvinceOrPostalCode}`
                  )
                }
                onStartToteOrderClick={() =>
                  handleNewServiceRequestClick(
                    `/serviceOrder/repair?aid=${siteNumber}&spc=${siteCityStateProvinceOrPostalCode}&isToteOrder=true`
                  )
                }
                roleActions={roleActions}
                isAccountPage={true}
                isBranchSelected={false}
                isHubSelected={false}
              />

              <OrderHistory
                siteData={siteDetail}
                dataTestId={'orderHistoryTab-testId'}
                onProcessMultipleOrderClick={handleMultiToolProcess}
                page={1}
                onProcessOrderClick={() => {}}
                groupsResponse={serviceOrderGroups}
                headerLabel={t('features.account.details.transactionHistory')}
                displayOrigin={OrderListDisplayOrigin.OrderHistoryAccountDetail}
              />
            </Loading>
          </PageAreaBox>
        </Box>
      )}
    </>
  );
}
