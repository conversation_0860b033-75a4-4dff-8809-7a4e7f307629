'use client';

import { useRoleActions } from '@/auth';
import { CloseIcon, PlusIcon } from '@/design/atoms';
import { <PERSON><PERSON>, PageHeaderContainer, Snackbar } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { selectBranch } from '@/features/branch';
import { ProfileFormModal } from '@/features/customer';
import { selectHub } from '@/features/hub';
import { SearchCustomerContainer } from '@/features/search';
import { useAppSelector } from '@/store/hooks';
import { SnackbarState } from '@/util';
import { Box, IconButton, Typography } from '@mui/material';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function CustomerSearchPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const { canCreateCustomer, hasBranchSelection } = useRoleActions();
  const [openProfileFormModal, setProfileFormModalOpen] = useState(false);
  const [isCustomerNotFound, setIsCustomerNotFound] = useState(false);
  const [isAccountNotFound, setIsAccountNotFound] = useState(false);
  const selectedBranch = useAppSelector(selectBranch);
  const selectedHub = useAppSelector(selectHub);

  const handleProfileModalChange = (value: boolean) => {
    setProfileFormModalOpen(value);
  };

  const [snackBarState, setSnackBarState] = useState<SnackbarState>({
    open: false,
    message: t('features.customer.profileForm.userNotFound'),
    severity: 'error'
  });

  const handleCustomerNotFound = (value: boolean) => {
    setIsCustomerNotFound(value);
  };

  const handleCustomeSelectedrNotFound = () => {
    setSnackBarState((prevState) => ({
      ...prevState,
      open: true
    }));
  };

  const handleAccountNotFound = (value: boolean) => {
    setIsAccountNotFound(value);
  };

  return (
    <>
      <PageHeaderContainer
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="h5" color="neutral.800" textAlign="center">
            {t('features.customer.lookup')}
          </Typography>
        </Box>
        <IconButton
          onClick={() => router.push('/')}
          data-testid="closeBtn-testId"
        >
          <CloseIcon />
        </IconButton>
      </PageHeaderContainer>
      <PageAreaBox
        sx={{ mt: 6, height: '100%', bgcolor: 'background.paper', pt: '36px' }}
        pageAreaBoxName="CustomerLookup"
      >
        <Snackbar
          open={snackBarState.open}
          message={snackBarState.message}
          handleClose={() =>
            setSnackBarState({ ...snackBarState, open: false })
          }
          severity={snackBarState.severity}
        />
        <ProfileFormModal
          open={openProfileFormModal}
          onFormChange={handleProfileModalChange}
          disableNonEditableFields={false}
          saveButtonText={t('common.saveAndView')}
        />
        <Box>
          <SearchCustomerContainer
            isViewMode={true}
            onCustomerNotFound={handleCustomerNotFound}
            isCustomerNotFound={isCustomerNotFound}
            selectedBranch={selectedBranch}
            selectedHub={selectedHub}
            onCustomerSelectedNotFound={handleCustomeSelectedrNotFound}
            isAccountNotFound={isAccountNotFound}
            onAccountNotFound={handleAccountNotFound}
          />
          {canCreateCustomer && isCustomerNotFound && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'end',
                mt: '16px',
                justifyContent: 'flex-end'
              }}
            >
              <Button
                data-testid="createProfileButtonRepair-testid"
                size="medium"
                variant="secondary"
                startIcon={<PlusIcon viewBox="0 0 16 16" />}
                onClick={() => setProfileFormModalOpen(true)}
                disabled={!hasBranchSelection}
              >
                {t('features.home.mainButtons.createProfile')}
              </Button>
            </Box>
          )}
        </Box>
      </PageAreaBox>
    </>
  );
}
