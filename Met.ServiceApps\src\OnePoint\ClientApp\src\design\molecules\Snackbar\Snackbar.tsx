import { ApiErrorIcon } from '@/design/atoms';
import {
  Alert,
  AlertColor,
  AlertTitle,
  Snackbar as MUISnackbar,
  SxProps,
  Typography
} from '@mui/material';
import { SnackbarOrigin } from '@mui/material/Snackbar';

interface Props {
  open: boolean;
  message: string;
  severity: AlertColor;
  autoHideDuration?: number | null;
  isApiError?: boolean;
  position?: SnackbarOrigin;
  handleClose?: () => void;
  sx?: SxProps;
}

export const Snackbar = (props: Props) => {
  const {
    autoHideDuration = 5000,
    isApiError = false,
    position = {
      vertical: 'top',
      horizontal: 'center'
    },
    sx
  } = props;

  return (
    <MUISnackbar
      anchorOrigin={position}
      open={props.open}
      onClose={props.handleClose}
      key={props.message}
      autoHideDuration={autoHideDuration}
    >
      {isApiError ? (
        <Alert
          onClose={props.handleClose}
          severity={props.severity}
          icon={<ApiErrorIcon sx={{ color: 'error.800' }} />}
        >
          <AlertTitle>
            <Typography variant="p3" color={'error.800'} fontWeight={'600'}>
              {props.message}
            </Typography>
          </AlertTitle>
        </Alert>
      ) : sx ? (
        <Alert onClose={props.handleClose} severity={props.severity}>
          <AlertTitle>
            <Typography variant="p3" sx={sx}>
              {props.message}
            </Typography>
          </AlertTitle>
        </Alert>
      ) : (
        <Alert onClose={props.handleClose} severity={props.severity}>
          {props.message}
        </Alert>
      )}
    </MUISnackbar>
  );
};
