import { BREAKPOINTS } from '@/design/atoms';
import { Button, SelectDefaultOption } from '@/design/molecules';
import { AccountParams, AccountSearchList, SiteItem } from '@/features/account';
import { Location } from '@/features/branch';
import { CustomerSearchList, CustomerSearchResult } from '@/features/customer';
import { Hub } from '@/features/hub';
import {
  AccountSearch,
  accountSearchSchema,
  AccountSearchSchemaType,
  ClearTextFieldIconButton,
  CustomerSearchBy,
  customerSearchByFields,
  FieldValues,
  PagedListDetails,
  ProfileSearchBy,
  SearchBox,
  SearchBoxMultiple
} from '@/features/search';
import { useAppSelector } from '@/store/hooks';
import { formatPhoneNumber, matchNonDigit } from '@/util';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  InputAdornment,
  TextField,
  Theme,
  useMediaQuery
} from '@mui/material';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'next-export-i18n';
import { useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

interface Props {
  isFetching: boolean;
  customers: CustomerSearchResult[] | undefined;
  onSearchBoxChange: (
    value: string,
    values: string[] | undefined,
    customerSearchBy: CustomerSearchBy
  ) => void;
  onCustomerSelected: (id: string) => void;
  paginationData: PagedListDetails;
  isViewMode: boolean;
  isListOpened: boolean;
  valueNotFound: boolean;
  customerSearchBy: ProfileSearchBy;
  onLoadMoreClick: () => void;
  selectedBranch?: Location;
  selectedHub?: Hub;
  isListOpenedAccountList: boolean;
  onAccountSearchBoxChange: (searchText: string, param: AccountParams) => void;
  isAccountSearchFetching: boolean;
  sites: SiteItem[] | undefined;
  onLoadMoreAccountsClick: () => void;
  paginationAccountData: PagedListDetails;
  accountNotFound?: boolean;
  onAccountSelected: (siteNumber: string, siteItem: SiteItem) => void;
  hasTooManySitesResults?: boolean;
  setCustomerSearchBy: React.Dispatch<React.SetStateAction<ProfileSearchBy>>;
  onCustomerSearchBoxChange: (
    emailValue?: string,
    phoneValue?: string,
    firstNameValue?: string,
    lastNameValue?: string
  ) => void;
  allowGuest?: boolean;
  onContinueAsGuestClick?: () => void;
  isAdvancedFiltersOpen: boolean;
  setAdvancedFiltersOpen: () => void;
}

export const SearchCustomer = (props: Props) => {
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  const [searchText, setSearchText] = React.useState('');
  const [searchValues, setSearchValues] = React.useState<FieldValues>({
    firstName: { value: '', error: false },
    lastName: { value: '', error: false }
  });

  const {
    control,
    reset,
    resetField,
    register,
    setValue,
    getValues,
    formState,
    getFieldState
  } = useForm<AccountSearchSchemaType>({
    resolver: zodResolver(accountSearchSchema),
    mode: 'onChange',
    defaultValues: {
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    }
  });

  const [activeSearchBox, setActiveSearchBox] =
    useState<CustomerSearchBy | null>(null);

  const searchByAccountParams = React.useMemo(
    () => ({
      siteNameOrNumber: searchParams.get('st') ?? '',
      stateOrProvince: searchParams.get('sp') ?? 'All',
      parentAccountName: searchParams.get('pa') ?? '',
      city: searchParams.get('c') ?? '',
      postalCode: searchParams.get('pc') ?? ''
    }),
    [searchParams]
  );
  const searchByCustomerParams = React.useMemo(
    () => ({
      email: searchParams.get('e') ?? '',
      phone: searchParams.get('p') ?? '',
      firstName: searchParams.get('fn') ?? '',
      lastName: searchParams.get('ln') ?? ''
    }),
    [searchParams]
  );

  const [searchByCustomerLoaded, setSearchByCustomerLoaded] =
    React.useState(false);
  const {
    onSearchBoxChange,
    onAccountSearchBoxChange,
    isAccountSearchFetching,
    sites,
    setCustomerSearchBy,
    onCustomerSearchBoxChange,
    accountNotFound,
    customerSearchBy,
    setAdvancedFiltersOpen
  } = props;

  const isSearchingProfile = useAppSelector(
    (state) => state.accountPage.showProfileSearch
  );
  const isDeskTopScreen = useMediaQuery((theme: Theme) =>
    theme.breakpoints.up(BREAKPOINTS.values.xl)
  );

  const handleAccountSearch = useCallback(
    (accountSearchText: string, params?: AccountParams) => {
      const accountSearchFormValues = getValues();
      const accountSearchParams = {
        siteCity: accountSearchFormValues.city,
        sitePostalCode: accountSearchFormValues.postalCode,
        siteParentAccountName: accountSearchFormValues.parentAccountName,
        siteState: accountSearchFormValues.stateOrProvince
      };

      onAccountSearchBoxChange(accountSearchText, {
        ...accountSearchParams,
        ...params
      });
    },
    [onAccountSearchBoxChange, getValues]
  );

  const searchisValidSearch = getFieldState('accountSearchText');
  const disableApplyFiltersButton =
    !formState.isDirty || searchisValidSearch.invalid;

  useEffect(() => {
    const searchByAccountParams = getValues();

    reset({
      ...searchByAccountParams,
      parentAccountName: '',
      city: '',
      postalCode: ''
    });
  }, [props.isAdvancedFiltersOpen, reset, getValues]);

  React.useEffect(() => {
    setSearchText('');
    setSearchValues({
      firstName: { value: '', error: false },
      lastName: { value: '', error: false }
    });

    reset({
      accountSearchText: '',
      stateOrProvince: SelectDefaultOption.All,
      parentAccountName: '',
      city: '',
      postalCode: ''
    });
  }, [props.customerSearchBy, reset]);

  useEffect(() => {
    if (customerSearchBy !== ProfileSearchBy.AccountInfo) return;
    const isAdvancedFiltersUpdated =
      searchByAccountParams.parentAccountName ||
      searchByAccountParams.city ||
      searchByAccountParams.postalCode;

    if (
      !isAccountSearchFetching &&
      sites?.length === 0 &&
      !isSearchingProfile &&
      !accountNotFound &&
      searchByAccountParams.siteNameOrNumber
    ) {
      const setDelay = 500;

      setTimeout(() => {
        setValue('accountSearchText', searchByAccountParams.siteNameOrNumber);
        setValue('stateOrProvince', searchByAccountParams.stateOrProvince);
        setValue('city', searchByAccountParams.city);
        setValue('parentAccountName', searchByAccountParams.parentAccountName);
        setValue('postalCode', searchByAccountParams.postalCode);

        handleAccountSearch(searchByAccountParams.siteNameOrNumber, {
          siteState: searchByAccountParams.stateOrProvince!,
          siteCity: searchByAccountParams.city!,
          siteParentAccountName: searchByAccountParams.parentAccountName!,
          sitePostalCode: searchByAccountParams.postalCode!
        });

        if (isAdvancedFiltersUpdated) {
          setAdvancedFiltersOpen();
        }
      }, setDelay);
    }
  }, [
    searchByAccountParams.siteNameOrNumber,
    searchByAccountParams.city,
    searchByAccountParams.parentAccountName,
    searchByAccountParams.postalCode,
    searchByAccountParams.stateOrProvince,
    handleAccountSearch,
    setValue,
    isAccountSearchFetching,
    sites,
    isSearchingProfile,
    customerSearchBy,
    accountNotFound,
    setAdvancedFiltersOpen
  ]);

  const handleOnMultipleFieldChange = useCallback(
    (values: FieldValues) => {
      const hasValue = Object.values(values).some(
        (field) => field.value.trim() !== ''
      );
      setActiveSearchBox(hasValue ? CustomerSearchBy.CustomerName : null);

      const hasIncompletesValue = Object.values(values).some(
        (field) => field.value.trim() === ''
      );
      if (hasIncompletesValue) {
        onSearchBoxChange('', undefined, CustomerSearchBy.CustomerName);
      }
    },
    [onSearchBoxChange]
  );

  useEffect(() => {
    if (props.customerSearchBy !== ProfileSearchBy.CustomerInfo) return;
    if (
      (searchByCustomerParams.email ||
        searchByCustomerParams.phone ||
        (searchByCustomerParams.firstName &&
          searchByCustomerParams.lastName)) &&
      !searchByCustomerLoaded
    ) {
      const setDelay = 500;
      setCustomerSearchBy(ProfileSearchBy.CustomerInfo);
      if (searchByCustomerParams.email) {
        const email = searchByCustomerParams.email;
        setTimeout(() => {
          setSearchText(email);
          onCustomerSearchBoxChange(email, undefined, undefined, undefined);
          setSearchByCustomerLoaded(true);
        }, setDelay);
        return;
      }
      if (searchByCustomerParams.phone) {
        const phone = searchByCustomerParams.phone;
        setTimeout(() => {
          setSearchText(phone);
          onCustomerSearchBoxChange(undefined, phone, undefined, undefined);
          setSearchByCustomerLoaded(true);
        }, setDelay);
        return;
      }
      if (searchByCustomerParams.firstName && searchByCustomerParams.lastName) {
        const firstName = searchByCustomerParams.firstName;
        const lastName = searchByCustomerParams.lastName;

        const searchObj = {
          firstName: { value: firstName, error: false },
          lastName: { value: lastName, error: false }
        };

        setTimeout(() => {
          setSearchValues(searchObj);
          onCustomerSearchBoxChange(undefined, undefined, firstName, lastName);
          setSearchByCustomerLoaded(true);
          handleOnMultipleFieldChange(searchObj);
        }, setDelay);
      }
    }
  }, [
    props.customerSearchBy,
    searchByCustomerParams,
    searchByCustomerLoaded,
    setCustomerSearchBy,
    onCustomerSearchBoxChange,
    handleOnMultipleFieldChange
  ]);

  const onMultipleSearchBoxChange = (fieldValues: FieldValues) => {
    const searchValues = Object.values(fieldValues).map((x) =>
      x.value.replaceAll(' ', '')
    );

    const searchValue = searchValues.join(' ');
    props.onSearchBoxChange(
      searchValue,
      searchValues,
      CustomerSearchBy.CustomerName
    );
  };

  const handleCustomerTextChange = (text: string) => {
    setActiveSearchBox(text ? CustomerSearchBy.ContactInfo : null);

    let textChanged: string = text;

    if (
      textChanged.length >= 3 &&
      textChanged.at(0) !== '1' &&
      !matchNonDigit(textChanged)
    ) {
      textChanged = formatPhoneNumber(textChanged);
    }

    setSearchText(textChanged);
    if (textChanged.length === 0) {
      props.onSearchBoxChange(
        textChanged,
        undefined,
        CustomerSearchBy.ContactInfo
      );
    }
  };

  const handleOnSearchBoxChange = useCallback(
    (value: string) => {
      onSearchBoxChange(value, undefined, CustomerSearchBy.ContactInfo);
      setActiveSearchBox(value ? CustomerSearchBy.ContactInfo : null);
      if (!value) {
        setSearchText('');
      }
    },
    [onSearchBoxChange]
  );

  const handleOnSearchAccountBoxChange = useCallback(() => {
    const accountSearchFormValues = getValues();
    if (accountSearchFormValues.accountSearchText) {
      handleAccountSearch(accountSearchFormValues.accountSearchText);
    }
  }, [handleAccountSearch, getValues]);

  const handleApplyFilters = () => {
    const accountSearchFormValues = getValues();

    if (accountSearchFormValues.accountSearchText) {
      handleAccountSearch(accountSearchFormValues.accountSearchText);
    }
  };

  const handleReset = () => {
    reset();
  };

  const handleStateOrProvinceChange = (value: string) => {
    const accountSearchFormValues = getValues();

    if (accountSearchFormValues.accountSearchText) {
      handleAccountSearch(accountSearchFormValues.accountSearchText, {
        siteState: value
      });
    }
  };

  const displaySearchBox = () => {
    switch (props.customerSearchBy) {
      case ProfileSearchBy.AccountInfo: {
        return (
          <>
            <AccountSearch
              isFetching={isAccountSearchFetching}
              allowGuest={props.allowGuest}
              onSearch={handleOnSearchAccountBoxChange}
              handleContinueAsGuestClick={() => {
                props.onContinueAsGuestClick && props.onContinueAsGuestClick();
              }}
              onStateChange={handleStateOrProvinceChange}
              register={register}
              control={control}
              resetField={resetField}
            />
            <Accordion
              elevation={0}
              expanded={props.isAdvancedFiltersOpen}
              onChange={() => props.setAdvancedFiltersOpen()}
              data-testid="advancesFiltersAccordion-testId"
              sx={{
                ':before': {
                  height: 'unset'
                },
                '.MuiAccordionSummary-root': {
                  height: '0px',
                  minHeight: '0px'
                },
                '.MuiAccordionSummary-root.MuiButtonBase-root.Mui-expanded': {
                  height: '0px',
                  minHeight: '0px'
                },
                '.MuiAccordionDetails-root': {
                  padding: 'unset'
                }
              }}
            >
              <AccordionSummary />
              <AccordionDetails>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px'
                  }}
                >
                  <TextField
                    placeholder={t(
                      'features.customer.search.parentAccountNameOptional'
                    )}
                    data-testid={`parentAccountName-testId`}
                    {...register('parentAccountName')}
                    sx={{
                      backgroundColor: 'common.white',
                      height: '100%'
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Box
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              display: 'flex'
                            }}
                          >
                            <ClearTextFieldIconButton
                              control={control}
                              isFetching={props.isFetching}
                              resetField={resetField}
                              propName="parentAccountName"
                            />
                          </Box>
                        </InputAdornment>
                      )
                    }}
                  />
                  <Box
                    sx={{
                      display: 'flex',
                      gap: '16px'
                    }}
                  >
                    <TextField
                      placeholder={t('features.customer.search.cityOptional')}
                      data-testid={`city-testId`}
                      {...register('city')}
                      sx={{
                        backgroundColor: 'common.white',
                        height: '100%',
                        width: '50%',
                        flexGrow: 4
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Box
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                display: 'flex'
                              }}
                            >
                              <ClearTextFieldIconButton
                                control={control}
                                isFetching={props.isFetching}
                                resetField={resetField}
                                propName="city"
                              />
                            </Box>
                          </InputAdornment>
                        )
                      }}
                    />
                    <TextField
                      placeholder={t(
                        'features.customer.search.postalCodeOptional'
                      )}
                      data-testid={`postalCode-testId`}
                      {...register('postalCode')}
                      sx={{
                        backgroundColor: 'common.white',
                        height: '100%',
                        width: '50%',
                        flexGrow: 4
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Box
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                display: 'flex'
                              }}
                            >
                              <ClearTextFieldIconButton
                                control={control}
                                isFetching={props.isFetching}
                                resetField={resetField}
                                propName="postalCode"
                              />
                            </Box>
                          </InputAdornment>
                        )
                      }}
                    />
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      gap: '12px'
                    }}
                  >
                    {formState.isDirty && (
                      <Button
                        data-testid="resetAdvancedSearchButton-testid"
                        size="medium"
                        variant="secondary"
                        onClick={handleReset}
                      >
                        {t('features.search.advancedSearch.buttonReset')}
                      </Button>
                    )}

                    <Button
                      data-testid="applyFiltersAdvancedSearchButton-testid"
                      size="medium"
                      variant="primary"
                      onClick={handleApplyFilters}
                      disabled={disableApplyFiltersButton}
                    >
                      {t('features.search.advancedSearch.buttonApplyFilters')}
                    </Button>
                  </Box>
                </Box>
              </AccordionDetails>
            </Accordion>
          </>
        );
      }
      case ProfileSearchBy.CustomerInfo: {
        return (
          <>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'flex-end',
                gap: '16px',
                alignSelf: 'stretch'
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  position: 'relative'
                }}
              >
                <SearchBox
                  criteria="customer"
                  isFetching={props.isFetching}
                  onSearch={handleOnSearchBoxChange}
                  searchText={searchText}
                  onSearchTextChange={handleCustomerTextChange}
                  isDisabled={
                    activeSearchBox !== CustomerSearchBy.ContactInfo &&
                    activeSearchBox !== null
                  }
                />
                {activeSearchBox === CustomerSearchBy.ContactInfo &&
                  searchText &&
                  (props.isFetching ||
                    props.valueNotFound ||
                    (props.customers?.length ?? 0) > 0) && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%',
                        position: 'absolute',
                        top: '100%',
                        zIndex: 10,
                        backgroundColor: 'white',
                        boxShadow: 3
                      }}
                    >
                      {displayCustomerSearchList()}
                    </Box>
                  )}
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  height: '24px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignSelf: 'stretch'
                }}
              >
                <Divider sx={{ flexGrow: 1 }} />
                <Typography sx={{ mx: 2, color: 'neutral.400' }}>
                  {' '}
                  {t('features.customer.search.orSeparator')}
                </Typography>
                <Divider sx={{ flexGrow: 1 }} />
              </Box>
              <Box sx={{ width: '100%' }}>
                <SearchBoxMultiple
                  onSearch={onMultipleSearchBoxChange}
                  fieldList={customerSearchByFields}
                  onFieldChange={handleOnMultipleFieldChange}
                  isDisabledAll={
                    activeSearchBox !== CustomerSearchBy.CustomerName &&
                    activeSearchBox !== null
                  }
                  searchValues={searchValues}
                />
                {activeSearchBox === CustomerSearchBy.CustomerName &&
                  (props.isFetching ||
                    props.valueNotFound ||
                    (props.customers?.length ?? 0) > 0) && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%'
                      }}
                    >
                      {displayCustomerSearchList()}
                    </Box>
                  )}
              </Box>
              {props.allowGuest && (
                <Button
                  data-testid="customerSearch-btnContinueAsGuest-testid"
                  size="medium"
                  variant="secondary"
                  onClick={() => {
                    props.onContinueAsGuestClick &&
                      props.onContinueAsGuestClick();
                  }}
                  fullWidth={!isDeskTopScreen}
                >
                  {t('features.customer.search.continueAsGuest')}
                </Button>
              )}
            </Box>
          </>
        );
      }
    }
  };

  const displayCustomerSearchList = () => {
    return (
      <>
        <CustomerSearchList
          isViewMode={props.isViewMode}
          data-testid="customerList-testId"
          isFetching={props.isFetching}
          items={props.customers ?? []}
          onCustomerSelected={props.onCustomerSelected}
          isListOpened={props.isListOpened}
          valueNotFound={props.valueNotFound}
          paginationData={props.paginationData}
          onLoadMoreClick={props.onLoadMoreClick}
        />
      </>
    );
  };

  return (
    <Box>
      {displaySearchBox()}
      <AccountSearchList
        isViewMode={props.isViewMode}
        data-testid="accountList-testId"
        isFetching={isAccountSearchFetching}
        items={sites ?? []}
        isListOpened={props.isListOpenedAccountList}
        valueNotFound={props.accountNotFound ?? false}
        paginationData={props.paginationAccountData}
        onLoadMoreClick={props.onLoadMoreAccountsClick}
        onAccountSelected={props.onAccountSelected}
        hasTooManySitesResults={props.hasTooManySitesResults}
      />
    </Box>
  );
};
