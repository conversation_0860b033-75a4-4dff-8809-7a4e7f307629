import { BaseQueryFn, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { requestHeaders } from './requestHeaders';

export const removeLeadingForwardSlash = (text: string) =>
  text?.replace(/^\//, '');

export const fetchBaseQueryWrapper = ({
  path = '',
  baseUrl = '',
  extraOptions = {}
} = {}): BaseQueryFn =>
  fetchBaseQuery({
    baseUrl:
      typeof baseUrl === 'string'
        ? baseUrl
        : `api/v1${path && `/${removeLeadingForwardSlash(path)}`}`,
    prepareHeaders: (headers) => requestHeaders(headers),
    ...extraOptions
  });
