'use client';

import { getAccountName, useRoleActions } from '@/auth';
import { WarningNotificationIcon, maxWidthBP } from '@/design/atoms';
import { Loading, NavbarContainer } from '@/design/molecules';
import { PageAreaBox } from '@/design/organisms';
import { onProfileConfirmed } from '@/features/account';
import {
  BranchLink,
  SelectBranchModal,
  onPageToRedirectChange,
  selectBranch,
  setSelectedBranch,
  useGetAllLocationsDataQuery
} from '@/features/branch';
import { setSalesOrderCreationProfile } from '@/features/customer';
import { MainButtonContainer, WelcomeModal } from '@/features/home';
import {
  HubLink,
  SelectHubModal,
  selectHub,
  setSelectedHub
} from '@/features/hub';
import { SearchByContainer } from '@/features/search';
import { selectedOrderGroups } from '@/features/serviceOrder';
import { onSiteSelected } from '@/store/applicationContextSlice';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { getSession, getStorage, updateSession } from '@/store/local';
import { Alert, Divider, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function HomePage() {
  const dispatch = useAppDispatch();
  const selectedBranch = useAppSelector(selectBranch);
  const selectedHub = useAppSelector(selectHub);
  const savedOrderGroups = useAppSelector(selectedOrderGroups);
  const pageToRedirect = useAppSelector(
    (state) => state.branches.pageToRedirect
  );
  const roleActions = useRoleActions();
  const { hasBranchSelection, hasHubSelection } = roleActions;
  const [openWelcomeModal, setWelcomeModalOpen] = useState(false);
  const [openSelectBranchModal, setSelectBranchModalOpen] = useState(false);
  const [openSelectHubModal, setSelectHubModalOpen] = useState(false);
  const name = getAccountName();
  const modalName = name ? name.split(',')[1] || '' : '';
  const router = useRouter();
  const [isNotificationOpen, setNotificationOpen] = useState(
    savedOrderGroups.length > 0
  );
  const [isDoingRedirect, setIsDoingRedirect] = useState<boolean>(false);

  const { data: locations, isLoading: areLocationsLoading } =
    useGetAllLocationsDataQuery();

  const { t } = useTranslation();

  const handleWelcomeModelChange = (value: boolean) => {
    setWelcomeModalOpen(value);
    setSelectBranchModalOpen(!value);
  };

  const handleBranchLinkClick = () => {
    setSelectBranchModalOpen(true);
  };

  const handleHubLinkClick = () => {
    setSelectHubModalOpen(true);
  };

  useEffect(() => {
    if (!hasBranchSelection) {
      return;
    }

    const session = getSession();
    const storage = getStorage();

    if (session && session.selectedBranch) {
      dispatch(setSelectedBranch(session.selectedBranch));
      return;
    }

    if (storage && storage.selectedBranch) {
      updateSession({ selectedBranch: storage.selectedBranch });
      dispatch(setSelectedBranch(storage.selectedBranch));
      return;
    }

    setWelcomeModalOpen(true);
  }, [hasBranchSelection, dispatch]);

  useEffect(() => {
    if (!hasHubSelection) {
      return;
    }

    const session = getSession();
    const storage = getStorage();

    if (session && session.selectedHub) {
      dispatch(setSelectedHub(session.selectedHub));
      return;
    }

    if (storage && storage.selectedHub) {
      updateSession({ selectedHub: storage.selectedHub });
      dispatch(setSelectedHub(storage.selectedHub));
      return;
    }

    setSelectHubModalOpen(true);
  }, [hasHubSelection, dispatch]);

  useEffect(() => {
    dispatch(onSiteSelected(undefined));
  }, [dispatch]);

  const handleSavedOrdersClick = () => {
    router.push('/serviceOrder/savedorders');
  };

  const onLocationConfirm = () => {
    if (pageToRedirect) {
      setIsDoingRedirect(true);
      dispatch(onPageToRedirectChange(''));
      router.push(pageToRedirect);
    }
  };

  const getNotificationText = (): JSX.Element => {
    const preNotificationMessage = t('features.home.savedOrdersNotification1');
    const keyMessage = t('features.home.savedOrders');
    const postNotificationMessage = t('features.home.savedOrdersNotification2');
    return (
      <>
        {preNotificationMessage}{' '}
        <Typography
          variant="p3"
          sx={{
            color: 'primary.500',
            fontWeight: 600,
            textDecoration: 'underline',
            cursor: 'pointer'
          }}
          data-testid={'savedOrdersLink-testid'}
          onClick={handleSavedOrdersClick}
        >
          {keyMessage}
        </Typography>{' '}
        {postNotificationMessage}
      </>
    );
  };

  const onNewServiceRequestClick = () => {
    dispatch(onProfileConfirmed({}));
    router.push('serviceOrder/repair');
  };

  const handleOnStartSalesOrderClick = () => {
    if (
      !selectedBranch ||
      !selectedBranch.accounts ||
      selectedBranch.accounts.length === 0
    ) {
      throw new Error('No branch selected or branch has no accounts mapped.');
    }

    const account = selectedBranch.accounts.find((acc) => acc.taxable);

    if (!account) {
      throw new Error('No taxable account found for current selected branch.');
    }

    dispatch(
      setSalesOrderCreationProfile({
        accountNumber: account.accountNumber,
        isTaxExempt: false,
        isGuest: true,
        site: undefined
      })
    );
    router.push('salesOrder/create');
  };

  return (
    <>
      <NavbarContainer closeNotification={() => setNotificationOpen(false)} />
      <PageAreaBox pageAreaBoxName="Home">
        <Loading
          isLoading={isDoingRedirect}
          fallbackContainerProps={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Box
            sx={{
              pt: isNotificationOpen ? '0px' : { xs: '22px', sm: '36px' },
              maxWidth: maxWidthBP,
              px: { xs: 2, md: 4 }
            }}
          >
            {isNotificationOpen && (
              <Alert
                severity="warning"
                icon={
                  <WarningNotificationIcon
                    sx={{
                      color: 'neutral.800'
                    }}
                    viewBox="0 0 20 17"
                  />
                }
                onClose={() => setNotificationOpen(false)}
                sx={{
                  '& .MuiAlert-icon.MuiAlert-icon': {
                    color: 'neutral.800',
                    fontSize: '26px'
                  },
                  '& .MuiAlert-message': {
                    fontWeight: 700,
                    fontSize: '18px'
                  },
                  '& .MuiAlert-action': {
                    p: '0'
                  },
                  border: '1px solid',
                  borderColor: 'success.700',
                  backgroundColor: 'warning.100',
                  borderRadius: '0',
                  mb: '4px',
                  padding: '7px 32px',
                  alignItems: 'center'
                }}
              >
                {getNotificationText()}
              </Alert>
            )}
            {hasBranchSelection && (
              <>
                <WelcomeModal
                  open={openWelcomeModal}
                  onChange={handleWelcomeModelChange}
                  name={modalName}
                  data-testid="Welcome"
                ></WelcomeModal>
                <SelectBranchModal
                  open={openSelectBranchModal}
                  selectedBranch={selectedBranch}
                  onBranchSelection={(e) => dispatch(setSelectedBranch(e))}
                  onChange={setSelectBranchModalOpen}
                  onConfirm={onLocationConfirm}
                  data-testid="SelectBranch"
                  locationsQueryResult={{
                    locationData: locations,
                    isLoading: areLocationsLoading
                  }}
                />
                <Box
                  sx={{
                    mb: { xs: 3, sm: 4 },
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {t('features.branch.serviceBranch')}
                  </Typography>
                  <Divider
                    sx={{
                      color: 'neutral.400'
                    }}
                    orientation="vertical"
                    flexItem
                  />
                  <BranchLink
                    branchName={selectedBranch?.name ?? ''}
                    handleOnClick={handleBranchLinkClick}
                    data-testid="branchLink-testId"
                  />
                </Box>
              </>
            )}

            {hasHubSelection && (
              <>
                <SelectHubModal
                  open={openSelectHubModal}
                  selectedHub={selectedHub}
                  onHubSelection={(e) => dispatch(setSelectedHub(e))}
                  onChange={setSelectHubModalOpen}
                  onConfirm={onLocationConfirm}
                  data-testid="SelectHub"
                />
                <Box
                  sx={{
                    mb: { xs: 3, sm: 4 },
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {t('features.branch.serviceHub')}
                  </Typography>
                  <Divider
                    sx={{
                      color: 'neutral.400'
                    }}
                    orientation="vertical"
                    flexItem
                  />
                  <HubLink
                    hubName={selectedHub?.name ?? ''}
                    handleOnClick={handleHubLinkClick}
                    data-testid="hubLink-testId"
                  />
                </Box>
              </>
            )}

            <SearchByContainer
              locationsQueryResult={{
                locationData: locations,
                isLoading: areLocationsLoading
              }}
              roleActions={roleActions}
            />
            <MainButtonContainer
              onCustomerLookupClick={() => router.push('customer')}
              onStartRepairClick={onNewServiceRequestClick}
              onStartToteOrderClick={() =>
                router.push('serviceOrder/repair?isToteOrder=true')
              }
              onRealtimeServiceClick={() =>
                router.push('serviceOrder/repair?realtime=true')
              }
              onStartSalesOrderClick={handleOnStartSalesOrderClick}
              startSalesOrderBtnText="features.home.mainButtons.guestCheckout"
              roleActions={roleActions}
              isAccountPage={false}
              isBranchSelected={hasBranchSelection}
              isHubSelected={hasHubSelection}
            />
          </Box>
        </Loading>
      </PageAreaBox>
    </>
  );
}
